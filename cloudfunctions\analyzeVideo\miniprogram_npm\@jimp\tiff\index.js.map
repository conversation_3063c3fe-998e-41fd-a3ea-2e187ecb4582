{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _utif = _interopRequireDefault(require(\"utif\"));\n\nvar MIME_TYPE = 'image/tiff';\n\nvar _default = function _default() {\n  return {\n    mime: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, ['tiff', 'tif']),\n    constants: {\n      MIME_TIFF: MIME_TYPE\n    },\n    decoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (data) {\n      var ifds = _utif[\"default\"].decode(data);\n\n      var page = ifds[0];\n\n      _utif[\"default\"].decodeImages(data, ifds);\n\n      var rgba = _utif[\"default\"].toRGBA8(page);\n\n      return {\n        data: Buffer.from(rgba),\n        width: page.t256[0],\n        height: page.t257[0]\n      };\n    }),\n    encoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (image) {\n      var tiff = _utif[\"default\"].encodeImage(image.bitmap.data, image.bitmap.width, image.bitmap.height);\n\n      return Buffer.from(tiff);\n    })\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}