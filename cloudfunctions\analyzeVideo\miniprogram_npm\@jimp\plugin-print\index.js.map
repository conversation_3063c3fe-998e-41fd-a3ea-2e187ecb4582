{"version": 3, "sources": ["index.js", "measure-text.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _path = _interopRequireDefault(require(\"path\"));\n\nvar _loadBmfont = _interopRequireDefault(require(\"load-bmfont\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _measureText = require(\"./measure-text\");\n\nfunction xOffsetBasedOnAlignment(constants, font, line, maxWidth, alignment) {\n  if (alignment === constants.HORIZONTAL_ALIGN_LEFT) {\n    return 0;\n  }\n\n  if (alignment === constants.HORIZONTAL_ALIGN_CENTER) {\n    return (maxWidth - (0, _measureText.measureText)(font, line)) / 2;\n  }\n\n  return maxWidth - (0, _measureText.measureText)(font, line);\n}\n\nfunction drawCharacter(image, font, x, y, _char) {\n  if (_char.width > 0 && _char.height > 0) {\n    var characterPage = font.pages[_char.page];\n    image.blit(characterPage, x + _char.xoffset, y + _char.yoffset, _char.x, _char.y, _char.width, _char.height);\n  }\n\n  return image;\n}\n\nfunction printText(font, x, y, text, defaultCharWidth) {\n  for (var i = 0; i < text.length; i++) {\n    var _char2 = void 0;\n\n    if (font.chars[text[i]]) {\n      _char2 = text[i];\n    } else if (/\\s/.test(text[i])) {\n      _char2 = '';\n    } else {\n      _char2 = '?';\n    }\n\n    var fontChar = font.chars[_char2] || {};\n    var fontKerning = font.kernings[_char2];\n    drawCharacter(this, font, x, y, fontChar || {});\n    var kerning = fontKerning && fontKerning[text[i + 1]] ? fontKerning[text[i + 1]] : 0;\n    x += kerning + (fontChar.xadvance || defaultCharWidth);\n  }\n}\n\nfunction splitLines(font, text, maxWidth) {\n  var words = text.split(' ');\n  var lines = [];\n  var currentLine = [];\n  var longestLine = 0;\n  words.forEach(function (word) {\n    var line = [].concat((0, _toConsumableArray2[\"default\"])(currentLine), [word]).join(' ');\n    var length = (0, _measureText.measureText)(font, line);\n\n    if (length <= maxWidth) {\n      if (length > longestLine) {\n        longestLine = length;\n      }\n\n      currentLine.push(word);\n    } else {\n      lines.push(currentLine);\n      currentLine = [word];\n    }\n  });\n  lines.push(currentLine);\n  return {\n    lines: lines,\n    longestLine: longestLine\n  };\n}\n\nfunction loadPages(Jimp, dir, pages) {\n  var newPages = pages.map(function (page) {\n    return Jimp.read(dir + '/' + page);\n  });\n  return Promise.all(newPages);\n}\n\nvar dir = process.env.DIRNAME || \"\".concat(__dirname, \"/../\");\n\nvar _default = function _default() {\n  return {\n    constants: {\n      measureText: _measureText.measureText,\n      measureTextHeight: _measureText.measureTextHeight,\n      FONT_SANS_8_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt'),\n      FONT_SANS_10_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt'),\n      FONT_SANS_12_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt'),\n      FONT_SANS_14_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt'),\n      FONT_SANS_16_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt'),\n      FONT_SANS_32_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt'),\n      FONT_SANS_64_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt'),\n      FONT_SANS_128_BLACK: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt'),\n      FONT_SANS_8_WHITE: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt'),\n      FONT_SANS_16_WHITE: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt'),\n      FONT_SANS_32_WHITE: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt'),\n      FONT_SANS_64_WHITE: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt'),\n      FONT_SANS_128_WHITE: _path[\"default\"].join(dir, 'fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt'),\n\n      /**\n       * Loads a bitmap font from a file\n       * @param {string} file the file path of a .fnt file\n       * @param {function(Error, Jimp)} cb (optional) a function to call when the font is loaded\n       * @returns {Promise} a promise\n       */\n      loadFont: function loadFont(file, cb) {\n        var _this = this;\n\n        if (typeof file !== 'string') return _utils.throwError.call(this, 'file must be a string', cb);\n        return new Promise(function (resolve, reject) {\n          cb = cb || function (err, font) {\n            if (err) reject(err);else resolve(font);\n          };\n\n          (0, _loadBmfont[\"default\"])(file, function (err, font) {\n            var chars = {};\n            var kernings = {};\n\n            if (err) {\n              return _utils.throwError.call(_this, err, cb);\n            }\n\n            for (var i = 0; i < font.chars.length; i++) {\n              chars[String.fromCharCode(font.chars[i].id)] = font.chars[i];\n            }\n\n            for (var _i = 0; _i < font.kernings.length; _i++) {\n              var firstString = String.fromCharCode(font.kernings[_i].first);\n              kernings[firstString] = kernings[firstString] || {};\n              kernings[firstString][String.fromCharCode(font.kernings[_i].second)] = font.kernings[_i].amount;\n            }\n\n            loadPages(_this, _path[\"default\"].dirname(file), font.pages).then(function (pages) {\n              cb(null, {\n                chars: chars,\n                kernings: kernings,\n                pages: pages,\n                common: font.common,\n                info: font.info\n              });\n            });\n          });\n        });\n      }\n    },\n    \"class\": {\n      /**\n       * Draws a text on a image on a given boundary\n       * @param {Jimp} font a bitmap font loaded from `Jimp.loadFont` command\n       * @param {number} x the x position to start drawing the text\n       * @param {number} y the y position to start drawing the text\n       * @param {any} text the text to draw (string or object with `text`, `alignmentX`, and/or `alignmentY`)\n       * @param {number} maxWidth (optional) the boundary width to draw in\n       * @param {number} maxHeight (optional) the boundary height to draw in\n       * @param {function(Error, Jimp)} cb (optional) a function to call when the text is written\n       * @returns {Jimp} this for chaining of methods\n       */\n      print: function print(font, x, y, text, maxWidth, maxHeight, cb) {\n        var _this2 = this;\n\n        if (typeof maxWidth === 'function' && typeof cb === 'undefined') {\n          cb = maxWidth;\n          maxWidth = Infinity;\n        }\n\n        if (typeof maxWidth === 'undefined') {\n          maxWidth = Infinity;\n        }\n\n        if (typeof maxHeight === 'function' && typeof cb === 'undefined') {\n          cb = maxHeight;\n          maxHeight = Infinity;\n        }\n\n        if (typeof maxHeight === 'undefined') {\n          maxHeight = Infinity;\n        }\n\n        if ((0, _typeof2[\"default\"])(font) !== 'object') {\n          return _utils.throwError.call(this, 'font must be a Jimp loadFont', cb);\n        }\n\n        if (typeof x !== 'number' || typeof y !== 'number' || typeof maxWidth !== 'number') {\n          return _utils.throwError.call(this, 'x, y and maxWidth must be numbers', cb);\n        }\n\n        if (typeof maxWidth !== 'number') {\n          return _utils.throwError.call(this, 'maxWidth must be a number', cb);\n        }\n\n        if (typeof maxHeight !== 'number') {\n          return _utils.throwError.call(this, 'maxHeight must be a number', cb);\n        }\n\n        var alignmentX;\n        var alignmentY;\n\n        if ((0, _typeof2[\"default\"])(text) === 'object' && text.text !== null && text.text !== undefined) {\n          alignmentX = text.alignmentX || this.constructor.HORIZONTAL_ALIGN_LEFT;\n          alignmentY = text.alignmentY || this.constructor.VERTICAL_ALIGN_TOP;\n          var _text = text;\n          text = _text.text;\n        } else {\n          alignmentX = this.constructor.HORIZONTAL_ALIGN_LEFT;\n          alignmentY = this.constructor.VERTICAL_ALIGN_TOP;\n          text = text.toString();\n        }\n\n        if (maxHeight !== Infinity && alignmentY === this.constructor.VERTICAL_ALIGN_BOTTOM) {\n          y += maxHeight - (0, _measureText.measureTextHeight)(font, text, maxWidth);\n        } else if (maxHeight !== Infinity && alignmentY === this.constructor.VERTICAL_ALIGN_MIDDLE) {\n          y += maxHeight / 2 - (0, _measureText.measureTextHeight)(font, text, maxWidth) / 2;\n        }\n\n        var defaultCharWidth = Object.entries(font.chars)[0][1].xadvance;\n\n        var _splitLines = splitLines(font, text, maxWidth),\n            lines = _splitLines.lines,\n            longestLine = _splitLines.longestLine;\n\n        lines.forEach(function (line) {\n          var lineString = line.join(' ');\n          var alignmentWidth = xOffsetBasedOnAlignment(_this2.constructor, font, lineString, maxWidth, alignmentX);\n          printText.call(_this2, font, x + alignmentWidth, y, lineString, defaultCharWidth);\n          y += font.common.lineHeight;\n        });\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this, {\n            x: x + longestLine,\n            y: y\n          });\n        }\n\n        return this;\n      }\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.measureText = measureText;\nexports.measureTextHeight = measureTextHeight;\n\nfunction measureText(font, text) {\n  var x = 0;\n\n  for (var i = 0; i < text.length; i++) {\n    if (font.chars[text[i]]) {\n      var kerning = font.kernings[text[i]] && font.kernings[text[i]][text[i + 1]] ? font.kernings[text[i]][text[i + 1]] : 0;\n      x += (font.chars[text[i]].xadvance || 0) + kerning;\n    }\n  }\n\n  return x;\n}\n\nfunction measureTextHeight(font, text, maxWidth) {\n  var words = text.split(' ');\n  var line = '';\n  var textTotalHeight = font.common.lineHeight;\n\n  for (var n = 0; n < words.length; n++) {\n    var testLine = line + words[n] + ' ';\n    var testWidth = measureText(font, testLine);\n\n    if (testWidth > maxWidth && n > 0) {\n      textTotalHeight += font.common.lineHeight;\n      line = words[n] + ' ';\n    } else {\n      line = testLine;\n    }\n  }\n\n  return textTotalHeight;\n}\n//# sourceMappingURL=measure-text.js.map"]}