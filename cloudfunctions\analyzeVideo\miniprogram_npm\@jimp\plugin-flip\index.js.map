{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Flip the image horizontally\n * @param {boolean} horizontal a Boolean, if true the image will be flipped horizontally\n * @param {boolean} vertical a Boolean, if true the image will be flipped vertically\n * @param {function(<PERSON>rro<PERSON>, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction flipFn(horizontal, vertical, cb) {\n  if (typeof horizontal !== 'boolean' || typeof vertical !== 'boolean') return _utils.throwError.call(this, 'horizontal and vertical must be Booleans', cb);\n  var bitmap = Buffer.alloc(this.bitmap.data.length);\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n    var _x = horizontal ? this.bitmap.width - 1 - x : x;\n\n    var _y = vertical ? this.bitmap.height - 1 - y : y;\n\n    var _idx = this.bitmap.width * _y + _x << 2;\n\n    var data = this.bitmap.data.readUInt32BE(idx);\n    bitmap.writeUInt32BE(data, _idx);\n  });\n  this.bitmap.data = Buffer.from(bitmap);\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nvar _default = function _default() {\n  return {\n    flip: flipFn,\n    mirror: flipFn\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}