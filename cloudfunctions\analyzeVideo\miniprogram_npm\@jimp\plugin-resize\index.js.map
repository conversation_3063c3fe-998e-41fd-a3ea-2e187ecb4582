{"version": 3, "sources": ["index.js", "modules/resize.js", "modules/resize2.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _resize = _interopRequireDefault(require(\"./modules/resize\"));\n\nvar _resize2 = _interopRequireDefault(require(\"./modules/resize2\"));\n\nvar _default = function _default() {\n  return {\n    constants: {\n      RESIZE_NEAREST_NEIGHBOR: 'nearestNeighbor',\n      RESIZE_BILINEAR: 'bilinearInterpolation',\n      RESIZE_BICUBIC: 'bicubicInterpolation',\n      RESIZE_HERMITE: 'hermiteInterpolation',\n      RESIZE_BEZIER: 'bezierInterpolation'\n    },\n    \"class\": {\n      /**\n       * Resizes the image to a set width and height using a 2-pass bilinear algorithm\n       * @param {number} w the width to resize the image to (or Jimp.AUTO)\n       * @param {number} h the height to resize the image to (or Jimp.AUTO)\n       * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      resize: function resize(w, h, mode, cb) {\n        if (typeof w !== 'number' || typeof h !== 'number') {\n          return _utils.throwError.call(this, 'w and h must be numbers', cb);\n        }\n\n        if (typeof mode === 'function' && typeof cb === 'undefined') {\n          cb = mode;\n          mode = null;\n        }\n\n        if (w === this.constructor.AUTO && h === this.constructor.AUTO) {\n          return _utils.throwError.call(this, 'w and h cannot both be set to auto', cb);\n        }\n\n        if (w === this.constructor.AUTO) {\n          w = this.bitmap.width * (h / this.bitmap.height);\n        }\n\n        if (h === this.constructor.AUTO) {\n          h = this.bitmap.height * (w / this.bitmap.width);\n        }\n\n        if (w < 0 || h < 0) {\n          return _utils.throwError.call(this, 'w and h must be positive numbers', cb);\n        } // round inputs\n\n\n        w = Math.round(w);\n        h = Math.round(h);\n\n        if (typeof _resize2[\"default\"][mode] === 'function') {\n          var dst = {\n            data: Buffer.alloc(w * h * 4),\n            width: w,\n            height: h\n          };\n\n          _resize2[\"default\"][mode](this.bitmap, dst);\n\n          this.bitmap = dst;\n        } else {\n          var image = this;\n          var resize = new _resize[\"default\"](this.bitmap.width, this.bitmap.height, w, h, true, true, function (buffer) {\n            image.bitmap.data = Buffer.from(buffer);\n            image.bitmap.width = w;\n            image.bitmap.height = h;\n          });\n          resize.resize(this.bitmap.data);\n        }\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      }\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map", "\n\n// JavaScript Image Resizer (c) 2012 - <PERSON>\n// Released to public domain 29 July 2013: https://github.com/grantgalitz/JS-Image-Resizer/issues/4\nfunction Resize(widthOriginal, heightOriginal, targetWidth, targetHeight, blendAlpha, interpolationPass, resizeCallback) {\n  this.widthOriginal = Math.abs(Math.floor(widthOriginal) || 0);\n  this.heightOriginal = Math.abs(Math.floor(heightOriginal) || 0);\n  this.targetWidth = Math.abs(Math.floor(targetWidth) || 0);\n  this.targetHeight = Math.abs(Math.floor(targetHeight) || 0);\n  this.colorChannels = blendAlpha ? 4 : 3;\n  this.interpolationPass = Boolean(interpolationPass);\n  this.resizeCallback = typeof resizeCallback === 'function' ? resizeCallback : function () {};\n  this.targetWidthMultipliedByChannels = this.targetWidth * this.colorChannels;\n  this.originalWidthMultipliedByChannels = this.widthOriginal * this.colorChannels;\n  this.originalHeightMultipliedByChannels = this.heightOriginal * this.colorChannels;\n  this.widthPassResultSize = this.targetWidthMultipliedByChannels * this.heightOriginal;\n  this.finalResultSize = this.targetWidthMultipliedByChannels * this.targetHeight;\n  this.initialize();\n}\n\nResize.prototype.initialize = function () {\n  // Perform some checks:\n  if (this.widthOriginal > 0 && this.heightOriginal > 0 && this.targetWidth > 0 && this.targetHeight > 0) {\n    this.configurePasses();\n  } else {\n    throw new Error('Invalid settings specified for the resizer.');\n  }\n};\n\nResize.prototype.configurePasses = function () {\n  if (this.widthOriginal === this.targetWidth) {\n    // Bypass the width resizer pass:\n    this.resizeWidth = this.bypassResizer;\n  } else {\n    // Setup the width resizer pass:\n    this.ratioWeightWidthPass = this.widthOriginal / this.targetWidth;\n\n    if (this.ratioWeightWidthPass < 1 && this.interpolationPass) {\n      this.initializeFirstPassBuffers(true);\n      this.resizeWidth = this.colorChannels === 4 ? this.resizeWidthInterpolatedRGBA : this.resizeWidthInterpolatedRGB;\n    } else {\n      this.initializeFirstPassBuffers(false);\n      this.resizeWidth = this.colorChannels === 4 ? this.resizeWidthRGBA : this.resizeWidthRGB;\n    }\n  }\n\n  if (this.heightOriginal === this.targetHeight) {\n    // Bypass the height resizer pass:\n    this.resizeHeight = this.bypassResizer;\n  } else {\n    // Setup the height resizer pass:\n    this.ratioWeightHeightPass = this.heightOriginal / this.targetHeight;\n\n    if (this.ratioWeightHeightPass < 1 && this.interpolationPass) {\n      this.initializeSecondPassBuffers(true);\n      this.resizeHeight = this.resizeHeightInterpolated;\n    } else {\n      this.initializeSecondPassBuffers(false);\n      this.resizeHeight = this.colorChannels === 4 ? this.resizeHeightRGBA : this.resizeHeightRGB;\n    }\n  }\n};\n\nResize.prototype._resizeWidthInterpolatedRGBChannels = function (buffer, fourthChannel) {\n  var channelsNum = fourthChannel ? 4 : 3;\n  var ratioWeight = this.ratioWeightWidthPass;\n  var outputBuffer = this.widthBuffer;\n  var weight = 0;\n  var finalOffset = 0;\n  var pixelOffset = 0;\n  var firstWeight = 0;\n  var secondWeight = 0;\n  var targetPosition; // Handle for only one interpolation input being valid for start calculation:\n\n  for (targetPosition = 0; weight < 1 / 3; targetPosition += channelsNum, weight += ratioWeight) {\n    for (finalOffset = targetPosition, pixelOffset = 0; finalOffset < this.widthPassResultSize; pixelOffset += this.originalWidthMultipliedByChannels, finalOffset += this.targetWidthMultipliedByChannels) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel) outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  } // Adjust for overshoot of the last pass's counter:\n\n\n  weight -= 1 / 3;\n  var interpolationWidthSourceReadStop;\n\n  for (interpolationWidthSourceReadStop = this.widthOriginal - 1; weight < interpolationWidthSourceReadStop; targetPosition += channelsNum, weight += ratioWeight) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight; // Interpolate:\n\n    for (finalOffset = targetPosition, pixelOffset = Math.floor(weight) * channelsNum; finalOffset < this.widthPassResultSize; pixelOffset += this.originalWidthMultipliedByChannels, finalOffset += this.targetWidthMultipliedByChannels) {\n      outputBuffer[finalOffset + 0] = buffer[pixelOffset + 0] * firstWeight + buffer[pixelOffset + channelsNum + 0] * secondWeight;\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1] * firstWeight + buffer[pixelOffset + channelsNum + 1] * secondWeight;\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2] * firstWeight + buffer[pixelOffset + channelsNum + 2] * secondWeight;\n      if (fourthChannel) outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3] * firstWeight + buffer[pixelOffset + channelsNum + 3] * secondWeight;\n    }\n  } // Handle for only one interpolation input being valid for end calculation:\n\n\n  for (interpolationWidthSourceReadStop = this.originalWidthMultipliedByChannels - channelsNum; targetPosition < this.targetWidthMultipliedByChannels; targetPosition += channelsNum) {\n    for (finalOffset = targetPosition, pixelOffset = interpolationWidthSourceReadStop; finalOffset < this.widthPassResultSize; pixelOffset += this.originalWidthMultipliedByChannels, finalOffset += this.targetWidthMultipliedByChannels) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel) outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeWidthRGBChannels = function (buffer, fourthChannel) {\n  var channelsNum = fourthChannel ? 4 : 3;\n  var ratioWeight = this.ratioWeightWidthPass;\n  var ratioWeightDivisor = 1 / ratioWeight;\n  var nextLineOffsetOriginalWidth = this.originalWidthMultipliedByChannels - channelsNum + 1;\n  var nextLineOffsetTargetWidth = this.targetWidthMultipliedByChannels - channelsNum + 1;\n  var output = this.outputWidthWorkBench;\n  var outputBuffer = this.widthBuffer;\n  var trustworthyColorsCount = this.outputWidthWorkBenchOpaquePixelsCount;\n  var weight = 0;\n  var amountToNext = 0;\n  var actualPosition = 0;\n  var currentPosition = 0;\n  var line = 0;\n  var pixelOffset = 0;\n  var outputOffset = 0;\n  var multiplier = 1;\n  var r = 0;\n  var g = 0;\n  var b = 0;\n  var a = 0;\n\n  do {\n    for (line = 0; line < this.originalHeightMultipliedByChannels;) {\n      output[line++] = 0;\n      output[line++] = 0;\n      output[line++] = 0;\n\n      if (fourthChannel) {\n        output[line++] = 0;\n        trustworthyColorsCount[line / channelsNum - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n\n      for (line = 0, pixelOffset = actualPosition; line < this.originalHeightMultipliedByChannels; pixelOffset += nextLineOffsetOriginalWidth) {\n        r = buffer[pixelOffset];\n        g = buffer[++pixelOffset];\n        b = buffer[++pixelOffset];\n        a = fourthChannel ? buffer[++pixelOffset] : 255; // Ignore RGB values if pixel is completely transparent\n\n        output[line++] += (a ? r : 0) * multiplier;\n        output[line++] += (a ? g : 0) * multiplier;\n        output[line++] += (a ? b : 0) * multiplier;\n\n        if (fourthChannel) {\n          output[line++] += a * multiplier;\n          trustworthyColorsCount[line / channelsNum - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition += channelsNum;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (weight > 0 && actualPosition < this.originalWidthMultipliedByChannels);\n\n    for (line = 0, pixelOffset = outputOffset; line < this.originalHeightMultipliedByChannels; pixelOffset += nextLineOffsetTargetWidth) {\n      weight = fourthChannel ? trustworthyColorsCount[line / channelsNum] : 1;\n      multiplier = fourthChannel ? weight ? 1 / weight : 0 : ratioWeightDivisor;\n      outputBuffer[pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      if (fourthChannel) outputBuffer[++pixelOffset] = output[line++] * ratioWeightDivisor;\n    }\n\n    outputOffset += channelsNum;\n  } while (outputOffset < this.targetWidthMultipliedByChannels);\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeHeightRGBChannels = function (buffer, fourthChannel) {\n  var ratioWeight = this.ratioWeightHeightPass;\n  var ratioWeightDivisor = 1 / ratioWeight;\n  var output = this.outputHeightWorkBench;\n  var outputBuffer = this.heightBuffer;\n  var trustworthyColorsCount = this.outputHeightWorkBenchOpaquePixelsCount;\n  var weight = 0;\n  var amountToNext = 0;\n  var actualPosition = 0;\n  var currentPosition = 0;\n  var pixelOffset = 0;\n  var outputOffset = 0;\n  var caret = 0;\n  var multiplier = 1;\n  var r = 0;\n  var g = 0;\n  var b = 0;\n  var a = 0;\n\n  do {\n    for (pixelOffset = 0; pixelOffset < this.targetWidthMultipliedByChannels;) {\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n\n      if (fourthChannel) {\n        output[pixelOffset++] = 0;\n        trustworthyColorsCount[pixelOffset / 4 - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n      caret = actualPosition;\n\n      for (pixelOffset = 0; pixelOffset < this.targetWidthMultipliedByChannels;) {\n        r = buffer[caret++];\n        g = buffer[caret++];\n        b = buffer[caret++];\n        a = fourthChannel ? buffer[caret++] : 255; // Ignore RGB values if pixel is completely transparent\n\n        output[pixelOffset++] += (a ? r : 0) * multiplier;\n        output[pixelOffset++] += (a ? g : 0) * multiplier;\n        output[pixelOffset++] += (a ? b : 0) * multiplier;\n\n        if (fourthChannel) {\n          output[pixelOffset++] += a * multiplier;\n          trustworthyColorsCount[pixelOffset / 4 - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition = caret;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (weight > 0 && actualPosition < this.widthPassResultSize);\n\n    for (pixelOffset = 0; pixelOffset < this.targetWidthMultipliedByChannels;) {\n      weight = fourthChannel ? trustworthyColorsCount[pixelOffset / 4] : 1;\n      multiplier = fourthChannel ? weight ? 1 / weight : 0 : ratioWeightDivisor;\n      outputBuffer[outputOffset++] = Math.round(output[pixelOffset++] * multiplier);\n      outputBuffer[outputOffset++] = Math.round(output[pixelOffset++] * multiplier);\n      outputBuffer[outputOffset++] = Math.round(output[pixelOffset++] * multiplier);\n\n      if (fourthChannel) {\n        outputBuffer[outputOffset++] = Math.round(output[pixelOffset++] * ratioWeightDivisor);\n      }\n    }\n  } while (outputOffset < this.finalResultSize);\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeWidthInterpolatedRGB = function (buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthInterpolatedRGBA = function (buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeWidthRGB = function (buffer) {\n  return this._resizeWidthRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthRGBA = function (buffer) {\n  return this._resizeWidthRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeHeightInterpolated = function (buffer) {\n  var ratioWeight = this.ratioWeightHeightPass;\n  var outputBuffer = this.heightBuffer;\n  var weight = 0;\n  var finalOffset = 0;\n  var pixelOffset = 0;\n  var pixelOffsetAccumulated = 0;\n  var pixelOffsetAccumulated2 = 0;\n  var firstWeight = 0;\n  var secondWeight = 0;\n  var interpolationHeightSourceReadStop; // Handle for only one interpolation input being valid for start calculation:\n\n  for (; weight < 1 / 3; weight += ratioWeight) {\n    for (pixelOffset = 0; pixelOffset < this.targetWidthMultipliedByChannels;) {\n      outputBuffer[finalOffset++] = Math.round(buffer[pixelOffset++]);\n    }\n  } // Adjust for overshoot of the last pass's counter:\n\n\n  weight -= 1 / 3;\n\n  for (interpolationHeightSourceReadStop = this.heightOriginal - 1; weight < interpolationHeightSourceReadStop; weight += ratioWeight) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight; // Interpolate:\n\n    pixelOffsetAccumulated = Math.floor(weight) * this.targetWidthMultipliedByChannels;\n    pixelOffsetAccumulated2 = pixelOffsetAccumulated + this.targetWidthMultipliedByChannels;\n\n    for (pixelOffset = 0; pixelOffset < this.targetWidthMultipliedByChannels; ++pixelOffset) {\n      outputBuffer[finalOffset++] = Math.round(buffer[pixelOffsetAccumulated++] * firstWeight + buffer[pixelOffsetAccumulated2++] * secondWeight);\n    }\n  } // Handle for only one interpolation input being valid for end calculation:\n\n\n  while (finalOffset < this.finalResultSize) {\n    for (pixelOffset = 0, pixelOffsetAccumulated = interpolationHeightSourceReadStop * this.targetWidthMultipliedByChannels; pixelOffset < this.targetWidthMultipliedByChannels; ++pixelOffset) {\n      outputBuffer[finalOffset++] = Math.round(buffer[pixelOffsetAccumulated++]);\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeHeightRGB = function (buffer) {\n  return this._resizeHeightRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeHeightRGBA = function (buffer) {\n  return this._resizeHeightRGBChannels(buffer, true);\n};\n\nResize.prototype.resize = function (buffer) {\n  this.resizeCallback(this.resizeHeight(this.resizeWidth(buffer)));\n};\n\nResize.prototype.bypassResizer = function (buffer) {\n  // Just return the buffer passed:\n  return buffer;\n};\n\nResize.prototype.initializeFirstPassBuffers = function (BILINEARAlgo) {\n  // Initialize the internal width pass buffers:\n  this.widthBuffer = this.generateFloatBuffer(this.widthPassResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputWidthWorkBench = this.generateFloatBuffer(this.originalHeightMultipliedByChannels);\n\n    if (this.colorChannels > 3) {\n      this.outputWidthWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(this.heightOriginal);\n    }\n  }\n};\n\nResize.prototype.initializeSecondPassBuffers = function (BILINEARAlgo) {\n  // Initialize the internal height pass buffers:\n  this.heightBuffer = this.generateUint8Buffer(this.finalResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputHeightWorkBench = this.generateFloatBuffer(this.targetWidthMultipliedByChannels);\n\n    if (this.colorChannels > 3) {\n      this.outputHeightWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(this.targetWidth);\n    }\n  }\n};\n\nResize.prototype.generateFloatBuffer = function (bufferLength) {\n  // Generate a float32 typed array buffer:\n  try {\n    return new Float32Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateFloat64Buffer = function (bufferLength) {\n  // Generate a float64 typed array buffer:\n  try {\n    return new Float64Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateUint8Buffer = function (bufferLength) {\n  // Generate a uint8 typed array buffer:\n  try {\n    return new Uint8Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nmodule.exports = Resize;\n//# sourceMappingURL=resize.js.map", "\n\n/**\n * Copyright (c) 2015 <PERSON><PERSON> Roche\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:</p>\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nmodule.exports = {\n  nearestNeighbor: function nearestNeighbor(src, dst) {\n    var wSrc = src.width;\n    var hSrc = src.height;\n    var wDst = dst.width;\n    var hDst = dst.height;\n    var bufSrc = src.data;\n    var bufDst = dst.data;\n\n    for (var i = 0; i < hDst; i++) {\n      for (var j = 0; j < wDst; j++) {\n        var posDst = (i * wDst + j) * 4;\n        var iSrc = Math.floor(i * hSrc / hDst);\n        var jSrc = Math.floor(j * wSrc / wDst);\n        var posSrc = (iSrc * wSrc + jSrc) * 4;\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n      }\n    }\n  },\n  bilinearInterpolation: function bilinearInterpolation(src, dst) {\n    var wSrc = src.width;\n    var hSrc = src.height;\n    var wDst = dst.width;\n    var hDst = dst.height;\n    var bufSrc = src.data;\n    var bufDst = dst.data;\n\n    var interpolate = function interpolate(k, kMin, vMin, kMax, vMax) {\n      // special case - k is integer\n      if (kMin === kMax) {\n        return vMin;\n      }\n\n      return Math.round((k - kMin) * vMax + (kMax - k) * vMin);\n    };\n\n    var assign = function assign(pos, offset, x, xMin, xMax, y, yMin, yMax) {\n      var posMin = (yMin * wSrc + xMin) * 4 + offset;\n      var posMax = (yMin * wSrc + xMax) * 4 + offset;\n      var vMin = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]); // special case, y is integer\n\n      if (yMax === yMin) {\n        bufDst[pos + offset] = vMin;\n      } else {\n        posMin = (yMax * wSrc + xMin) * 4 + offset;\n        posMax = (yMax * wSrc + xMax) * 4 + offset;\n        var vMax = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]);\n        bufDst[pos + offset] = interpolate(y, yMin, vMin, yMax, vMax);\n      }\n    };\n\n    for (var i = 0; i < hDst; i++) {\n      for (var j = 0; j < wDst; j++) {\n        var posDst = (i * wDst + j) * 4; // x & y in src coordinates\n\n        var x = j * wSrc / wDst;\n        var xMin = Math.floor(x);\n        var xMax = Math.min(Math.ceil(x), wSrc - 1);\n        var y = i * hSrc / hDst;\n        var yMin = Math.floor(y);\n        var yMax = Math.min(Math.ceil(y), hSrc - 1);\n        assign(posDst, 0, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 1, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 2, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 3, x, xMin, xMax, y, yMin, yMax);\n      }\n    }\n  },\n  _interpolate2D: function _interpolate2D(src, dst, options, interpolate) {\n    var bufSrc = src.data;\n    var bufDst = dst.data;\n    var wSrc = src.width;\n    var hSrc = src.height;\n    var wDst = dst.width;\n    var hDst = dst.height; // when dst smaller than src/2, interpolate first to a multiple between 0.5 and 1.0 src, then sum squares\n\n    var wM = Math.max(1, Math.floor(wSrc / wDst));\n    var wDst2 = wDst * wM;\n    var hM = Math.max(1, Math.floor(hSrc / hDst));\n    var hDst2 = hDst * hM; // ===========================================================\n    // Pass 1 - interpolate rows\n    // buf1 has width of dst2 and height of src\n\n    var buf1 = Buffer.alloc(wDst2 * hSrc * 4);\n\n    for (var i = 0; i < hSrc; i++) {\n      for (var j = 0; j < wDst2; j++) {\n        // i in src coords, j in dst coords\n        // calculate x in src coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (wSrc-1)/wDst2\n        var x = j * (wSrc - 1) / wDst2;\n        var xPos = Math.floor(x);\n        var t = x - xPos;\n        var srcPos = (i * wSrc + xPos) * 4;\n        var buf1Pos = (i * wDst2 + j) * 4;\n\n        for (var k = 0; k < 4; k++) {\n          var kPos = srcPos + k;\n          var x0 = xPos > 0 ? bufSrc[kPos - 4] : 2 * bufSrc[kPos] - bufSrc[kPos + 4];\n          var x1 = bufSrc[kPos];\n          var x2 = bufSrc[kPos + 4];\n          var x3 = xPos < wSrc - 2 ? bufSrc[kPos + 8] : 2 * bufSrc[kPos + 4] - bufSrc[kPos];\n          buf1[buf1Pos + k] = interpolate(x0, x1, x2, x3, t);\n        }\n      }\n    } // this._writeFile(wDst2, hSrc, buf1, \"out/buf1.jpg\");\n    // ===========================================================\n    // Pass 2 - interpolate columns\n    // buf2 has width and height of dst2\n\n\n    var buf2 = Buffer.alloc(wDst2 * hDst2 * 4);\n\n    for (var _i = 0; _i < hDst2; _i++) {\n      for (var _j = 0; _j < wDst2; _j++) {\n        // i&j in dst2 coords\n        // calculate y in buf1 coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (hSrc-1)/hDst2\n        var y = _i * (hSrc - 1) / hDst2;\n        var yPos = Math.floor(y);\n\n        var _t = y - yPos;\n\n        var _buf1Pos = (yPos * wDst2 + _j) * 4;\n\n        var buf2Pos = (_i * wDst2 + _j) * 4;\n\n        for (var _k = 0; _k < 4; _k++) {\n          var _kPos = _buf1Pos + _k;\n\n          var y0 = yPos > 0 ? buf1[_kPos - wDst2 * 4] : 2 * buf1[_kPos] - buf1[_kPos + wDst2 * 4];\n          var y1 = buf1[_kPos];\n          var y2 = buf1[_kPos + wDst2 * 4];\n          var y3 = yPos < hSrc - 2 ? buf1[_kPos + wDst2 * 8] : 2 * buf1[_kPos + wDst2 * 4] - buf1[_kPos];\n          buf2[buf2Pos + _k] = interpolate(y0, y1, y2, y3, _t);\n        }\n      }\n    } // this._writeFile(wDst2, hDst2, buf2, \"out/buf2.jpg\");\n    // ===========================================================\n    // Pass 3 - scale to dst\n\n\n    var m = wM * hM;\n\n    if (m > 1) {\n      for (var _i2 = 0; _i2 < hDst; _i2++) {\n        for (var _j2 = 0; _j2 < wDst; _j2++) {\n          // i&j in dst bounded coords\n          var r = 0;\n          var g = 0;\n          var b = 0;\n          var a = 0;\n          var realColors = 0;\n\n          for (var _y = 0; _y < hM; _y++) {\n            var _yPos = _i2 * hM + _y;\n\n            for (var _x = 0; _x < wM; _x++) {\n              var _xPos = _j2 * wM + _x;\n\n              var xyPos = (_yPos * wDst2 + _xPos) * 4;\n              var pixelAlpha = buf2[xyPos + 3];\n\n              if (pixelAlpha) {\n                r += buf2[xyPos];\n                g += buf2[xyPos + 1];\n                b += buf2[xyPos + 2];\n                realColors++;\n              }\n\n              a += pixelAlpha;\n            }\n          }\n\n          var pos = (_i2 * wDst + _j2) * 4;\n          bufDst[pos] = realColors ? Math.round(r / realColors) : 0;\n          bufDst[pos + 1] = realColors ? Math.round(g / realColors) : 0;\n          bufDst[pos + 2] = realColors ? Math.round(b / realColors) : 0;\n          bufDst[pos + 3] = Math.round(a / m);\n        }\n      }\n    } else {\n      // replace dst buffer with buf2\n      dst.data = buf2;\n    }\n  },\n  bicubicInterpolation: function bicubicInterpolation(src, dst, options) {\n    var interpolateCubic = function interpolateCubic(x0, x1, x2, x3, t) {\n      var a0 = x3 - x2 - x0 + x1;\n      var a1 = x0 - x1 - a0;\n      var a2 = x2 - x0;\n      var a3 = x1;\n      return Math.max(0, Math.min(255, a0 * (t * t * t) + a1 * (t * t) + a2 * t + a3));\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateCubic);\n  },\n  hermiteInterpolation: function hermiteInterpolation(src, dst, options) {\n    var interpolateHermite = function interpolateHermite(x0, x1, x2, x3, t) {\n      var c0 = x1;\n      var c1 = 0.5 * (x2 - x0);\n      var c2 = x0 - 2.5 * x1 + 2 * x2 - 0.5 * x3;\n      var c3 = 0.5 * (x3 - x0) + 1.5 * (x1 - x2);\n      return Math.max(0, Math.min(255, Math.round(((c3 * t + c2) * t + c1) * t + c0)));\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateHermite);\n  },\n  bezierInterpolation: function bezierInterpolation(src, dst, options) {\n    // between 2 points y(n), y(n+1), use next points out, y(n-1), y(n+2)\n    // to predict control points (a & b) to be placed at n+0.5\n    //  ya(n) = y(n) + (y(n+1)-y(n-1))/4\n    //  yb(n) = y(n+1) - (y(n+2)-y(n))/4\n    // then use std bezier to interpolate [n,n+1)\n    //  y(n+t) = y(n)*(1-t)^3 + 3 * ya(n)*(1-t)^2*t + 3 * yb(n)*(1-t)*t^2 + y(n+1)*t^3\n    //  note the 3* factor for the two control points\n    // for edge cases, can choose:\n    //  y(-1) = y(0) - 2*(y(1)-y(0))\n    //  y(w) = y(w-1) + 2*(y(w-1)-y(w-2))\n    // but can go with y(-1) = y(0) and y(w) = y(w-1)\n    var interpolateBezier = function interpolateBezier(x0, x1, x2, x3, t) {\n      // x1, x2 are the knots, use x0 and x3 to calculate control points\n      var cp1 = x1 + (x2 - x0) / 4;\n      var cp2 = x2 - (x3 - x1) / 4;\n      var nt = 1 - t;\n      var c0 = x1 * nt * nt * nt;\n      var c1 = 3 * cp1 * nt * nt * t;\n      var c2 = 3 * cp2 * nt * t * t;\n      var c3 = x2 * t * t * t;\n      return Math.max(0, Math.min(255, Math.round(c0 + c1 + c2 + c3)));\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateBezier);\n  }\n};\n//# sourceMappingURL=resize2.js.map"]}