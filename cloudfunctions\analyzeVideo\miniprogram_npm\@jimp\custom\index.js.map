{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = configure;\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar _core = _interopRequireWildcard(require(\"@jimp/core\"));\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { (0, _defineProperty2[\"default\"])(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction configure(configuration) {\n  var jimpInstance = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _core[\"default\"];\n  var jimpConfig = {\n    hasAlpha: {},\n    encoders: {},\n    decoders: {},\n    \"class\": {},\n    constants: {}\n  };\n\n  function addToConfig(newConfig) {\n    Object.entries(newConfig).forEach(function (_ref) {\n      var _ref2 = (0, _slicedToArray2[\"default\"])(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n\n      jimpConfig[key] = _objectSpread({}, jimpConfig[key], {}, value);\n    });\n  }\n\n  function addImageType(typeModule) {\n    var type = typeModule();\n\n    if (Array.isArray(type.mime)) {\n      _core.addType.apply(void 0, (0, _toConsumableArray2[\"default\"])(type.mime));\n    } else {\n      Object.entries(type.mime).forEach(function (mimeType) {\n        return _core.addType.apply(void 0, (0, _toConsumableArray2[\"default\"])(mimeType));\n      });\n    }\n\n    delete type.mime;\n    addToConfig(type);\n  }\n\n  function addPlugin(pluginModule) {\n    var plugin = pluginModule(_core.jimpEvChange) || {};\n\n    if (!plugin[\"class\"] && !plugin.constants) {\n      // Default to class function\n      addToConfig({\n        \"class\": plugin\n      });\n    } else {\n      addToConfig(plugin);\n    }\n  }\n\n  if (configuration.types) {\n    configuration.types.forEach(addImageType);\n    jimpInstance.decoders = _objectSpread({}, jimpInstance.decoders, {}, jimpConfig.decoders);\n    jimpInstance.encoders = _objectSpread({}, jimpInstance.encoders, {}, jimpConfig.encoders);\n    jimpInstance.hasAlpha = _objectSpread({}, jimpInstance.hasAlpha, {}, jimpConfig.hasAlpha);\n  }\n\n  if (configuration.plugins) {\n    configuration.plugins.forEach(addPlugin);\n  }\n\n  (0, _core.addJimpMethods)(jimpConfig[\"class\"], jimpInstance);\n  (0, _core.addConstants)(jimpConfig.constants, jimpInstance);\n  return _core[\"default\"];\n}\n\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}