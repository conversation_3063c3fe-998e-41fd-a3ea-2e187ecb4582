{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Applies a minimum color threshold to a greyscale image.  Converts image to greyscale by default\n * @param {number} options object\n *  max: A number auto limited between 0 - 255\n *  replace: (optional) A number auto limited between 0 - 255 (default 255)\n *  autoGreyscale: (optional) A boolean whether to apply greyscale beforehand (default true)\n * @param {number} cb (optional) a callback for when complete\n * @return {this} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    threshold: function threshold(_ref, cb) {\n      var _this = this;\n\n      var max = _ref.max,\n          _ref$replace = _ref.replace,\n          replace = _ref$replace === void 0 ? 255 : _ref$replace,\n          _ref$autoGreyscale = _ref.autoGreyscale,\n          autoGreyscale = _ref$autoGreyscale === void 0 ? true : _ref$autoGreyscale;\n\n      if (typeof max !== 'number') {\n        return _utils.throwError.call(this, 'max must be a number', cb);\n      }\n\n      if (typeof replace !== 'number') {\n        return _utils.throwError.call(this, 'replace must be a number', cb);\n      }\n\n      if (typeof autoGreyscale !== 'boolean') {\n        return _utils.throwError.call(this, 'autoGreyscale must be a boolean', cb);\n      }\n\n      max = this.constructor.limit255(max);\n      replace = this.constructor.limit255(replace);\n\n      if (autoGreyscale) {\n        this.greyscale();\n      }\n\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var grey = _this.bitmap.data[idx] < max ? _this.bitmap.data[idx] : replace;\n        _this.bitmap.data[idx] = grey;\n        _this.bitmap.data[idx + 1] = grey;\n        _this.bitmap.data[idx + 2] = grey;\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}