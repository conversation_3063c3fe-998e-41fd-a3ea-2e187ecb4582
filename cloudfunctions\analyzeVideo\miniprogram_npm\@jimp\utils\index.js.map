{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isNodePattern = isNodePattern;\nexports.throwError = throwError;\nexports.scan = scan;\nexports.scanIterator = scanIterator;\n\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\n\nvar _marked =\n/*#__PURE__*/\n_regenerator[\"default\"].mark(scanIterator);\n\nfunction isNodePattern(cb) {\n  if (typeof cb === 'undefined') {\n    return false;\n  }\n\n  if (typeof cb !== 'function') {\n    throw new TypeError('Callback must be a function');\n  }\n\n  return true;\n}\n\nfunction throwError(error, cb) {\n  if (typeof error === 'string') {\n    error = new Error(error);\n  }\n\n  if (typeof cb === 'function') {\n    return cb.call(this, error);\n  }\n\n  throw error;\n}\n\nfunction scan(image, x, y, w, h, f) {\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n  w = Math.round(w);\n  h = Math.round(h);\n\n  for (var _y = y; _y < y + h; _y++) {\n    for (var _x = x; _x < x + w; _x++) {\n      var idx = image.bitmap.width * _y + _x << 2;\n      f.call(image, _x, _y, idx);\n    }\n  }\n\n  return image;\n}\n\nfunction scanIterator(image, x, y, w, h) {\n  var _y, _x, idx;\n\n  return _regenerator[\"default\"].wrap(function scanIterator$(_context) {\n    while (1) {\n      switch (_context.prev = _context.next) {\n        case 0:\n          // round input\n          x = Math.round(x);\n          y = Math.round(y);\n          w = Math.round(w);\n          h = Math.round(h);\n          _y = y;\n\n        case 5:\n          if (!(_y < y + h)) {\n            _context.next = 17;\n            break;\n          }\n\n          _x = x;\n\n        case 7:\n          if (!(_x < x + w)) {\n            _context.next = 14;\n            break;\n          }\n\n          idx = image.bitmap.width * _y + _x << 2;\n          _context.next = 11;\n          return {\n            x: _x,\n            y: _y,\n            idx: idx,\n            image: image\n          };\n\n        case 11:\n          _x++;\n          _context.next = 7;\n          break;\n\n        case 14:\n          _y++;\n          _context.next = 5;\n          break;\n\n        case 17:\n        case \"end\":\n          return _context.stop();\n      }\n    }\n  }, _marked);\n}\n//# sourceMappingURL=index.js.map"]}