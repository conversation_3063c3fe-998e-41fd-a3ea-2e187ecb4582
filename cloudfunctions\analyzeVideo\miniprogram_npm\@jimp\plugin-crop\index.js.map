{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = pluginCrop;\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _utils = require(\"@jimp/utils\");\n\n/* eslint-disable no-labels */\nfunction pluginCrop(event) {\n  /**\n   * Crops the image at a given point to a give size\n   * @param {number} x the x coordinate to crop form\n   * @param {number} y the y coordinate to crop form\n   * @param w the width of the crop region\n   * @param h the height of the crop region\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  event('crop', function (x, y, w, h, cb) {\n    if (typeof x !== 'number' || typeof y !== 'number') return _utils.throwError.call(this, 'x and y must be numbers', cb);\n    if (typeof w !== 'number' || typeof h !== 'number') return _utils.throwError.call(this, 'w and h must be numbers', cb); // round input\n\n    x = Math.round(x);\n    y = Math.round(y);\n    w = Math.round(w);\n    h = Math.round(h);\n\n    if (x === 0 && w === this.bitmap.width) {\n      // shortcut\n      var start = w * y + x << 2;\n      var end = start + h * w << 2;\n      this.bitmap.data = this.bitmap.data.slice(start, end);\n    } else {\n      var bitmap = Buffer.allocUnsafe(w * h * 4);\n      var offset = 0;\n      this.scanQuiet(x, y, w, h, function (x, y, idx) {\n        var data = this.bitmap.data.readUInt32BE(idx, true);\n        bitmap.writeUInt32BE(data, offset, true);\n        offset += 4;\n      });\n      this.bitmap.data = bitmap;\n    }\n\n    this.bitmap.width = w;\n    this.bitmap.height = h;\n\n    if ((0, _utils.isNodePattern)(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  });\n  return {\n    \"class\": {\n      /**\n       * Autocrop same color borders from this image\n       * @param {number} tolerance (optional): a percent value of tolerance for pixels color difference (default: 0.0002%)\n       * @param {boolean} cropOnlyFrames (optional): flag to crop only real frames: all 4 sides of the image must have some border (default: true)\n       * @param {function(Error, Jimp)} cb (optional): a callback for when complete (default: no callback)\n       * @returns {Jimp} this for chaining of methods\n       */\n      autocrop: function autocrop() {\n        var w = this.bitmap.width;\n        var h = this.bitmap.height;\n        var minPixelsPerSide = 1; // to avoid cropping completely the image, resulting in an invalid 0 sized image\n\n        var cb; // callback\n\n        var leaveBorder = 0; // Amount of pixels in border to leave\n\n        var tolerance = 0.0002; // percent of color difference tolerance (default value)\n\n        var cropOnlyFrames = true; // flag to force cropping only if the image has a real \"frame\"\n        // i.e. all 4 sides have some border (default value)\n\n        var cropSymmetric = false; // flag to force cropping top be symmetric.\n        // i.e. north and south / east and west are cropped by the same value\n        // parse arguments\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        for (var a = 0, len = args.length; a < len; a++) {\n          if (typeof args[a] === 'number') {\n            // tolerance value passed\n            tolerance = args[a];\n          }\n\n          if (typeof args[a] === 'boolean') {\n            // cropOnlyFrames value passed\n            cropOnlyFrames = args[a];\n          }\n\n          if (typeof args[a] === 'function') {\n            // callback value passed\n            cb = args[a];\n          }\n\n          if ((0, _typeof2[\"default\"])(args[a]) === 'object') {\n            // config object passed\n            var config = args[a];\n\n            if (typeof config.tolerance !== 'undefined') {\n              tolerance = config.tolerance;\n            }\n\n            if (typeof config.cropOnlyFrames !== 'undefined') {\n              cropOnlyFrames = config.cropOnlyFrames;\n            }\n\n            if (typeof config.cropSymmetric !== 'undefined') {\n              cropSymmetric = config.cropSymmetric;\n            }\n\n            if (typeof config.leaveBorder !== 'undefined') {\n              leaveBorder = config.leaveBorder;\n            }\n          }\n        }\n        /**\n         * All borders must be of the same color as the top left pixel, to be cropped.\n         * It should be possible to crop borders each with a different color,\n         * but since there are many ways for corners to intersect, it would\n         * introduce unnecessary complexity to the algorithm.\n         */\n        // scan each side for same color borders\n\n\n        var colorTarget = this.getPixelColor(0, 0); // top left pixel color is the target color\n\n        var rgba1 = this.constructor.intToRGBA(colorTarget); // for north and east sides\n\n        var northPixelsToCrop = 0;\n        var eastPixelsToCrop = 0;\n        var southPixelsToCrop = 0;\n        var westPixelsToCrop = 0; // north side (scan rows from north to south)\n\n        colorTarget = this.getPixelColor(0, 0);\n\n        north: for (var y = 0; y < h - minPixelsPerSide; y++) {\n          for (var x = 0; x < w; x++) {\n            var colorXY = this.getPixelColor(x, y);\n            var rgba2 = this.constructor.intToRGBA(colorXY);\n\n            if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break north;\n            }\n          } // this row contains all pixels with the same color: increment this side pixels to crop\n\n\n          northPixelsToCrop++;\n        } // east side (scan columns from east to west)\n\n\n        colorTarget = this.getPixelColor(w, 0);\n\n        east: for (var _x = 0; _x < w - minPixelsPerSide; _x++) {\n          for (var _y = 0 + northPixelsToCrop; _y < h; _y++) {\n            var _colorXY = this.getPixelColor(_x, _y);\n\n            var _rgba = this.constructor.intToRGBA(_colorXY);\n\n            if (this.constructor.colorDiff(rgba1, _rgba) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break east;\n            }\n          } // this column contains all pixels with the same color: increment this side pixels to crop\n\n\n          eastPixelsToCrop++;\n        } // south side (scan rows from south to north)\n\n\n        colorTarget = this.getPixelColor(0, h);\n\n        south: for (var _y2 = h - 1; _y2 >= northPixelsToCrop + minPixelsPerSide; _y2--) {\n          for (var _x2 = w - eastPixelsToCrop - 1; _x2 >= 0; _x2--) {\n            var _colorXY2 = this.getPixelColor(_x2, _y2);\n\n            var _rgba2 = this.constructor.intToRGBA(_colorXY2);\n\n            if (this.constructor.colorDiff(rgba1, _rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break south;\n            }\n          } // this row contains all pixels with the same color: increment this side pixels to crop\n\n\n          southPixelsToCrop++;\n        } // west side (scan columns from west to east)\n\n\n        colorTarget = this.getPixelColor(w, h);\n\n        west: for (var _x3 = w - 1; _x3 >= 0 + eastPixelsToCrop + minPixelsPerSide; _x3--) {\n          for (var _y3 = h - 1; _y3 >= 0 + northPixelsToCrop; _y3--) {\n            var _colorXY3 = this.getPixelColor(_x3, _y3);\n\n            var _rgba3 = this.constructor.intToRGBA(_colorXY3);\n\n            if (this.constructor.colorDiff(rgba1, _rgba3) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break west;\n            }\n          } // this column contains all pixels with the same color: increment this side pixels to crop\n\n\n          westPixelsToCrop++;\n        } // decide if a crop is needed\n\n\n        var doCrop = false; // apply leaveBorder\n\n        westPixelsToCrop -= leaveBorder;\n        eastPixelsToCrop -= leaveBorder;\n        northPixelsToCrop -= leaveBorder;\n        southPixelsToCrop -= leaveBorder;\n\n        if (cropSymmetric) {\n          var horizontal = Math.min(eastPixelsToCrop, westPixelsToCrop);\n          var vertical = Math.min(northPixelsToCrop, southPixelsToCrop);\n          westPixelsToCrop = horizontal;\n          eastPixelsToCrop = horizontal;\n          northPixelsToCrop = vertical;\n          southPixelsToCrop = vertical;\n        } // make sure that crops are >= 0\n\n\n        westPixelsToCrop = westPixelsToCrop >= 0 ? westPixelsToCrop : 0;\n        eastPixelsToCrop = eastPixelsToCrop >= 0 ? eastPixelsToCrop : 0;\n        northPixelsToCrop = northPixelsToCrop >= 0 ? northPixelsToCrop : 0;\n        southPixelsToCrop = southPixelsToCrop >= 0 ? southPixelsToCrop : 0; // safety checks\n\n        var widthOfRemainingPixels = w - (westPixelsToCrop + eastPixelsToCrop);\n        var heightOfRemainingPixels = h - (southPixelsToCrop + northPixelsToCrop);\n\n        if (cropOnlyFrames) {\n          // crop image if all sides should be cropped\n          doCrop = eastPixelsToCrop !== 0 && northPixelsToCrop !== 0 && westPixelsToCrop !== 0 && southPixelsToCrop !== 0;\n        } else {\n          // crop image if at least one side should be cropped\n          doCrop = eastPixelsToCrop !== 0 || northPixelsToCrop !== 0 || westPixelsToCrop !== 0 || southPixelsToCrop !== 0;\n        }\n\n        if (doCrop) {\n          // do the real crop\n          this.crop(eastPixelsToCrop, northPixelsToCrop, widthOfRemainingPixels, heightOfRemainingPixels);\n        }\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      }\n    }\n  };\n}\n\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}