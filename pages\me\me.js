// 个人页面
const app = getApp()

// 用户数据访问客户端
class ProfileClient {
  constructor() {
    this.cache = new Map();
    // 从Storage加载初始缓存
    const storedUserInfo = wx.getStorageSync('userInfo');
    if (storedUserInfo) {
      this.cache.set('userInfo', storedUserInfo);
    }
    // dataPath缓存预留，暂未使用
    // const storedDataPath = wx.getStorageSync('dataPath');
    // if (storedDataPath) {
    //   this.cache.set('dataPath', storedDataPath);
    // }
  }

  async getCurrentProfile() {
    try {
      const { result } = await wx.cloud.callFunction({
        name: 'login'
      });
      
      if (!result) {
        throw new Error('调用登录云函数失败');
      }

      if (!result.success) {
        if (result.needLogin) {
          this.clearCacheAndStorage(); // 需要登录时清除所有缓存
          return null;
        }
        throw new Error(result.message || '获取用户配置失败');
      }

      // 更新缓存和Storage
      this.updateCache(result.data);
      if (result.data && result.data.userInfo) {
        wx.setStorageSync('userInfo', result.data.userInfo);
        wx.setStorageSync('isLoggedIn', true);
      } else { // 即使success为true，但没有userInfo，也视为未登录
        this.clearCacheAndStorage();
      }
      return result.data;
    } catch (err) {
      console.error('获取用户信息失败:', err);
      // 根据错误类型决定是否清除本地缓存
      // 例如: if (err.message.includes("session_key已过期") || err.message.includes("登录态校验失败")) {
      //   this.clearCacheAndStorage();
      // }
      throw err;
    }
  }

  async updateProfile(userInfo) {
    try {
      if (!userInfo) {
        throw new Error('用户信息不能为空');
      }

      const { result } = await wx.cloud.callFunction({
        name: 'login',
        data: { userInfo }
      });

      if (!result) {
        throw new Error('调用登录云函数失败');
      }

      if (!result.success) {
        throw new Error(result.message || '更新用户信息失败');
      }

      // 更新缓存和Storage
      this.updateCache(result.data);
      if (result.data && result.data.userInfo) {
        wx.setStorageSync('userInfo', result.data.userInfo);
        wx.setStorageSync('isLoggedIn', true); // 确保更新时也设置isLoggedIn
      }
      return result.data;
    } catch (err) {
      console.error('更新用户信息失败:', err);
      throw err;
    }
  }

  updateCache(data) {
    if (data && data.userInfo) {
      this.cache.set('userInfo', data.userInfo);
      if (data.dataPath) {
        this.cache.set('dataPath', data.dataPath);
        // wx.setStorageSync('dataPath', data.dataPath); // 如果需要持久化dataPath
      }
    }
  }

  clearCache() { // 只清内存
    this.cache.clear();
  }

  // 同时清除内存和Storage
  clearCacheAndStorage() {
      this.cache.clear();
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('isLoggedIn');
      // 确保清除所有可能的用户数据存储
      wx.removeStorageSync('dataPath');
      wx.removeStorageSync('deviceCache');
      wx.removeStorageSync('lastAnalysisTime');
      wx.removeStorageSync('settings');
  }

  getCachedUserInfo() {
    // 优先从内存获取，没有的话再从Storage获取
    let userInfo = this.cache.get('userInfo');
    if (!userInfo) {
        userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
            this.cache.set('userInfo', userInfo); // 更新到内存
        }
    }
    return userInfo;
  }

  getCachedDataPath() {
    // dataPath也是类似处理
    let dataPath = this.cache.get('dataPath');
    if (!dataPath) {
        dataPath = wx.getStorageSync('dataPath');
        if (dataPath) {
            this.cache.set('dataPath', dataPath);
        }
    }
    return dataPath;
    // return this.cache.get('dataPath');
  }
}

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    isLoggedIn: false,
    canIUseProfile: wx.canIUse('getUserProfile'),
    canIUseNickname: wx.canIUse('input.type.nickname'),
    showBadge: false,
    isPhoneBound: false,
    showBindPhoneButton: false, // 设为false，不显示绑定手机号按钮
    showGetPhone: false,
    profileClient: null,
    meList: [
      {
        text: '设备信息',
        icon: '../../assets/img/iconfont-dingdan.png',
        url: '../deviceList/deviceList'
      },
      {
        text: '已绑定的设备',
        icon: '../../assets/img/iconfont-help.png',
        url: '../deviceList/deviceList'
      },
      {
        text: '解绑的设备',
        icon: '../../assets/img/iconfont-icontuan.png',
        url: '../unboundDeviceList/unboundDeviceList'
      },
      {
        text: '未绑定的设备',
        icon: '../../assets/img/iconfont-kefu.png',
        url: '../unboundDeviceList/unboundDeviceList'
      },
      {
        text: '添加新设备',
        icon: '../../assets/img/iconfont-tuihuo.png',
        url: '../addDevice/addDevice'
      },
    ],
    isShowingData: false,
    storedData: [],
    selectedDataIndexes: [],
    showChart: false,
    isGenerating: false,
    chartScale: 1,
    translateX: 0,
    translateY: 0,
    lastX: 0,
    lastY: 0,
    isDragging: false,
    isMultiSelectMode: false,  // 是否处于多选模式
    showParamDetail: false,    // 是否显示参数详情
    currentData: null,         // 当前选中的数据
    paramDetailHiding: false,  // Existing property for hiding animation
    paramDetailScrollTop: 0,  // Add this for scroll-view
    showMediaSelect: false,
    currentMediaIndex: null,
    showExportOptions: false,
    selectedVideoIndexes: [],
    selectedImageIndexes: [],
    canExport: false,  // 新增导出按钮状态控制
    showVideoPlayer: false,
    currentVideoUrl: '',
    searchQuery: '', // 新增：搜索查询
    filteredStoredData: [], // 新增：过滤后的数据
    searchTimer: null, // 新增：用于搜索防抖的计时器
    isSearchFocused: false, // 新增：控制搜索框聚焦样式
    currentTab: 'me', // 添加当前标签页状态
    paramDetailMaskHiding: false, // 遮罩层隐藏状态
    currentParamData: null,
    currentParamTitle: '',
    currentParamTime: '',
    paramScrollTop: 0,
    modeTransition: false, // 新增：模式过渡状态
    animateItems: false, // 新增：控制数据项动画显示
    // 保存相关状态
    showSaveProgress: false, // 显示保存进度
    showSaveResults: false, // 显示保存结果
    saveProgress: {
      percentage: 0,
      text: '',
      currentTask: ''
    },
    saveResults: {
      videos: [],
      images: [],
      dataFiles: [],
      errors: []
    }
  },

  // 页面布局适配方法
  _adaptPageLayout: function() {
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 获取状态栏高度（单位：px）
      const statusBarHeight = systemInfo.statusBarHeight || 0;

      // 获取安全区域信息
      const safeArea = systemInfo.safeArea || {};
      const safeAreaTop = safeArea.top || statusBarHeight;

      // 计算导航栏实际高度（状态栏 + 导航内容44rpx + 悬浮按钮空间20rpx）
      // 将rpx转换为px：44rpx = 44 * (screenWidth / 750)
      const navContentHeight = 44 * (systemInfo.screenWidth / 750);
      const floatingButtonSpace = 40 * (systemInfo.screenWidth / 750); // 为悬浮按钮留出更多空间
      const totalNavHeight = safeAreaTop + navContentHeight + floatingButtonSpace;

      console.log('me页面布局计算:', {
        statusBarHeight,
        safeAreaTop,
        navContentHeight,
        totalNavHeight,
        screenWidth: systemInfo.screenWidth
      });

      // 设置页面数据，用于动态样式
      this.setData({
        systemInfo: systemInfo,
        statusBarHeight: statusBarHeight,
        safeAreaTop: safeAreaTop,
        navContentHeight: navContentHeight,
        totalNavHeight: totalNavHeight
      });

    } catch (error) {
      console.error('me页面布局适配失败:', error);
      // 设置默认值，确保页面正常显示
      this.setData({
        statusBarHeight: 20,
        safeAreaTop: 20,
        totalNavHeight: 88
      });
    }
  },

  onLoad: function() {
    // 页面布局适配
    this._adaptPageLayout();

    // 创建ProfileClient实例
    // const profileClient = new ProfileClient(); // ProfileClient内部构造函数会尝试从StorageSync加载
    // this.setData({ profileClient });
    // 延迟到onShow中确保profileClient初始化，或在需要时再创建
    // 如果profileClient在onLoad创建，后续的onShow可能会拿到旧的实例
    // 更好的做法是在onShow确保profileClient的存在和状态
  },

  onShow: async function() {
    // 使用setTimeout避免递归更新
    setTimeout(async () => {
      let profileClient = this.data.profileClient;
      if (!profileClient) {
        profileClient = new ProfileClient(); // 确保profileClient实例存在
        this.setData({ profileClient });
      }

      const storedUserInfo = wx.getStorageSync('userInfo');
      const storedIsLoggedIn = wx.getStorageSync('isLoggedIn');

      if (storedIsLoggedIn && storedUserInfo) {
        // 从缓存中读取到了登录信息，设置为已登录状态
        // 确保内存中的profileClient缓存与storage保持一致
        profileClient.cache.set('userInfo', storedUserInfo);

        this.setData({
          userInfo: storedUserInfo,
          isLoggedIn: true,
          hasUserInfo: !!storedUserInfo,
          isPhoneBound: storedUserInfo && !!storedUserInfo.phoneNumber
        });
      } else {
        // 本地无缓存，或缓存表明未登录，设置为未登录状态
        this.setData({
          userInfo: null,
          isLoggedIn: false,
          hasUserInfo: false,
          isPhoneBound: false
        });
      }

      // 重置参数详情卡片状态，确保页面返回时动画效果正常
      console.log('页面显示，强制重置参数详情卡片状态');
      // 重要：每次强制重置所有状态，以修复第一次点击无动画的问题
      this.setData({
        currentTab: 'me', // 更新底部tabbar状态
        // 重置动画相关状态 - 确保彻底重置
        showParamDetail: false,
        paramDetailHiding: false,
        paramDetailMaskHiding: false,
        paramDetailCardAnimation: 'none', // 重要：重置动画样式
        currentParamData: {}, // 清空当前参数数据
        currentParamTitle: '', // 清空标题
        currentParamTime: '', // 清空时间
        paramScrollTop: 0, // 重置滚动位置
        // 退出多选模式相关状态重置
        isMultiSelectMode: false,
        modeTransition: false,
        animateItems: false,
        // 如果页面已关闭数据显示，也重置相关状态
        isShowingData: this.data.isShowingData || false
      });
    }, 50);
  },

  // 获取用户信息
  getUserProfile() {
    // 确保ProfileClient已初始化
    let profileClient = this.data.profileClient;
    if (!profileClient) {
      profileClient = new ProfileClient();
      this.setData({ profileClient });
    }

    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: async (res) => {
        let loginSuccess = false;
        wx.showLoading({
          title: '登录中...',
          mask: true
        });

        try {
          // 先尝试获取已有的用户信息
          const { result } = await wx.cloud.callFunction({
            name: 'login'
          }).catch(err => {
            throw new Error('网络连接失败，请检查网络后重试');
          });

          if (!result) {
            throw new Error('登录失败，请稍后重试');
          }

          // 准备用户信息
          let userInfo = {
            avatarUrl: res.userInfo.avatarUrl || '',
            gender: res.userInfo.gender || 0,
            country: res.userInfo.country || '',
            province: res.userInfo.province || '',
            city: res.userInfo.city || '',
            language: res.userInfo.language || 'zh_CN'
          };

          // 如果已有用户信息，使用已有的昵称，否则使用默认昵称
          if (result && result.success && result.data.userInfo) {
            userInfo.nickName = result.data.userInfo.nickName;
          } else {
            userInfo.nickName = '微信用户';
          }

          // 更新用户信息
          const profileData = await this.data.profileClient.updateProfile(userInfo).catch(err => {
            throw new Error('更新用户信息失败，请稍后重试');
          });
          
          // 更新页面状态
          this.setData({
            userInfo: profileData.userInfo,
            hasUserInfo: true,
            isLoggedIn: true,
            isPhoneBound: !!profileData.userInfo.phoneNumber
          });
          loginSuccess = true;
        } catch (err) {
          console.error('登录失败:', err);
          // 重置登录状态
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            isLoggedIn: false,
            isPhoneBound: false
          });
          if (this.data.profileClient) {
            this.data.profileClient.clearCache();
          }
          // 重新初始化ProfileClient
          const profileClient = new ProfileClient();
          this.setData({ profileClient });

          // 显示具体错误信息
          wx.showToast({
            title: err.message || '登录失败，请重试',
            icon: 'none',
            duration: 2000
          });
        } finally {
          wx.hideLoading();
          
          if (loginSuccess) {
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        console.error('用户拒绝授权:', err);
        // 根据错误类型显示不同提示
        let message = '需要授权才能使用';
        if (err.errMsg.includes('fail auth deny')) {
          message = '您已拒绝授权，部分功能将无法使用';
        } else if (err.errMsg.includes('fail system permission denied')) {
          message = '系统权限被禁用，请在设置中允许授权';
        } else if (err.errMsg.includes('fail Failed to fetch')) {
          message = '网络连接失败，请检查网络后重试';
        }

        wx.showToast({
          title: message,
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 更新昵称
  async updateNickname(newName) {
    if (!newName || !newName.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '更新中...',
        mask: true
      });

      // 获取当前用户信息
      const currentUserInfo = this.data.profileClient.getCachedUserInfo();
      if (!currentUserInfo) {
        throw new Error('未找到用户信息');
      }

      // 如果新昵称和当前昵称相同，直接返回
      if (currentUserInfo.nickName === newName) {
        wx.hideLoading();
        wx.showToast({
          title: '昵称未变更',
          icon: 'none'
        });
        return;
      }

      // 准备更新的用户信息
      const updatedUserInfo = {
        ...currentUserInfo,
        nickName: newName
      };

      // 调用login云函数更新用户信息
      const profileData = await this.data.profileClient.updateProfile(updatedUserInfo);
      
      // 更新页面状态和缓存
      // 确保使用新的昵称，无论云函数返回什么
      const finalUpdatedUserInfo = {
        ...(profileData.userInfo || this.data.userInfo), // 合并服务端可能返回的其他字段
        nickName: newName // 强制使用新昵称
      };

      // 更新页面展示
      this.setData({
        userInfo: finalUpdatedUserInfo,
        hasUserInfo: true
      });

      // 同步更新云端数据库
      await wx.cloud.callFunction({
        name: 'login',
        data: { 
          userInfo: finalUpdatedUserInfo,
          forceUpdate: true // 告诉服务端强制更新昵称
        }
      });

      // 确保 Storage 也使用包含新昵称的 userInfo 更新
      wx.setStorageSync('userInfo', finalUpdatedUserInfo);

      // 更新 ProfileClient 的内存缓存
      if (this.data.profileClient && typeof this.data.profileClient.updateCache === 'function') {
        this.data.profileClient.updateCache({ userInfo: finalUpdatedUserInfo });
      }

      wx.hideLoading();
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });
    } catch (err) {
      console.error('更新昵称失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    }
  },

  // 选择头像回调
  async onChooseAvatar(e) {
    if (e.detail.errMsg !== 'chooseAvatar:ok') {
      return;
    }

    try {
      wx.showLoading({
        title: '更新中...',
        mask: true
      });

      // 上传头像到用户配置目录
      const { result } = await wx.cloud.callFunction({
        name: 'updateAvatar',
        data: {
          avatarUrl: e.detail.avatarUrl,
          // currentPath: await this.data.profileClient.getCurrentPath() // getCurrentPath 未定义，需要确认其来源或移除
        }
      });

      if (result.success) {
        // 1. 更新 setData 以刷新UI
        this.setData({
          'userInfo.avatarUrl': result.data.avatarUrl,
          hasUserInfo: true
        });

        // 2. 准备包含新头像的完整 userInfo 对象
        const updatedUserInfoForAvatar = {
          ...this.data.userInfo, // 获取 setData 更新后的 this.data.userInfo
          avatarUrl: result.data.avatarUrl
        };

        // 3. 更新 Storage
        wx.setStorageSync('userInfo', updatedUserInfoForAvatar);

        // 4. 更新 ProfileClient 的内存缓存
        if (this.data.profileClient && typeof this.data.profileClient.updateCache === 'function') {
          this.data.profileClient.updateCache({ userInfo: updatedUserInfoForAvatar });
        }

        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      }
    } catch (err) {
      console.error('更新头像失败:', err);
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 登出
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除客户端缓存和持久化存储
          if (this.data.profileClient) {
            this.data.profileClient.clearCacheAndStorage();
          } else {
            // 兜底清除 Storage
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('isLoggedIn');
          }
          
          // 重置所有状态
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            isLoggedIn: false,
            isPhoneBound: false,
            showBindPhoneButton: false,
            showGetPhone: false
          });

          // 重新初始化ProfileClient
          const profileClient = new ProfileClient();
          this.setData({ profileClient });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 检查手机号绑定状态
  checkPhoneBinding() {
    const userInfo = wx.getStorageSync('userInfo');
    const isPhoneBound = userInfo && userInfo.phoneNumber;
    this.setData({
      isPhoneBound: !!isPhoneBound,
      showBindPhoneButton: true  // 保持按钮显示
    });
  },

  // 显示用户数据
  showMyData: async function() {
    console.log('点击了我的数据按钮');

    // 1. 检查前端记录的登录状态
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      wx.showToast({ title: '请先登录后再查看数据', icon: 'none' });
      // 可以选择性地自动弹出登录提示，或者让用户手动点击登录按钮
      // this.getUserProfile(); 
      return;
    }

    // 2. 如果前端认为已登录，再向服务器确认一下登录态是否真实有效
    let serverProfile;
    try {
      wx.showLoading({ title: '验证登录状态...', mask: true });
      serverProfile = await this.data.profileClient.getCurrentProfile(); // getCurrentProfile会与服务器通信
      wx.hideLoading();

      if (!serverProfile || !serverProfile.userInfo) { 
        // 服务器验证失败（例如session过期），此时getCurrentProfile内部已清除了storage
        this.setData({ 
          userInfo: null, 
          isLoggedIn: false, 
          hasUserInfo: false, 
          isPhoneBound: false 
        });
        wx.showModal({
          title: '登录提醒',
          content: '您的登录已过期，请重新登录后查看数据。',
          showCancel: false,
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              this.getUserProfile(); // 引导用户重新登录
            }
          }
        });
        return;
      }

      // 如果服务器验证成功，并且返回的用户信息与本地不一致，则更新本地信息
      if (JSON.stringify(this.data.userInfo) !== JSON.stringify(serverProfile.userInfo)) {
        this.setData({
          userInfo: serverProfile.userInfo,
          isLoggedIn: true,
          hasUserInfo: true,
          isPhoneBound: !!serverProfile.userInfo.phoneNumber
        });
        
        // 同时更新本地缓存和Storage以保持一致性
        wx.setStorageSync('userInfo', serverProfile.userInfo);
        
        // 更新ProfileClient的内存缓存
        if (this.data.profileClient) {
          this.data.profileClient.updateCache({ userInfo: serverProfile.userInfo });
        }
      }
    } catch (err) {
      wx.hideLoading();
      // 网络错误或其他导致验证失败的情况
      console.error("验证登录状态时发生错误:", err);
      wx.showModal({
        title: '操作失败',
        content: '验证登录状态失败，请检查网络并稍后重试。',
        showCancel: false,
        confirmText: '知道了'
      });
      // 发生错误时，不一定立即清除本地登录状态，除非错误明确指示 session 无效
      // 如果 getCurrentProfile 内部已处理清除，则这里不需要重复
      return;
    }
    
    // ----- 验证通过后，执行原有的 showMyData 业务逻辑 -----
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 调用云函数获取用户数据
    wx.cloud.callFunction({
        name: 'getUserData',
        success: (res) => {
          console.log('获取用户数据成功', res);
          wx.hideLoading();
          
          const resultData = res.result;
          // 检查是否有数据
          if (!resultData || !resultData.data || resultData.data.length === 0) {
            wx.showToast({
              title: '没有找到数据',
              icon: 'none'
            });
            return;
          }
          
          // 打印完整的原始数据结构，帮助调试
          console.log('原始数据结构示例:', JSON.stringify(resultData.data[0], null, 2));
          
          // 处理数据进行展示
          let displayData = resultData.data.map((item, index) => { // 添加 index 参数
            // 首先完整记录每项数据的关键字段，帮助调试
            console.log('数据项关键字段:', {
              customName: item.customName,
              sessionId: item.sessionId,
              resultFilePath: item.resultFilePath,
              cAreaImage: item.cAreaImage ? item.cAreaImage.slice(0, 100) + '...' : undefined,
              tAreaImage: item.tAreaImage ? item.tAreaImage.slice(0, 100) + '...' : undefined,
              createTime: item.createTime,
              updateTime: item.updateTime
            });
            
            // 根据文件夹命名逻辑，优先级顺序调整为
            // 1.T区图片路径中的时间 2.其他图片路径中的文件夹名称 3.sessionId 4.自定义名称 5.创建时间
            let displayTime = '';
            let displaySource = '';
            
            // 优先从T区图片路径中提取时间信息
            if (item.tAreaImage || item.cAreaImage || item.resultFilePath) {
              // 直接将整个item传递给formatDisplayTime，让它从路径中提取时间
              displayTime = this.formatDisplayTime('', item);
              if (displayTime && displayTime !== '时间未知') {
                displaySource = 'tAreaImage_path';
              }
            }
            // 如果从路径中没有提取到时间，但有自定义名称，则使用自定义名称
            else if (item.customName) {
              displayTime = item.customName;
              displaySource = 'customName';
            }
            
            // 如果从T区图片路径中无法获取时间，尝试其他文件路径
            if (!displayTime && (item.resultFilePath || item.cAreaImagePath || item.tAreaImagePath || item.cAreaImage)) {
              // 尝试从任何可用的文件路径中提取文件夹名称
              const path = item.resultFilePath || item.cAreaImagePath || item.tAreaImagePath || item.cAreaImage;
              if (path) {
                // 从路径中提取文件夹名称（格式如：/users/xxxx_data/2025_06_04_15_09_32/...）
                // 改进正则表达式，优先匹配包含标准时间格式的文件夹名称
                const timeFormatRegex = /\/([12]\d{3}[-_][01]\d[-_][0-3]\d[-_][0-2]\d[-_][0-5]\d[-_][0-5]\d)\//;
                const folderRegex = /\/users\/[^\/]+_data\/([^\/]+)\//;
                
                // 首先尝试匹配标准时间格式
                let timeMatch = path.match(timeFormatRegex);
                if (timeMatch && timeMatch[1]) {
                  displayTime = this.formatDisplayTime('', timeMatch[1]);
                  displaySource = 'filepath_time_format';
                }
                // 如果未匹配到时间格式，则尝试提取标准数据路径格式中的文件夹名
                else if (path.includes('users') && path.includes('_data')) {
                  const match = path.match(folderRegex);
                  if (match && match[1]) {
                    const folderName = match[1];
                    displayTime = this.formatDisplayTime('', folderName);
                    displaySource = 'filepath_folder';
                  }
                } 
                // 最后尝试从路径分割并查找符合时间格式的部分
                else {
                  const pathParts = path.split('/');
                  for (let i = 0; i < pathParts.length; i++) {
                    if (/^[12]\d{3}[-_][01]\d[-_][0-3]\d[-_][0-2]\d[-_][0-5]\d[-_][0-5]\d$/.test(pathParts[i])) {
                      displayTime = this.formatDisplayTime('', pathParts[i]);
                      displaySource = 'filepath_parts';
                      break;
                    }
                  }
                }
              }
            }
            
            // 仅当无法从文件路径获取时间戳时才使用sessionId（含UTC时间）
            if (!displayTime && item.sessionId) {
              displayTime = this.formatDisplayTime('', item.sessionId);
              displaySource = 'sessionId';
            }
            
            if (!displayTime && item.createTime) {
              displayTime = this.formatDisplayTime(item.createTime);
              displaySource = 'createTime';
            } else if (!displayTime && item.updateTime) {
              displayTime = this.formatDisplayTime(item.updateTime);
              displaySource = 'updateTime';
            }
            
            console.log(`显示时间来源: ${displaySource}, 值: ${displayTime}`);
            
            return {
              _id: item._id || Math.random().toString(36).substring(2),
              originalIndex: index, // 在这里记录原始索引
              customName: item.customName,
              tcValue: item.tcValue || '暂无数据',
              cAreaValue: item.cAreaValue || '暂无数据',
              tAreaValue: item.tAreaValue || '暂无数据',
              concentration: item.concentration || '未测量',
              isTemp: item.isTemp !== undefined ? item.isTemp : true,
              createTime: item.createTime,
              updateTime: item.updateTime || item.analysisTime,
              analysisTime: item.analysisTime,
              displayTime: displayTime,
              sessionId: item.sessionId,
              resultFilePath: item.resultFilePath,
              parameters: item.parameters || {
                brightness: 115,
                contrast: 115,
                saturation: 106,
                white_balance_temperature_auto: 0,
                gain: 0,
                power_line_frequency: 2,
                white_balance_temperature: 4650,
                sharpness: 10,
                exposure_auto: 3,
                exposure_absolute: 1250,
                pan_absolute: 0,
                tilt_absolute: 0,
                focus_absolute: 0,
                zoom_absolute: 100,
  
                setVoltage: 12
              },
              cAreaImage: item.cAreaImage,
              tAreaImage: item.tAreaImage,
              videoPath: item.videoPath,
              selected: false,
              videoSelected: false,
              imageSelected: false
            };
          });
          
          // 按时间倒序排列，最新的数据在前面
          displayData.sort((a, b) => {
            // 优先比较createTime
            const timeA = a.createTime ? new Date(a.createTime).getTime() : 
                         (a.updateTime ? new Date(a.updateTime).getTime() : 0);
            const timeB = b.createTime ? new Date(b.createTime).getTime() : 
                         (b.updateTime ? new Date(b.updateTime).getTime() : 0);
            return timeB - timeA;
          });
          
          // 更新页面数据
          this.setData({
            isShowingData: true,
            isMultiSelectMode: false,
            selectedDataIndexes: [],
            selectedVideoIndexes: [],
            selectedImageIndexes: [],
            storedData: displayData,
            filteredStoredData: displayData, // 初始显示所有数据
    searchQuery: '', // 清空搜索框
    // 重置参数详情卡片状态，确保返回页面后动画效果正常
    showParamDetail: false,
    paramDetailHiding: false,
    paramDetailMaskHiding: false
          });
          console.log('数据显示完成, 共显示', displayData.length, '条记录');
        },
        fail: (err) => {
          console.error('获取用户数据失败:', err);
          wx.hideLoading();
          wx.showToast({
              title: '获取数据失败',
              icon: 'none'
          });
        }
    });
  },

  hideDataDisplay: function() {
    console.log('点击了返回按钮');
    // 如果正在显示图表，不要关闭数据显示界面
    if (this.data.showChart) {
      console.log('图表正在显示，不关闭界面');
      return;
    }
    
    console.log('关闭数据显示界面');
    this.setData({
      isShowingData: false,
      selectedDataIndexes: [],  // 清空选中状态
      isMultiSelectMode: false, // 退出多选模式
      storedData: this.data.storedData.map(item => ({...item, selected: false})),  // 重置所有选中状态
      searchQuery: '', // 清空搜索
      filteredStoredData: [], // 清空过滤列表
      // 重置参数详情卡片状态
      showParamDetail: false,
      paramDetailHiding: false,
      paramDetailMaskHiding: false
    });
  },

  // 比较版本号
  compareVersion(v1, v2) {
    v1 = v1.split('.');
    v2 = v2.split('.');
    const len = Math.max(v1.length, v2.length);
    while (v1.length < len) {
      v1.push('0');
    }
    while (v2.length < len) {
      v2.push('0');
    }
    for (let i = 0; i < len; i++) {
      const num1 = parseInt(v1[i]);
      const num2 = parseInt(v2[i]);
      if (num1 > num2) {
        return 1;
      } else if (num1 < num2) {
        return -1;
      }
    }
    return 0;
  },

  // 使用用户信息进行登录
  loginWithUserInfo(userInfo) {
    console.log('开始登录，用户信息:', userInfo);
    // 调用登录云函数，获取/更新用户信息
    wx.cloud.callFunction({
      name: 'login',
      data: {
        userInfo: userInfo
      }
    }).then(res => {
      console.log('云函数登录结果：', res);
      
      if (res.result && res.result.success) {
        const { data } = res.result;
        console.log('解析的数据:', data);
        
        // 更新页面状态
        this.setData({
          userInfo: data.userInfo,
          hasUserInfo: true,
          isLoggedIn: true,
          isPhoneBound: data.isPhoneBound || false,
          showBindPhoneButton: true
        });

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', data.userInfo);

        // 登录成功后，如果未绑定手机号，显示绑定提示
        if (!data.isPhoneBound) {
          setTimeout(() => {
            wx.showModal({
              title: '提示',
              content: '是否需要绑定手机号？',
              confirmText: '立即绑定',
              success: (modalRes) => {
                console.log('用户选择是否绑定手机号:', modalRes);
                if (modalRes.confirm) {
                  this.showPhoneInput();
                }
              }
            });
          }, 500);
        }
      } else {
        console.error('登录返回数据异常:', res);
        wx.showToast({
          title: res.result ? res.result.message : '登录失败',
          icon: 'error'
        });
      }
    }).catch(err => {
      console.error('调用云函数失败：', err);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    });
  },

  // 处理手机号绑定按钮点击
  handlePhoneBinding() {
    if (this.data.isPhoneBound) {
      wx.showActionSheet({
        itemList: ['更换手机号', '解除绑定'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 更换手机号
            this.showPhoneInput();
          } else if (res.tapIndex === 1) {
            // 解除绑定
            wx.showModal({
              title: '提示',
              content: '确定要解除手机号绑定吗？',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.unbindPhone();
                }
              }
            });
          }
        }
      });
    } else {
      // 未绑定时，显示手机号输入框
      this.showPhoneInput();
    }
  },

  // 显示手机号输入框
  showPhoneInput() {
    console.log('显示手机号绑定选项');
    wx.showActionSheet({
      itemList: ['使用微信绑定的手机号', '手动输入手机号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 使用微信绑定的手机号，直接显示获取手机号按钮
          this.setData({
            showGetPhone: true
          });
        } else {
          // 手动输入手机号
          wx.showModal({
            title: '绑定手机号',
            editable: true,
            placeholderText: '请输入手机号',
            success: (res) => {
              console.log('手机号输入结果:', res);
              if (res.confirm && res.content) {
                const phoneNumber = res.content;
                if (/^1[3-9]\d{9}$/.test(phoneNumber)) {
                  // 发送验证码
                  this.sendVerifyCode(phoneNumber);
                } else {
                  wx.showToast({
                    title: '手机号格式不正确',
                    icon: 'none'
                  });
                }
              }
            }
          });
        }
      }
    });
  },

  // 获取微信绑定的手机号
  getPhoneNumber(e) {
    console.log('获取手机号回调:', e);
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      wx.showLoading({
        title: '获取手机号...',
        mask: true
      });
      // 获取到cloudID，调用云函数解密
      wx.cloud.callFunction({
        name: 'bindPhone',
        data: {
          action: 'getWxPhone',
          cloudID: e.detail.cloudID
        }
      }).then(res => {
        wx.hideLoading();
        console.log('获取手机号结果:', res);
        if (res.result && res.result.success) {
          const phoneNumber = res.result.phoneNumber;
          // 发送验证码
          this.sendVerifyCode(phoneNumber);
        } else {
          wx.showToast({
            title: '获取手机号失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        console.error('获取手机号失败:', err);
        wx.showToast({
          title: '获取手机号失败',
          icon: 'none'
        });
      }).finally(() => {
        // 隐藏获取手机号按钮
        this.setData({
          showGetPhone: false
        });
      });
    } else {
      // 用户取消了授权
      this.setData({
        showGetPhone: false
      });
    }
  },

  // 发送验证码
  sendVerifyCode(phoneNumber) {
    console.log('开始发送验证码, 手机号:', phoneNumber);
    wx.showLoading({
      title: '发送验证码...',
      mask: true
    });

    wx.cloud.callFunction({
      name: 'bindPhone',
      data: {
        action: 'sendCode',
        phoneNumber: phoneNumber
      }
    }).then(res => {
      console.log('发送验证码结果:', res);
      wx.hideLoading();
      if (res.result && res.result.success) {
        // 直接使用返回的验证码进行绑定
        this.verifyAndBindPhone(phoneNumber, res.result.verifyCode);
      } else {
        wx.showToast({
          title: res.result ? res.result.message : '发送失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('发送验证码失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    });
  },

  // 显示验证码输入框
  showVerifyCodeInput(phoneNumber) {
    wx.showModal({
      title: '请输入验证码',
      editable: true,
      placeholderText: '请输入6位验证码',
      success: (res) => {
        if (res.confirm && res.content) {
          const verifyCode = res.content;
          if (/^\d{6}$/.test(verifyCode)) {
            // 验证并绑定手机号
            this.verifyAndBindPhone(phoneNumber, verifyCode);
          } else {
            wx.showToast({
              title: '验证码格式不正确',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 验证并绑定手机号
  verifyAndBindPhone(phoneNumber, verifyCode) {
    wx.showLoading({
      title: '正在验证...',
      mask: true
    });

    wx.cloud.callFunction({
      name: 'bindPhone',
      data: {
        action: 'verifyAndBind',
        phoneNumber: phoneNumber,
        verifyCode: verifyCode
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result && res.result.success) {
        this.setData({
          isPhoneBound: true,
          'userInfo.phoneNumber': phoneNumber
        });
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.result ? res.result.message : '验证失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '验证失败',
        icon: 'none'
      });
    });
  },

  // 解绑手机号
  unbindPhone() {
    wx.showLoading({
      title: '正在解绑...',
      mask: true
    });

    wx.cloud.callFunction({
      name: 'bindPhone',
      data: {
        action: 'unbind'
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result && res.result.success) {
        this.setData({
          isPhoneBound: false,
          'userInfo.phoneNumber': null
        });
        wx.showToast({
          title: '解绑成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.result ? res.result.message : '解绑失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '解绑失败',
        icon: 'none'
      });
    });
  },

  // 选择数据 (修正逻辑)
  selectData: function(e) {
    // 确保在多选模式下才执行
    if (!this.data.isMultiSelectMode) {
      console.log("非多选模式，不执行 selectData");
      return;
    }
    
    // 从dataset中获取当前显示列表 (filteredStoredData) 的索引
    let index = e.currentTarget.dataset.index; 
    
    // 确保 index 是有效的数字
    index = Number(index);
    if (isNaN(index) || index < 0 || index >= this.data.filteredStoredData.length) {
      console.error('selectData：无效的数据索引:', index, ' (filteredStoredData length:', this.data.filteredStoredData.length, ')');
      return;
    }
    
    let filteredData = [...this.data.filteredStoredData];
    let selectedIndexes = [...this.data.selectedDataIndexes];
    
    // 切换选中项在 filteredStoredData 中的选中状态
    filteredData[index].selected = !filteredData[index].selected;
    
    console.log(`选中/取消选中 filteredStoredData[${index}]，新状态: ${filteredData[index].selected}`);

    // 更新 selectedDataIndexes 数组 (存储的是 filteredStoredData 中的索引)
    const indexPosition = selectedIndexes.indexOf(index);
    if (filteredData[index].selected) {
      // 如果选中且不在数组中，则添加
      if (indexPosition === -1) {
        selectedIndexes.push(index);
      }
    } else {
      // 如果取消选中且在数组中，则移除
      if (indexPosition !== -1) {
        selectedIndexes.splice(indexPosition, 1);
      }
    }
    
    // 更新页面数据：只更新 filteredStoredData 和 selectedDataIndexes
    this.setData({
      // `filteredStoredData[${index}].selected`: filteredData[index].selected, // 优化：只更新变化的项 --> 这种局部更新有时在列表渲染时可能不生效，改为整体更新
      filteredStoredData: filteredData, // 整体更新 filteredStoredData 以确保视图刷新
      selectedDataIndexes: selectedIndexes
    });
    
    console.log('选择后状态 (基于 filteredStoredData):', {
      selectedCount: selectedIndexes.length,
      selectedIndexes: selectedIndexes,
      // selectedStatus: filteredData.map(item => item.selected) // 可选：完整状态日志
    });

    // --- 重要：如果需要根据 filteredStoredData 的选择去同步更新 storedData ---
    // --- 可以取消下方注释，但这会增加复杂性，且在过滤列表变化时需要重新同步 ---
    /*
    const clickedItem = filteredData[index];
    const originalIndex = this.data.storedData.findIndex(item => 
        (item._id && item._id === clickedItem._id) || 
        (item.sessionId && item.sessionId === clickedItem.sessionId)
    );
    if (originalIndex !== -1) {
        this.setData({
            [`storedData[${originalIndex}].selected`]: clickedItem.selected
        });
        console.log(`同步更新 storedData[${originalIndex}] 的选中状态`);
    } else {
        console.warn("无法在 storedData 中找到对应的项进行同步");
    }
    */
  },

  // 生成图表
  generateChart: async function(e) {
    if (this.isGenerating) return;
    this.isGenerating = true;

    try {
      const selectedData = this.data.storedData
        .filter((item, index) => this.data.selectedDataIndexes.includes(index))
        .map(item => ({
          tcValue: parseFloat(item.tcValue),
          concentration: parseFloat(item.concentration)
        }))
        .sort((a, b) => a.concentration - b.concentration);

      if (selectedData.length === 0) {
        wx.showToast({ title: '请选择数据', icon: 'none' });
        return;
      }

      await new Promise(resolve => {
        this.setData({ showChart: true }, resolve);
      });
      
      const query = wx.createSelectorQuery();
      const canvas = await new Promise((resolve, reject) => {
        query.select('#tcChart')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0] && res[0].node) resolve(res[0]);
            else reject(new Error('获取Canvas节点失败'));
          });
      });

      const canvasNode = canvas.node;
      const ctx = canvasNode.getContext('2d');
      
      // 获取设备像素比
      const dpr = wx.getSystemInfoSync().pixelRatio;
      
      // 设置画布的实际渲染尺寸
      const width = canvas.width * 2;  // 将画布尺寸放大2倍以提高清晰度
      const height = canvas.height * 2;
      canvasNode.width = width * dpr;
      canvasNode.height = height * dpr;
      
      // 缩放绘图上下文以适应实际显示
      ctx.scale(dpr * 2, dpr * 2);  // 因为画布尺寸放大了2倍，所以这里也要相应调整

      // 设置抗锯齿
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // 设置字体和颜色
      const fontFamily = 'PingFang SC';
      const axisColor = '#333333';
      const gridColor = '#EEEEEE';
      const textColor = '#666666';

      // 调整边距以优化文字布局，增加空间防止文字重叠
      const padding = {
        top: 40,    // 增加顶部边距
        right: 30,  // 增加右侧边距
        bottom: 60, // 增加底部边距
        left: 70    // 增加左侧边距，为Y轴标题和刻度留出更多空间
      };

      const chartWidth = width / 2 - padding.left - padding.right;
      const chartHeight = height / 2 - padding.top - padding.bottom;

      // 清空画布
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width / 2, height / 2);

      // 绘制X轴标题，提前绘制
      ctx.fillStyle = axisColor;
      ctx.font = 'bold 13px ' + fontFamily;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillText('[LgE] / ng mL⁻¹', padding.left + chartWidth / 2, height / 2 - 15);

      // 定义坐标轴范围
      const xMax = 1000;
      const yMax = 1.0;

      // 计算比例尺
      const xScale = chartWidth / xMax;
      const yScale = chartHeight / yMax;

      // 绘制网格线和刻度
      ctx.lineWidth = 0.5;
      ctx.font = '11px ' + fontFamily;  // 稍微减小字体大小
      
      // 绘制Y轴网格线和刻度
      for (let i = 0; i <= 10; i++) {
        const value = i * 0.1;
        const y = padding.top + chartHeight - (value * yScale);

        // 网格线
        ctx.beginPath();
        ctx.strokeStyle = gridColor;
        ctx.setLineDash([4, 4]);
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + chartWidth, y);
        ctx.stroke();
        ctx.setLineDash([]);

        // 刻度值，向左偏移更多以避免重叠
        ctx.fillStyle = textColor;
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
        ctx.fillText(value.toFixed(1), padding.left - 10, y);
      }

      // 绘制X轴网格线和刻度
      for (let i = 0; i <= 5; i++) {
        const value = i * 200;
        const x = padding.left + (value * xScale);

        // 网格线
        ctx.beginPath();
        ctx.strokeStyle = gridColor;
        ctx.setLineDash([4, 4]);
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + chartHeight);
        ctx.stroke();
        ctx.setLineDash([]);

        // 刻度值，向下偏移更多以避免重叠
        ctx.fillStyle = textColor;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        ctx.fillText(value.toString(), x, padding.top + chartHeight + 10);
      }

      // 绘制坐标轴
      ctx.beginPath();
      ctx.strokeStyle = axisColor;
      ctx.lineWidth = 1;
      // Y轴
      ctx.moveTo(padding.left, padding.top);
      ctx.lineTo(padding.left, padding.top + chartHeight);
      // X轴
      ctx.moveTo(padding.left, padding.top + chartHeight);
      ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
      ctx.stroke();

      // 绘制Y轴标题，位置调整以避免与刻度重叠
      ctx.save();
      ctx.translate(padding.left - 45, padding.top + chartHeight / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.fillStyle = axisColor;
      ctx.font = 'bold 13px ' + fontFamily;  // 稍微减小标题字体
      ctx.textAlign = 'center';
      ctx.fillText('T/C比例', 0, 0);
      ctx.restore();

      // 绘制数据线
      if (selectedData.length > 0) {
        // 计算所有数据点的坐标
        const points = selectedData.map(point => ({
          x: padding.left + point.concentration * xScale,
          y: padding.top + chartHeight - point.tcValue * yScale
        }));

        // 绘制平滑曲线
        ctx.beginPath();
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 1.2;  // 减小线条粗细使其更精致
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // 使用Cardinal样条插值方法绘制更自然的曲线
        ctx.moveTo(points[0].x, points[0].y);
        
        for (let i = 0; i < points.length - 1; i++) {
          const curr = points[i];
          const next = points[i + 1];
          
          // 计算控制点
          const smoothing = 0.2;  // 减小平滑系数，使曲线更接近实际数据点
          
          // 计算切线向量
          let tx, ty;
          if (i > 0) {
            // 使用前一个点来计算切线
            tx = (next.x - points[i-1].x) * smoothing;
            ty = (next.y - points[i-1].y) * smoothing;
          } else {
            // 第一个点使用相邻两点的差值
            tx = (next.x - curr.x) * smoothing;
            ty = (next.y - curr.y) * smoothing;
          }
          
          // 计算下一个点的切线
          let tx2, ty2;
          if (i < points.length - 2) {
            // 使用后一个点来计算切线
            tx2 = (points[i+2].x - curr.x) * smoothing;
            ty2 = (points[i+2].y - curr.y) * smoothing;
          } else {
            // 最后一个点使用相邻两点的差值
            tx2 = (next.x - curr.x) * smoothing;
            ty2 = (next.y - curr.y) * smoothing;
          }
          
          // 绘制三次贝塞尔曲线
          ctx.bezierCurveTo(
            curr.x + tx, curr.y + ty,
            next.x - tx2, next.y - ty2,
            next.x, next.y
          );
        }
        
        ctx.stroke();

        // 绘制数据点
        points.forEach(point => {
          // 绘制外圈
          ctx.beginPath();
          ctx.fillStyle = '#FFFFFF';
          ctx.strokeStyle = '#4A90E2';
          ctx.lineWidth = 1;
          ctx.arc(point.x, point.y, 2.5, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();

          // 绘制内圈
          ctx.beginPath();
          ctx.fillStyle = '#4A90E2';
          ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
          ctx.fill();
        });
      }

    } catch (error) {
      console.error('绘制图表失败:', error);
      wx.showToast({
        title: '绘制图表失败',
        icon: 'none'
      });
    } finally {
      this.isGenerating = false;
    }
  },

  // 关闭图表
  closeChart: function() {
    this.setData({
      showChart: false,
      chartScale: 1,
      translateX: 0,
      translateY: 0
    });
  },

  // 重置图表视图
  resetChartView: function() {
    this.setData({
      chartScale: 1,
      translateX: 0,
      translateY: 0
    });
  },

  // 处理触摸开始
  touchStart: function(e) {
    if (e.touches.length === 2) {
      // 双指触摸，准备缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      this.startDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      this.startScale = this.data.chartScale;
    } else if (e.touches.length === 1) {
      // 单指触摸，准备平移
      // --- MODIFICATION START ---
      // 检查是否存在有效的触摸点
      if (e.touches[0]) {
        this.setData({
          isDragging: true,
          lastX: e.touches[0].clientX,
          lastY: e.touches[0].clientY
        });
      }
      // --- MODIFICATION END ---
    }
  },

  // 处理触摸移动
  touchMove: function(e) {
    if (e.touches.length === 2) {
      // 双指缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      let newScale = this.startScale * (currentDistance / this.startDistance);
      newScale = Math.min(Math.max(newScale, 0.5), 5); // 限制缩放范围
      
      this.setData({
        chartScale: newScale
      });
    } else if (e.touches.length === 1 && this.data.isDragging) {
      // 单指平移
      // --- MODIFICATION START ---
      // 检查是否存在有效的触摸点和上一次的位置
      if (e.touches[0] && typeof this.data.lastX === 'number' && typeof this.data.lastY === 'number') {
        const deltaX = e.touches[0].clientX - this.data.lastX;
        const deltaY = e.touches[0].clientY - this.data.lastY;
        
        this.setData({
          translateX: this.data.translateX + deltaX,
          translateY: this.data.translateY + deltaY,
          lastX: e.touches[0].clientX,
          lastY: e.touches[0].clientY
        });
      }
      // --- MODIFICATION END ---
    }
  },

  // 处理触摸结束
  touchEnd: function() {
    this.setData({
      isDragging: false
    });
  },

  // 处理鼠标滚轮
  handleMouseWheel: function(e) {
    if (e.ctrlKey) {
      // 阻止默认缩放
      e.preventDefault();
      
      const rect = e.currentTarget.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      // 计算新的缩放比例
      const delta = e.deltaY || e.detail.deltaY;
      let newScale = this.data.chartScale * (1 - delta / 1000);
      newScale = Math.min(Math.max(newScale, 0.5), 5);
      
      this.setData({
        chartScale: newScale
      });
    }
  },

  // 删除选中的数据
  deleteSelectedData() {
    const that = this;
    const selectedIndexes = [...this.data.selectedDataIndexes];

    if (selectedIndexes.length === 0) {
      wx.showToast({
        title: '请先选择要删除的数据',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedIndexes.length} 条数据吗？删除后无法恢复。`,
      confirmColor: '#FF3B30', // 红色确认按钮
      success(res) {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          // 收集选中项的唯一ID (_id优先，其次sessionId)
          const idsToDelete = selectedIndexes.map(index => {
            const item = that.data.filteredStoredData[index];
            // 添加详细的日志，帮助调试
            if (item) {
              console.log(`数据项[${index}]详情:`, {
                _id: item._id,
                sessionId: item.sessionId,
                _id类型: typeof item._id,
                sessionId类型: typeof item.sessionId,
                _id长度: item._id ? item._id.length : 'undefined',
                sessionId长度: item.sessionId ? item.sessionId.length : 'undefined'
              });
              // 添加原始数据的完整ID和sessionId日志
              console.log(`原始数据项[${index}] _id:`, item._id);
              console.log(`原始数据项[${index}] sessionId:`, item.sessionId);
              
              // 返回_id优先，其次sessionId
              return item._id || item.sessionId;
            }
            return null;
          }).filter(id => !!id); // 过滤掉无效的ID

          if (idsToDelete.length !== selectedIndexes.length) {
            console.warn('部分选中项无法获取ID，可能数据不一致');
          }

          if (idsToDelete.length === 0) {
            wx.hideLoading();
            wx.showToast({
              title: '无法获取选中项ID',
              icon: 'none'
            });
            console.error('未能从选中项中提取有效的 _id 或 sessionId');
            return;
          }

          console.log('准备调用云函数删除数据，IDs:', idsToDelete);

          // 调用云函数执行删除
          wx.cloud.callFunction({
            name: 'deleteUserData', // 你需要创建这个云函数
            data: {
              idsToDelete: idsToDelete
            },
            success: (cloudRes) => {
              wx.hideLoading();
              console.log('删除云函数返回:', cloudRes);
              
              // 检查云函数是否真正执行了删除操作
              if (cloudRes.result && cloudRes.result.success) {
                // 检查实际删除的记录数量
                const deletedCount = cloudRes.result.deletedCount || 0;
                const fileStatus = cloudRes.result.fileDeletionStatus || {};
                const deletedFiles = fileStatus.deletedFiles || 0;
                const totalFiles = fileStatus.totalFiles || 0;
                
                if (deletedCount > 0) {
                  // 实际删除了记录，更新UI并显示成功提示
                  let successMessage = `成功删除 ${deletedCount} 条数据`;
                  
                  // 如果有文件删除信息，添加到提示中
                  if (totalFiles > 0) {
                    successMessage += `，${deletedFiles}/${totalFiles} 个文件`;
                  }
                  
                  wx.showToast({
                    title: successMessage,
                    icon: 'success',
                    duration: 2000
                  });
                  
                  // 延迟后重新加载数据，确保云端数据同步到前端
                  setTimeout(() => {
                    // 重新调用showMyData函数加载数据，确保数据完全刷新
                    that.showMyData();
                  }, 2000);
                } else {
                  // 云函数执行成功但没有删除任何记录
                  wx.showToast({
                    title: '未找到要删除的数据',
                    icon: 'none',
                    duration: 2000
                  });
                  
                  // 也需要重新加载数据，确保前端状态与后端一致
                  setTimeout(() => {
                    that.showMyData();
                  }, 1500);
                }
              } else {
                // 云函数返回失败
                wx.showToast({
                  title: cloudRes.result?.message || '删除失败',
                  icon: 'none',
                  duration: 2000
                });
                
                // 删除失败也需要刷新数据，确保前端状态与后端一致
                setTimeout(() => {
                  that.showMyData();
                }, 1500);
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('调用删除云函数失败:', err);
              wx.showToast({
                title: '删除失败，请检查网络后重试',
                icon: 'none',
                duration: 2000
              });
              
              // 出错也需要刷新数据
              setTimeout(() => {
                that.showMyData();
              }, 1500);
            }
          });
        }
      }
    });
  },

  // 显示参数详情
  showParamDetail(e) {
    if (this.data.isMultiSelectMode) return;
    console.log('显示参数详情:', e);
    
    // 获取完整的数据项
    const item = e.currentTarget.dataset.item;
    
    // 设置参数数据，包括基础指标和新的18个参数设置
    const defaultParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature_auto: 0,
      gain: 0,
      power_line_frequency: 2,
      white_balance_temperature: 4650,
      sharpness: 10,
      exposure_auto: 3,
      exposure_absolute: 1250,
      pan_absolute: 0,
      tilt_absolute: 0,
      focus_absolute: 0,
      zoom_absolute: 100,
      camera_move_speed: 100,

      setVoltage: 12
    };
    
    // 记录原始数据结构，帮助调试
    console.log('参数详情原始数据:', {
      hasVideoParameters: !!item.videoParameters,
      hasParameters: !!item.parameters,
      videoParametersKeys: item.videoParameters ? Object.keys(item.videoParameters) : [],
      parametersKeys: item.parameters ? Object.keys(item.parameters) : []
    });
    
    // 修正参数合并逻辑
    const paramData = {
      ...defaultParams, // 1. 应用所有默认值
      ...(item.parameters || {}), // 2. 用存储的常规参数覆盖
      ...(item.videoParameters || {}), // 3. 用视频参数再次覆盖（最高优先级）
      ...item, // 4. 最后应用数据项本身的基础属性（如 tcValue, _id 等）
    };
    
    const paramTitle = e.currentTarget.dataset.title;
    const paramTime = e.currentTarget.dataset.time;
    
    // 先重置滚动位置，确保详情从顶部开始显示
    this.setData({
      'paramScrollTop': 0
    });
    
    // 重要：先仅设置内容数据，还不显示卡片
    this.setData({
      currentParamData: paramData,
      currentParamTitle: paramTitle,
      currentParamTime: paramTime,
      paramDetailHiding: false,
      paramDetailMaskHiding: false
    });
    
    console.log('设置的参数数据:', paramData);
    
    // 使用两步式显示，确保DOM更新后再触发动画
    // 第一步：给浏览器时间渲染内容
    setTimeout(() => {
      // 第二步：显示卡片
      this.setData({
        showParamDetail: true
      });
      
      // 使用nextTick确保DOM渲染后再触发动画
      wx.nextTick(() => {
        // 触发卡片的弹出动画
        this.setData({
          // 直接应用动画，而不是通过类切换
          'paramDetailCardAnimation': 'card-pop-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards'
        });
      });
    }, 50);
  },
  
  // 隐藏参数详情
  hideParamDetail() {
    console.log('隐藏参数详情卡片');
    // 先设置隐藏动画的状态，但保持组件可见
    this.setData({
      paramDetailMaskHiding: true, // 开始隐藏遮罩层
      paramDetailHiding: true // 同时开始隐藏详情卡片
    });
    
    // 确保动画可以正确触发
    wx.nextTick(() => {
      // 等待动画完成后再隐藏组件，确保动画有足够的时间完成
    setTimeout(() => {
        console.log('参数详情卡片动画完成，隐藏组件');
      this.setData({
        showParamDetail: false,
        paramDetailHiding: false,
        paramDetailMaskHiding: false
      });
      }, 350); // 确保与CSS中simpleScaleOut动画时长匹配(0.3s + 50ms缓冲)
    });
  },

  // 防止冒泡
  preventBubble: function(e) {
    if (!e) return;
    
    // 小程序中阻止事件冒泡的方法
    // 1. 尝试使用标准 DOM 方法
    if (typeof e.stopPropagation === 'function') {
      try {
        e.stopPropagation();
      } catch (error) {
        console.log('标准阻止冒泡方法失败:', error);
      }
    }
    
    // 2. 小程序特有事件对象可能需要设置特定属性
    try {
      // 有些小程序事件需要设置这个属性来阻止冒泡
      if (e.detail && typeof e.detail === 'object') {
        e.detail.bubbles = false;
      }
      
      // 标记已处理，防止冒泡
      e._isPreventDefault = true;
    } catch (error) {
      console.log('设置事件属性失败:', error);
    }
  },

  // 启用多选模式
  enableMultiSelect: function(e) {
    console.log('启用多选模式');
    const index = parseInt(e.currentTarget.dataset.index);

    // --- MODIFICATION START ---
    // Ensure index is valid for filteredStoredData
    if (isNaN(index) || index < 0 || index >= this.data.filteredStoredData.length) {
      console.error('enableMultiSelect: Invalid index', index);
      return;
    }

    // Operate on copies
    let filteredData = [...this.data.filteredStoredData];
    let storedDataCopy = [...this.data.storedData];
    const targetItemId = filteredData[index]._id || filteredData[index].sessionId;

    // Reset all selections in both arrays first for a clean state
    filteredData = filteredData.map(item => ({ 
      ...item, 
      selected: false, 
      videoSelected: false, 
      imageSelected: false 
    }));
    storedDataCopy = storedDataCopy.map(item => ({ 
      ...item, 
      selected: false, 
      videoSelected: false, // Also reset media selection in storedData for consistency
      imageSelected: false 
    }));

    // Mark the target item as selected in filteredData
    filteredData[index].selected = true;

    // Find and mark the target item as selected in storedDataCopy
    const originalIndexInStoredData = storedDataCopy.findIndex(item => 
        (item._id && item._id === targetItemId) || 
        (item.sessionId && item.sessionId === targetItemId)
    );
    if (originalIndexInStoredData !== -1) {
      storedDataCopy[originalIndexInStoredData].selected = true;
    } else {
      console.warn('enableMultiSelect: Could not find corresponding item in storedData to mark as selected.');
    }

    this.setData({
      isMultiSelectMode: true,
      filteredStoredData: filteredData, // Update filtered data with the selection
      storedData: storedDataCopy,       // Update stored data with the selection
      selectedDataIndexes: [index],     // Initialize with the index from filteredData
      selectedVideoIndexes: [],
      selectedImageIndexes: [],
      canExport: false  // Reset export button status
    }, () => {
      console.log('多选模式已启用:', {
        isMultiSelectMode: this.data.isMultiSelectMode,
        selectedDataIndexes: this.data.selectedDataIndexes,
        selectedVideoIndexes: this.data.selectedVideoIndexes,
        selectedImageIndexes: this.data.selectedImageIndexes
      });
    });
    // --- MODIFICATION END ---
  },

  // 进入多选模式
  enterMultiSelectMode: function() {
    console.log('进入多选模式');
    
    // 开始动画过渡
    this.setData({
      modeTransition: true
    });
    
    // 等待淡出动画完成 - 延长时间为600ms以匹配CSS动画
    setTimeout(() => {
      // 重置所有选择状态
      let storedDataCopy = this.data.storedData.map(item => ({ 
        ...item, 
        selected: false, 
        videoSelected: false, 
        imageSelected: false 
      }));
      
      // 同时也要重置filteredStoredData
      let filteredDataCopy = this.data.filteredStoredData.map(item => ({ 
        ...item, 
        selected: false, 
        videoSelected: false, 
        imageSelected: false 
      }));
      
      // 设置多选模式状态 - 确保isMultiSelectMode为true
      this.setData({
        isMultiSelectMode: true,
        storedData: storedDataCopy,
        filteredStoredData: filteredDataCopy,
        selectedDataIndexes: [],
        selectedVideoIndexes: [],
        selectedImageIndexes: [],
        canExport: false
      }, () => {
        // 为了与退出多选模式保持动画一致性，使用相同的时序
        wx.nextTick(() => {
          // 延长延迟时间至300ms，使过渡更加平滑
          setTimeout(() => {
            // 移除过渡状态，淡入内容
            this.setData({
              modeTransition: false
            });
            
            // 给DOM一点时间处理modeTransition的变化，然后再启用动画
            setTimeout(() => {
              // 设置animateItems为true，开始元素淡入动画
              this.setData({
                animateItems: true
              });
            }, 50);
          }, 300);
        });
      });
    }, 600);
  },

  // 退出多选模式
  exitMultiSelectMode: function() {
    // 先移除数据项动画类并立即开始过渡动画（不再有初始延迟）
    this.setData({
      animateItems: false,
      modeTransition: true
    });
    
    // 等待淡出动画完成，延长时间让所有数据项完成淡出动画
    setTimeout(() => {
      let storedData = this.data.storedData.map(item => ({
        ...item,
        selected: false,
        videoSelected: false,
        imageSelected: false
      }));
      
      // 同时也要重置filteredStoredData
      let filteredData = this.data.filteredStoredData.map(item => ({
        ...item,
        selected: false,
        videoSelected: false,
        imageSelected: false
      }));
      
      // 重置多选模式状态
      this.setData({
        isMultiSelectMode: false,
        storedData: storedData,
        filteredStoredData: filteredData,
        selectedDataIndexes: [],
        selectedVideoIndexes: [],
        selectedImageIndexes: [],
        canExport: false
      }, () => {
        // 在状态设置完成后添加延迟再恢复显示，使过渡更流畅
        wx.nextTick(() => {
          // 延迟时间与进入多选模式保持一致
          setTimeout(() => {
            // 移除过渡效果，让搜索框的动画能够正常工作
            this.setData({
              modeTransition: false
            });
            
            // 让搜索框重新应用动画类
            // 因为搜索框有CSS中设置的动画，所以只需要重新触发渲染即可
            setTimeout(() => {
              // 这个空的setData会触发微信小程序的重新渲染
              // 让CSS中定义的搜索框动画生效
              this.setData({});
            }, 50);
          }, 300);
        });
      });
    }, 650); // 延长淡出时间，使动画更加流畅
  },

  // 显示导出选项
  showExportOptions: function() {
    console.log('显示导出选项');
    this.setData({
      showExportOptions: true
    });
  },

  // 处理导出数据
  handleExportData: function() {
    if (!this.data.canExport) {
      wx.showToast({
        title: '请先选择要导出的内容',
        icon: 'none'
      });
      return;
    }

    // 显示导出选项卡片
    this.setData({
      showExportOptions: true
    });
  },

  // 隐藏导出选项卡片
  hideExportOptions() {
    console.log('隐藏导出选项');
    
    // 重置状态标志，防止因为快速点击导致状态混乱
    this.isPreparingShare = false;
    this.isSaving = false;
    
    this.setData({
      showExportOptions: false
    });
  },

  // 分享到微信
  shareToWechat: function() {
    console.log('shareToWechat被调用');
    
    // 防止重复触发或与保存功能冲突
    if (this.isPreparingShare || this.isSaving) {
      console.log('正在准备分享或保存中，跳过重复触发');
      return;
    }
    
    this.isPreparingShare = true;
    this.isSaving = false; // 确保保存功能不会同时触发
    wx.showLoading({ title: '正在准备链接...', mask: true });

    try {
      // 选中的媒体项计数
      let selectedMediaCount = 0;
      // 已准备好URL的计数
      let preparedUrlCount = 0;
      // 收集的URL
      let urlsToShare = [];
      
      console.log("开始处理分享，选中的数据索引:", this.data.selectedDataIndexes);

      // 首先计算需要获取的媒体项总数
      for (const dataIndex of this.data.selectedDataIndexes) {
        if (dataIndex < 0 || dataIndex >= this.data.filteredStoredData.length) continue;
        
        const item = this.data.filteredStoredData[dataIndex];
        if (!item) continue;
        
        if (item.videoSelected) selectedMediaCount++;
        if (item.imageSelected) {
          // C区和T区图片分别计算
          if (item.cAreaImage) selectedMediaCount++;
          if (item.tAreaImage) selectedMediaCount++;
        }
      }

      // 如果没有选中任何媒体项，直接结束
      if (selectedMediaCount === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '请先选择要分享的视频或图片和数值',
          icon: 'none',
          duration: 2000
        });
        this.hideExportOptions(); // 关闭导出选项
        this.isPreparingShare = false;
        return;
      }

      console.log(`共有${selectedMediaCount}个媒体项需要获取URL`);
      
      // 处理完成回调函数
      const onPrepareComplete = () => {
        preparedUrlCount++;
        console.log(`已准备URL: ${preparedUrlCount}/${selectedMediaCount}`);
        
        // 当所有媒体项都已处理完成
        if (preparedUrlCount >= selectedMediaCount) {
          wx.hideLoading();
          this.isPreparingShare = false;
          this.isSaving = false; // 确保保存功能标志也被重置
          
          // 如果无法获取任何URL
          if (urlsToShare.length === 0) {
            wx.showToast({
              title: '未能获取任何可分享的内容',
              icon: 'none',
              duration: 2000
            });
            this.hideExportOptions();
            return;
          }
          
          // 构建分享文本
          let shareText = "为您分享了以下分析结果：\n";
          urlsToShare.forEach(entry => {
            shareText += `\n📊 ${entry.name}\n`;
            shareText += `🔢 数值：C区=${entry.cValue || 'N/A'}, T区=${entry.tValue || 'N/A'}, T/C=${entry.tcValue || 'N/A'}\n`;
            shareText += `🔗 链接：${entry.url}\n`;
          });
          
          // 复制到剪贴板
          wx.setClipboardData({
            data: shareText,
            success: () => {
              this.isPreparingShare = false; // 再次确保标志重置
              this.isSaving = false;
              wx.showModal({
                title: '分析结果已复制',
                content: '所有选中的分析结果（包含C/T区数值和媒体链接）已复制到剪贴板，请您手动粘贴并发送给微信好友。',
                showCancel: false,
                confirmText: '知道了',
                complete: () => {
                  this.hideExportOptions();
                }
              });
            },
            fail: (err) => {
              this.isPreparingShare = false; // 失败时也要重置标志
              this.isSaving = false;
              console.error("复制到剪贴板失败: ", err);
              wx.showToast({
                title: '复制分析结果失败，请重试',
                icon: 'none',
                duration: 2000,
                complete: () => {
                  this.hideExportOptions();
                }
              });
            }
          });
        }
      };
      
      // 处理每个选中的项
      for (const dataIndex of this.data.selectedDataIndexes) {
        if (dataIndex < 0 || dataIndex >= this.data.filteredStoredData.length) {
          console.warn(`shareToWechat: 无效的数据索引 ${dataIndex}，已跳过。`);
          continue;
        }
        
        const item = this.data.filteredStoredData[dataIndex];
        if (!item) {
          console.warn(`shareToWechat: 在 filteredStoredData 中未找到索引为 ${dataIndex} 的数据项，已跳过。`);
          continue;
        }

        const itemName = item.customName || item.displayTime || `数据条目 ${dataIndex + 1}`;
        console.log(`处理数据项: ${itemName}`);
        
        // 处理视频
        if (item.videoSelected) {
          this.getMediaUrlForSharing('video', item, (videoUrl) => {
            if (videoUrl) {
              urlsToShare.push({
                name: `${itemName} - 视频`,
                url: videoUrl,
                cValue: item.cAreaValue || 'N/A',
                tValue: item.tAreaValue || 'N/A',
                tcValue: item.tcValue || 'N/A'
              });
              console.log(`  已添加视频链接: ${videoUrl}`);
            } else {
              console.warn(`  ${itemName} 的视频URL获取失败`);
            }
            onPrepareComplete();
          });
        }

        // 处理图片
        if (item.imageSelected) {
          // 处理C区图片
          if (item.cAreaImage) {
            this.getMediaUrlForSharing('cAreaImage', item, (imageUrl) => {
              if (imageUrl) {
                urlsToShare.push({
                  name: `${itemName} - C区图片`,
                  url: imageUrl,
                  cValue: item.cAreaValue || 'N/A',
                  tValue: item.tAreaValue || 'N/A',
                  tcValue: item.tcValue || 'N/A'
                });
                console.log(`  已添加C区图片链接: ${imageUrl}`);
              } else {
                console.warn(`  ${itemName} 的C区图片URL获取失败`);
              }
              onPrepareComplete();
            });
          }

          // 处理T区图片
          if (item.tAreaImage) {
            this.getMediaUrlForSharing('tAreaImage', item, (imageUrl) => {
              if (imageUrl) {
                urlsToShare.push({
                  name: `${itemName} - T区图片`,
                  url: imageUrl,
                  cValue: item.cAreaValue || 'N/A',
                  tValue: item.tAreaValue || 'N/A',
                  tcValue: item.tcValue || 'N/A'
                });
                console.log(`  已添加T区图片链接: ${imageUrl}`);
              } else {
                console.warn(`  ${itemName} 的T区图片URL获取失败`);
              }
              onPrepareComplete();
            });
          }
        }
      }

    } catch (error) {
      wx.hideLoading();
      console.error('处理分享链接时发生错误:', error);
      // 确保重置所有状态标志
      this.isPreparingShare = false;
      this.isSaving = false;
      wx.showToast({
        title: '准备分享内容失败',
        icon: 'none',
        duration: 2000
      });
      this.hideExportOptions();
    }
  },

  // 格式化数值显示
  formatValue: function(value) {
    if (value === null || value === undefined || value === 'N/A') {
      return 'N/A';
    }

    // 如果是数字，保留合适的小数位数
    if (typeof value === 'number') {
      if (value === 0) return '0';
      if (value < 0.01) return value.toExponential(2);
      if (value < 1) return value.toFixed(4);
      if (value < 100) return value.toFixed(2);
      return Math.round(value).toString();
    }

    // 如果是字符串，尝试转换为数字再格式化
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      return this.formatValue(numValue);
    }

    return value.toString();
  },

  // 获取媒体URL专用方法
  getMediaUrlForSharing: function(mediaType, item, callback) {
    console.log(`获取${mediaType}的URL用于分享`);
    
    // 检查mediaType类型
    if (mediaType === 'video') {
      // 先检查是否有直接可用的URL
      if (item.videoUrl) {
        callback(item.videoUrl);
        return;
      }
      
      // 获取视频路径
      const videoPath = item.videoFile || item.videoPath;
      const sessionId = item.groupName || item.sessionId;
      
      if (!videoPath && !sessionId) {
        callback(null);
        return;
      }
      
      // 调用云函数获取视频URL
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getMediaFile',
          mediaType: 'video',
          sessionId: sessionId
        },
        success: (res) => {
          if (res.result && res.result.success && res.result.fileUrl) {
            callback(res.result.fileUrl);
          } else if (videoPath) {
            // 如果云函数失败但有videoPath，尝试使用processFileURL方法
            const processedUrl = this.processFileURL(videoPath);
            callback(processedUrl || null);
          } else {
            callback(null);
          }
        },
        fail: () => {
          // 如果云函数调用失败，尝试使用videoPath
          if (videoPath) {
            const processedUrl = this.processFileURL(videoPath);
            callback(processedUrl || null);
          } else {
            callback(null);
          }
        }
      });
    } 
    // 处理C区图片
    else if (mediaType === 'cAreaImage') {
      // 先检查是否有直接可用的URL
      if (item.cAreaImageUrl) {
        callback(item.cAreaImageUrl);
        return;
      }
      
      const imagePath = item.cAreaImage;
      const sessionId = item.groupName || item.sessionId;
      
      if (!imagePath && !sessionId) {
        callback(null);
        return;
      }
      
      // 调用云函数获取图片URL
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getMediaFile',
          mediaType: 'cAreaImage',
          sessionId: sessionId
        },
        success: (res) => {
          if (res.result && res.result.success && res.result.fileUrl) {
            callback(res.result.fileUrl);
          } else if (imagePath) {
            // 尝试直接获取临时URL
            wx.cloud.getTempFileURL({
              fileList: [this.formatFileID(imagePath)],
              success: (tempRes) => {
                if (tempRes.fileList && tempRes.fileList.length > 0 && 
                    tempRes.fileList[0].tempFileURL) {
                  callback(tempRes.fileList[0].tempFileURL);
                } else {
                  callback(null);
                }
              },
              fail: () => callback(null)
            });
          } else {
            callback(null);
          }
        },
        fail: () => {
          // 如果云函数调用失败但有imagePath，尝试直接获取临时URL
          if (imagePath) {
            wx.cloud.getTempFileURL({
              fileList: [this.formatFileID(imagePath)],
              success: (tempRes) => {
                if (tempRes.fileList && tempRes.fileList.length > 0 && 
                    tempRes.fileList[0].tempFileURL) {
                  callback(tempRes.fileList[0].tempFileURL);
                } else {
                  callback(null);
                }
              },
              fail: () => callback(null)
            });
          } else {
            callback(null);
          }
        }
      });
    }
    // 处理T区图片
    else if (mediaType === 'tAreaImage') {
      // 先检查是否有直接可用的URL
      if (item.tAreaImageUrl) {
        callback(item.tAreaImageUrl);
        return;
      }
      
      const imagePath = item.tAreaImage;
      const sessionId = item.groupName || item.sessionId;
      
      if (!imagePath && !sessionId) {
        callback(null);
        return;
      }
      
      // 调用云函数获取图片URL
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getMediaFile',
          mediaType: 'tAreaImage',
          sessionId: sessionId
        },
        success: (res) => {
          if (res.result && res.result.success && res.result.fileUrl) {
            callback(res.result.fileUrl);
          } else if (imagePath) {
            // 尝试直接获取临时URL
            wx.cloud.getTempFileURL({
              fileList: [this.formatFileID(imagePath)],
              success: (tempRes) => {
                if (tempRes.fileList && tempRes.fileList.length > 0 && 
                    tempRes.fileList[0].tempFileURL) {
                  callback(tempRes.fileList[0].tempFileURL);
                } else {
                  callback(null);
                }
              },
              fail: () => callback(null)
            });
          } else {
            callback(null);
          }
        },
        fail: () => {
          // 如果云函数调用失败但有imagePath，尝试直接获取临时URL
          if (imagePath) {
            wx.cloud.getTempFileURL({
              fileList: [this.formatFileID(imagePath)],
              success: (tempRes) => {
                if (tempRes.fileList && tempRes.fileList.length > 0 && 
                    tempRes.fileList[0].tempFileURL) {
                  callback(tempRes.fileList[0].tempFileURL);
                } else {
                  callback(null);
                }
              },
              fail: () => callback(null)
            });
          } else {
            callback(null);
          }
        }
      });
    } else {
      // 未知的媒体类型
      console.warn(`未知的媒体类型: ${mediaType}`);
      callback(null);
    }
  },

  // 保存到本地相册
  async saveLocally() {
    console.log('saveLocally被调用');

    // 防止重复触发或与分享功能冲突
    if (this.isSaving || this.isPreparingShare) {
      console.log('正在保存或分享中，跳过重复触发');
      return;
    }

    this.isSaving = true;
    this.isPreparingShare = false;

    // 隐藏导出选项，显示进度
    this.hideExportOptions();

    // 初始化进度状态
    this.setData({
      showSaveProgress: true,
      showSaveResults: false,
      saveProgress: {
        percentage: 0,
        text: '正在准备保存...',
        currentTask: ''
      },
      saveResults: {
        videos: [],
        images: [],
        dataFiles: [],
        errors: []
      }
    });

    try {
      // 获取选中的数据
      const selectedItems = this.getSelectedItemsForSave();

      if (selectedItems.length === 0) {
        throw new Error('没有选中任何内容');
      }

      // 计算总任务数
      let totalTasks = 0;
      selectedItems.forEach(item => {
        if (item.videoSelected) totalTasks++;
        if (item.imageSelected) {
          if (item.cAreaImage) totalTasks++;
          if (item.tAreaImage) totalTasks++;
        }
      });

      // 如果有数据，添加一个生成Excel文件的任务
      if (selectedItems.length > 0) totalTasks++;

      let completedTasks = 0;
      const results = {
        videos: [],
        images: [],
        dataFiles: [],
        errors: []
      };

      // 更新进度的函数
      const updateProgress = (taskName = '') => {
        completedTasks++;
        const percentage = Math.round((completedTasks / totalTasks) * 100);
        this.setData({
          'saveProgress.percentage': percentage,
          'saveProgress.text': `正在保存 ${completedTasks}/${totalTasks}`,
          'saveProgress.currentTask': taskName
        });
      };

      // 保存媒体文件
      for (const item of selectedItems) {
        // 使用前端显示的分析时间作为文件名（确保是正确的displayTime）
        const analysisTime = item.displayTime || item.createTime || '未知时间';
        // 将冒号替换为短横线，确保文件名兼容性（2025-06-13 06:45:53 -> 2025-06-13 06-45-53）
        const safeAnalysisTime = analysisTime.replace(/:/g, '-');

        // 保存视频
        if (item.videoSelected) {
          try {
            const videoFileName = `${safeAnalysisTime}_视频`;
            this.setData({
              'saveProgress.currentTask': `正在保存 ${analysisTime} 的视频...`
            });
            await this.saveMediaToAlbum('video', item, videoFileName);
            results.videos.push(videoFileName);
          } catch (error) {
            console.error(`保存视频失败:`, error);
            results.errors.push(`${analysisTime}视频: ${error.message}`);
          }
          updateProgress(`${analysisTime}_视频`);
        }

        // 保存C区图片
        if (item.imageSelected && item.cAreaImage) {
          try {
            const cImageFileName = `${safeAnalysisTime}_C区图片`;
            this.setData({
              'saveProgress.currentTask': `正在保存 ${analysisTime} 的C区图片...`
            });
            await this.saveMediaToAlbum('cAreaImage', item, cImageFileName);
            results.images.push(cImageFileName);
          } catch (error) {
            console.error(`保存C区图片失败:`, error);
            results.errors.push(`${analysisTime}C区图片: ${error.message}`);
          }
          updateProgress(`${analysisTime}_C区图片`);
        }

        // 保存T区图片
        if (item.imageSelected && item.tAreaImage) {
          try {
            const tImageFileName = `${safeAnalysisTime}_T区图片`;
            this.setData({
              'saveProgress.currentTask': `正在保存 ${analysisTime} 的T区图片...`
            });
            await this.saveMediaToAlbum('tAreaImage', item, tImageFileName);
            results.images.push(tImageFileName);
          } catch (error) {
            console.error(`保存T区图片失败:`, error);
            results.errors.push(`${analysisTime}T区图片: ${error.message}`);
          }
          updateProgress(`${analysisTime}_T区图片`);
        }
      }

      // 🔧 条件生成Excel数据文件 - 只有选择了图片时才生成
      const hasImageSelected = selectedItems.some(item => item.imageSelected);
      if (hasImageSelected) {
        try {
          this.setData({
            'saveProgress.currentTask': '正在生成Excel数据文件...'
          });
          // 使用原始时间格式，与视频图片命名保持一致
          const saveCompletionTime = this.formatDateTimeToOriginal(new Date());
          await this.generateAndSaveExcelData(selectedItems, saveCompletionTime);
          results.dataFiles.push(`分析数据汇总_${selectedItems.length}个样本_${saveCompletionTime}.xls`);
          updateProgress('Excel数据文件');
        } catch (error) {
          console.error('生成数据文件失败:', error);
          results.errors.push(`Excel数据文件: ${error.message}`);
          updateProgress('Excel数据文件');
        }
      } else {
        console.log('未选择图片，跳过Excel数据文件生成');
      }

      // 显示保存结果
      this.setData({
        showSaveProgress: false,
        showSaveResults: true,
        saveResults: results
      });

    } catch (error) {
      console.error('保存失败:', error);
      this.setData({
        showSaveProgress: false,
        showSaveResults: true,
        saveResults: {
          videos: [],
          images: [],
          dataFiles: [],
          errors: [error.message || '保存失败']
        }
      });
    } finally {
      this.isSaving = false;
      this.isPreparingShare = false;
    }
  },

  // 获取选中的数据项用于保存
  getSelectedItemsForSave() {
    const selectedItems = [];

    for (const dataIndex of this.data.selectedDataIndexes) {
      if (dataIndex < 0 || dataIndex >= this.data.filteredStoredData.length) continue;

      const item = this.data.filteredStoredData[dataIndex];
      if (!item) continue;

      // 检查是否有选中的内容
      if (item.videoSelected || item.imageSelected) {
        selectedItems.push({
          ...item,
          videoSelected: item.videoSelected || false,
          imageSelected: item.imageSelected || false
        });
      }
    }

    return selectedItems;
  },

  // 保存媒体文件到相册
  async saveMediaToAlbum(mediaType, item, fileName) {
    try {
      // 1. 获取媒体URL
      const mediaUrl = await this.getMediaUrlForSave(mediaType, item);
      if (!mediaUrl) {
        throw new Error('无法获取媒体URL');
      }

      // 2. 下载文件到临时目录
      const tempFilePath = await this.downloadFileToTemp(mediaUrl, fileName);

      // 3. 保存到相册
      if (mediaType === 'video') {
        await this.saveVideoToAlbum(tempFilePath);
      } else {
        await this.saveImageToAlbum(tempFilePath);
      }

      return { success: true };
    } catch (error) {
      console.error(`保存${mediaType}失败:`, error);
      throw error;
    }
  },

  // 获取媒体URL用于保存
  getMediaUrlForSave(mediaType, item) {
    return new Promise((resolve, reject) => {
      let mediaPath = null;

      switch (mediaType) {
        case 'video':
          // 优先使用videoUrl，然后是videoPath
          if (item.videoUrl) {
            resolve(item.videoUrl);
            return;
          }
          mediaPath = item.videoPath || item.videoFile;
          break;
        case 'cAreaImage':
          mediaPath = item.cAreaImagePath || item.cAreaImage;
          break;
        case 'tAreaImage':
          mediaPath = item.tAreaImagePath || item.tAreaImage;
          break;
        default:
          reject(new Error(`未知的媒体类型: ${mediaType}`));
          return;
      }

      if (!mediaPath) {
        reject(new Error(`${mediaType}路径不存在`));
        return;
      }

      // 如果已经是完整URL，直接返回
      if (mediaPath.startsWith('http')) {
        resolve(mediaPath);
        return;
      }

      // 通过云函数获取临时URL
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getTempFileURL',
          fileID: this.formatFileID(mediaPath)
        },
        success: (res) => {
          if (res.result && res.result.success && res.result.fileUrl) {
            resolve(res.result.fileUrl);
          } else {
            reject(new Error('获取临时URL失败'));
          }
        },
        fail: (error) => {
          reject(new Error(`云函数调用失败: ${error.errMsg}`));
        }
      });
    });
  },

  // 下载文件到临时目录
  downloadFileToTemp(url, fileName) {
    return new Promise((resolve, reject) => {
      console.log('🔍 开始下载文件:', { url, fileName });

      wx.downloadFile({
        url: url,
        success: (res) => {
          console.log('🔍 下载响应:', res);

          if (res.statusCode === 200) {
            // 验证下载的文件
            const fs = wx.getFileSystemManager();
            try {
              const stats = fs.statSync(res.tempFilePath);
              console.log('🔍 下载文件验证:', {
                path: res.tempFilePath,
                size: stats.size,
                isFile: stats.isFile()
              });

              if (stats.size === 0) {
                reject(new Error('下载的文件大小为0'));
                return;
              }

              // 如果是视频文件，检查文件头
              if (fileName.includes('视频') || res.tempFilePath.includes('.mp4')) {
                try {
                  const buffer = fs.readFileSync(res.tempFilePath);
                  const header = buffer.slice(0, 8);
                  console.log('🔍 视频文件头:', Array.from(header).map(b => b.toString(16).padStart(2, '0')).join(' '));

                  // 检查是否为有效的MP4文件头
                  const headerStr = header.toString('hex');
                  if (!headerStr.includes('66747970') && !headerStr.includes('6d646174')) {
                    console.warn('⚠️ 可能不是有效的MP4文件头');
                  }
                } catch (headerError) {
                  console.error('🔍 无法读取文件头:', headerError);
                }
              }

              resolve(res.tempFilePath);
            } catch (statError) {
              console.error('🔍 文件验证失败:', statError);
              reject(new Error(`下载文件验证失败: ${statError.message}`));
            }
          } else {
            reject(new Error(`下载失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          console.error('🔍 下载失败:', error);
          reject(new Error(`下载失败: ${error.errMsg}`));
        }
      });
    });
  },

  // 保存视频到相册
  saveVideoToAlbum(tempFilePath) {
    return new Promise((resolve, reject) => {
      console.log('🔍 开始保存视频到相册，文件路径:', tempFilePath);

      // 验证文件是否存在
      const fs = wx.getFileSystemManager();
      try {
        const stats = fs.statSync(tempFilePath);
        console.log('🔍 视频文件信息:', {
          size: stats.size,
          isFile: stats.isFile(),
          path: tempFilePath
        });

        if (stats.size === 0) {
          reject(new Error('视频文件大小为0'));
          return;
        }
      } catch (statError) {
        console.error('🔍 无法获取视频文件信息:', statError);
        reject(new Error(`视频文件不存在或无法访问: ${statError.message}`));
        return;
      }

      wx.saveVideoToPhotosAlbum({
        filePath: tempFilePath,
        success: () => {
          console.log('✅ 视频保存到相册成功');
          resolve();
        },
        fail: (error) => {
          console.error('❌ 保存视频到相册失败:', error);
          if (error.errMsg.includes('auth deny')) {
            reject(new Error('需要授权访问相册'));
          } else if (error.errMsg.includes('invalid')) {
            reject(new Error('视频格式不支持或已损坏，无法编辑'));
          } else {
            reject(new Error(`保存视频失败: ${error.errMsg}`));
          }
        }
      });
    });
  },

  // 保存图片到相册
  saveImageToAlbum(tempFilePath) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: () => {
          resolve();
        },
        fail: (error) => {
          if (error.errMsg.includes('auth deny')) {
            reject(new Error('需要授权访问相册'));
          } else {
            reject(new Error(`保存图片失败: ${error.errMsg}`));
          }
        }
      });
    });
  },

  // 生成并保存Excel数据文件
  async generateAndSaveExcelData(selectedItems, saveCompletionTime) {
    try {
      // 生成Excel格式的数据
      const excelData = this.generateExcelData(selectedItems);

      // 保存为Excel文件 - 使用传入的保存完成时间命名
      const fileName = `分析数据汇总_${selectedItems.length}个样本_${saveCompletionTime}.xls`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      const fs = wx.getFileSystemManager();
      fs.writeFileSync(filePath, excelData, 'utf8');

      // 尝试多种方式保存数据文件
      await this.saveDataFileToDevice(filePath, fileName, excelData, selectedItems);

      return { success: true };
    } catch (error) {
      console.error('生成Excel数据失败:', error);
      throw error;
    }
  },

  // 生成Excel格式数据（HTML表格格式，Excel可以打开）
  generateExcelData(selectedItems) {
    // 表头
    const headers = [
      '序号',
      '样本名称',
      '分析时间',
      'C区数值',
      'T区数值',
      'T/C比值',
      '备注'
    ];

    // 数据行 - 使用原始数值，与前端显示完全一致
    const rows = selectedItems.map((item, index) => [
      index + 1,
      `数据组${index + 1}`,
      item.displayTime || '',
      item.cAreaValue || 'N/A',
      item.tAreaValue || 'N/A',
      item.tcValue || 'N/A',
      item.notes || ''
    ]);

    // 计算统计信息
    const validCValues = selectedItems.filter(item => item.cAreaValue && !isNaN(item.cAreaValue)).map(item => parseFloat(item.cAreaValue));
    const validTValues = selectedItems.filter(item => item.tAreaValue && !isNaN(item.tAreaValue)).map(item => parseFloat(item.tAreaValue));
    const validTCValues = selectedItems.filter(item => item.tcValue && !isNaN(item.tcValue)).map(item => parseFloat(item.tcValue));

    const avgC = validCValues.length > 0 ? (validCValues.reduce((a, b) => a + b, 0) / validCValues.length).toFixed(2) : 'N/A';
    const avgT = validTValues.length > 0 ? (validTValues.reduce((a, b) => a + b, 0) / validTValues.length).toFixed(2) : 'N/A';
    const avgTC = validTCValues.length > 0 ? (validTCValues.reduce((a, b) => a + b, 0) / validTCValues.length).toFixed(2) : 'N/A';

    // 生成HTML表格格式（Excel可以识别）
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>分析数据报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { color: #333; margin-bottom: 10px; font-size: 24px; }
        .info { color: #666; font-size: 14px; }
        .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary h3 { margin-top: 0; color: #4a90e2; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: center; }
        th { background-color: #4a90e2; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e8f4fd; }
        .number { text-align: right; }
        .footer { margin-top: 30px; font-size: 12px; color: #999; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h2 class="title">分析数据报告</h2>
        <div class="info">
            <p>生成时间: ${new Date().toLocaleString()}</p>
            <p>样本数量: ${selectedItems.length} 个</p>
        </div>
    </div>

    <div class="summary">
        <h3>数据统计摘要</h3>
        <p><strong>C区平均值:</strong> ${avgC}</p>
        <p><strong>T区平均值:</strong> ${avgT}</p>
        <p><strong>T/C比值平均:</strong> ${avgTC}</p>
        <p><strong>有效数据:</strong> C区 ${validCValues.length}个, T区 ${validTValues.length}个, T/C比值 ${validTCValues.length}个</p>
    </div>

    <table>
        <thead>
            <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
        </thead>
        <tbody>
            ${rows.map(row => `
                <tr>
                    ${row.map((cell, index) => {
                      const isNumber = index >= 3 && index <= 5; // C区、T区、T/C列
                      return `<td class="${isNumber ? 'number' : ''}">${cell}</td>`;
                    }).join('')}
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="footer">
        <p>此报告由分析系统自动生成，包含 ${selectedItems.length} 个样本的完整数据</p>
        <p>可在Excel中进一步编辑和分析</p>
    </div>
</body>
</html>`;

    return htmlContent;
  },

  // 生成CSV格式数据（备用）
  generateCSVData(selectedItems) {
    // CSV头部
    const headers = [
      '样本名称',
      '分析时间',
      'C区数值',
      'T区数值',
      'T/C比值',
      '备注'
    ];

    // CSV数据行 - 使用原始数值，与前端显示完全一致
    const rows = selectedItems.map((item, index) => [
      `数据组${index + 1}`,
      item.displayTime || '',
      item.cAreaValue || 'N/A',
      item.tAreaValue || 'N/A',
      item.tcValue || 'N/A',
      item.notes || ''
    ]);

    // 组合成CSV格式
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    // 添加BOM以支持Excel正确显示中文
    return '\ufeff' + csvContent;
  },

  // 保存数据文件到设备
  async saveDataFileToDevice(filePath, fileName, excelData, selectedItems) {
    try {
      // 方法1: 使用 wx.openDocument 打开文件（用户可以保存）
      await this.openDocumentForSave(filePath, fileName);
    } catch (openError) {
      console.warn('打开文档失败，尝试其他方式:', openError);

      try {
        // 方法2: 尝试使用文件分享
        await this.tryShareDataFile(filePath, fileName);
      } catch (shareError) {
        console.warn('文件分享失败，尝试图片方式:', shareError);

        try {
          // 方法3: 保存为图片（将数据绘制成图片）
          const csvData = this.generateCSVData(selectedItems);
          await this.saveDataAsImage(csvData, fileName);
        } catch (imageError) {
          console.warn('保存为图片失败，使用剪贴板方式:', imageError);

          // 方法4: 复制到剪贴板（最后备用方案）
          const csvData = this.generateCSVData(selectedItems);
          await this.copyDataToClipboard(csvData);
        }
      }
    }
  },

  // 使用 wx.openDocument 打开文件供用户保存
  openDocumentForSave(filePath, fileName) {
    return new Promise((resolve, reject) => {
      // 显示提示信息
      wx.showModal({
        title: 'Excel文件已生成',
        content: '即将打开Excel文件，您可以在打开后选择"保存到文件"将其保存到手机中',
        showCancel: false,
        confirmText: '打开文件',
        success: () => {
          // 打开文档
          wx.openDocument({
            filePath: filePath,
            fileType: 'xls', // 指定为Excel文件类型
            success: () => {
              console.log('Excel文档打开成功，用户可以选择保存');
              resolve();
            },
            fail: (error) => {
              console.error('打开Excel文档失败:', error);
              reject(error);
            }
          });
        }
      });
    });
  },

  // 尝试分享数据文件
  tryShareDataFile(filePath, fileName) {
    return new Promise((resolve, reject) => {
      wx.shareFileMessage({
        filePath: filePath,
        fileName: fileName,
        success: () => {
          console.log('文件分享成功');
          resolve();
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 将数据保存为图片
  async saveDataAsImage(csvData, fileName) {
    return new Promise((resolve, reject) => {
      // 创建canvas上下文
      const query = wx.createSelectorQuery();
      query.select('#dataCanvas').fields({ node: true, size: true }).exec((res) => {
        if (!res[0]) {
          // 如果没有canvas元素，创建一个临时的
          this.createDataImageWithoutCanvas(csvData, fileName).then(resolve).catch(reject);
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 设置canvas尺寸
        const dpr = wx.getSystemInfoSync().pixelRatio;
        canvas.width = 800 * dpr;
        canvas.height = 1200 * dpr;
        ctx.scale(dpr, dpr);

        // 绘制数据表格
        this.drawDataTable(ctx, csvData, 800, 1200);

        // 保存为图片
        wx.canvasToTempFilePath({
          canvas: canvas,
          success: (res) => {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                console.log('数据图片保存成功');
                resolve();
              },
              fail: (error) => {
                reject(error);
              }
            });
          },
          fail: (error) => {
            reject(error);
          }
        });
      });
    });
  },

  // 不使用canvas创建数据图片（备用方案）
  createDataImageWithoutCanvas(csvData, fileName) {
    return new Promise((resolve, reject) => {
      // 这里可以使用其他方式生成图片，比如调用云函数
      // 暂时使用复制到剪贴板的方式
      this.copyDataToClipboard(csvData).then(resolve).catch(reject);
    });
  },

  // 绘制数据表格到canvas
  drawDataTable(ctx, csvData, width, height) {
    // 设置背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // 设置标题
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 24px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('分析数据报告', width / 2, 40);

    // 绘制表格
    const lines = csvData.split('\n');
    const startY = 80;
    const lineHeight = 30;

    ctx.font = '16px sans-serif';
    ctx.textAlign = 'left';

    lines.forEach((line, index) => {
      if (line.trim()) {
        const y = startY + index * lineHeight;

        // 表头使用不同颜色
        if (index === 0) {
          ctx.fillStyle = '#4a90e2';
          ctx.font = 'bold 16px sans-serif';
        } else {
          ctx.fillStyle = '#333333';
          ctx.font = '16px sans-serif';
        }

        // 移除CSV的引号并分割
        const cells = line.replace(/"/g, '').split(',');
        let x = 20;

        cells.forEach((cell, cellIndex) => {
          ctx.fillText(cell, x, y);
          x += 120; // 列宽
        });
      }
    });

    // 添加时间戳
    ctx.fillStyle = '#999999';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'right';
    ctx.fillText(`生成时间: ${new Date().toLocaleString()}`, width - 20, height - 20);
  },

  // 复制数据到剪贴板
  copyDataToClipboard(csvData) {
    return new Promise((resolve, reject) => {
      wx.setClipboardData({
        data: csvData,
        success: () => {
          wx.showToast({
            title: '数据已复制到剪贴板，可粘贴到Excel中',
            icon: 'none',
            duration: 3000
          });
          resolve();
        },
        fail: (error) => {
          reject(new Error('复制到剪贴板失败'));
        }
      });
    });
  },

  // 格式化日期时间（用于Excel文件名 - 旧格式，保留备用）
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  },

  // 格式化日期时间为原始格式（与视频图片命名一致）
  formatDateTimeToOriginal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 返回格式：2024-12-01 14-30-22（冒号替换为短横线确保文件名兼容性）
    return `${year}-${month}-${day} ${hours}-${minutes}-${seconds}`;
  },



  // 关闭保存结果
  closeSaveResults() {
    this.setData({
      showSaveResults: false,
      showSaveProgress: false
    });
  },

  // 切换视频选择状态
  toggleVideoSelect(e) {
    const index = parseInt(e.currentTarget.dataset.index);

    // 直接更新filteredStoredData中的状态
    const updatePath = `filteredStoredData[${index}].videoSelected`;
    const currentValue = this.data.filteredStoredData[index]?.videoSelected || false;

    this.setData({
      [updatePath]: !currentValue
    });

    // 更新导出按钮状态
    this.updateExportButtonState();
  },

  // 切换图片和数值选择状态
  toggleImageSelect(e) {
    const index = parseInt(e.currentTarget.dataset.index);

    // 直接更新filteredStoredData中的状态
    const updatePath = `filteredStoredData[${index}].imageSelected`;
    const currentValue = this.data.filteredStoredData[index]?.imageSelected || false;

    this.setData({
      [updatePath]: !currentValue
    });

    // 更新导出按钮状态
    this.updateExportButtonState();
  },

  // 更新导出按钮状态
  updateExportButtonState() {
    // 检查是否有选中的项目
    const hasSelectedItems = this.data.filteredStoredData.some(item =>
      item.videoSelected || item.imageSelected
    );

    this.setData({
      canExport: hasSelectedItems
    });
  },

  // 显示媒体选择
  viewMedia: function(e) {
    // 检查事件对象是否有stopPropagation方法，如果有则调用
    // 微信不同版本或不同绑定方式可能导致事件对象结构不同
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    console.log('查看媒体按钮被点击', e);
    
    // 获取数据索引
    const index = e.currentTarget.dataset.index;
    console.log('获取到的索引:', index);
    
    // 先设置索引，再延迟显示弹窗，实现更平滑的过渡
    this.setData({
      currentMediaIndex: index
    });
    
    // 使用延时确保动画效果更流畅
    setTimeout(() => {
      this.setData({
        showMediaSelect: true
      });
      console.log('显示媒体选择对话框');
    }, 50);
  },

  // 隐藏媒体选择
  hideMediaSelect() {
    this.setData({
      showMediaSelect: false,
      currentMediaIndex: null
    });
  },
  
  // 隐藏本地预览
  hideLocalPreview() {
    this.setData({
      showLocalPreview: false
    });
  },
  
  // 阻止冒泡
  preventBubble() {
    // 阻止事件冒泡
    return;
  },
  
  // 图片加载错误处理
  onImageLoadError(e) {
    const index = e.currentTarget.dataset.index;
    console.error(`图片${index}加载失败:`, e);
    
    // 在失败的图片下方显示错误信息
    const errorText = `图片${index+1}加载失败，请尝试复制链接在浏览器中打开`;
    
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },
  
  // 复制图片URL
  copyImageUrl(e) {
    const url = e.currentTarget.dataset.url;
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showToast({
          title: 'URL已复制，可在浏览器中打开',
          icon: 'none'
        });
      }
    });
  },

  // 内联播放视频方法
  playVideoInline: function(videoUrl) {
    console.log('播放视频:', videoUrl);
    
    // 使用processFileURL处理URL，确保格式正确且无时间戳参数
    let processedUrl = this.processFileURL(videoUrl);
    console.log('处理后的视频URL:', processedUrl);
    
    this.setData({
      showVideoPlayer: true,
      currentVideoUrl: processedUrl,
      showLocalPreview: false
    });
  },
  
  // 处理文件URL的方法，确保URL正确
  processFileURL: function(url) {
    if (!url) return '';
    
    console.log('处理URL:', url);
    
    // 1. 修复域名中的下划线问题
    let processedUrl = url.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
    
    // 2. 移除所有查询参数，包括时间戳
    processedUrl = processedUrl.split('?')[0];
    console.log('移除查询参数后的URL:', processedUrl);
    
    // 3. 检查是否是简化的视频路径格式
    if (processedUrl.includes('/videos/') && !processedUrl.includes('/users/')) {
      console.log('检测到简化的视频路径格式:', processedUrl);
      
      // 获取用户信息
      const app = getApp();
      const openId = app.globalData.openid || wx.getStorageSync('openid');
      
      if (openId && this.data.currentMediaIndex !== null && this.data.storedData[this.data.currentMediaIndex]) {
        const sessionId = this.data.storedData[this.data.currentMediaIndex].sessionId || 
                         this.data.storedData[this.data.currentMediaIndex].groupName;
        
        if (sessionId) {
          // 尝试通过云函数获取完整URL
          wx.cloud.callFunction({
            name: 'getUserData',
            data: {
              action: 'getTempFileURL',
              fileID: `users/${openId}_data/${sessionId}/${processedUrl}`
            },
            success: res => {
              if (res.result && res.result.success && res.result.fileUrl) {
                // 确保移除返回URL中的所有查询参数
                let cleanUrl = res.result.fileUrl.split('?')[0];
                console.log('成功获取到完整视频URL:', cleanUrl);
                
                // 更新视频URL，确保没有时间戳参数
                if (this.data.showVideoPlayer) {
                  this.setData({
                    currentVideoUrl: cleanUrl
                  });
                }
              }
            },
            fail: err => {
              console.error('获取完整URL失败:', err);
            }
          });
        }
      }
    }
    
    return processedUrl;
  },
  
  // 关闭视频播放器
  closeVideoPlayer: function() {
    this.setData({
      showVideoPlayer: false,
      currentVideoUrl: ''
    });
  },

  // 修改添加查看视频的方法
  viewVideo: function() {
    const index = this.data.currentMediaIndex;
    if (index === null) return;
    
    const currentData = this.data.storedData[index];
    
    // 首先检查是否存在直接可用的videoUrl（带签名的临时URL）
    if (currentData.videoUrl) {
      console.log('使用数据库中已有的视频临时URL:', currentData.videoUrl);
      
      // 显示加载中提示
      wx.showLoading({
        title: '加载数据库视频URL...',
        mask: true
      });
      
      // 视频URL处理 - 确保格式正确和时间戳有效
      const videoUrl = this.processFileURL(currentData.videoUrl);
      
      // 直接在当前页面播放视频，而不是导航到其他页面
      this.playVideoInline(videoUrl);
      wx.hideLoading();
      
      this.hideMediaSelect();
      return;
    }
    
    // 如果没有videoUrl，检查是否有videoFile路径
    if (!currentData.videoFile && !currentData.groupName && !currentData.sessionId) {
      wx.showToast({
        title: '暂无视频',
        icon: 'none'
      });
      this.hideMediaSelect();
      return;
    }
    
    console.log('查看视频');
    
    // 提前准备最重要的信息用于调试
    console.log('准备请求视频，会话信息:', {
      groupName: currentData.groupName,
      sessionId: currentData.sessionId,
      videoFile: currentData.videoFile
    });
    
    // 显示加载中
    wx.showLoading({
      title: '正在加载视频...',
      mask: true
    });
    
    // 调用云函数获取最新的文件信息
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'getMediaFile',
        mediaType: 'video',
        sessionId: currentData.groupName || currentData.sessionId
      }
    }).then(res => {
      wx.hideLoading();
      console.log('从云函数获取视频信息结果:', res);
      
      if (res.result && res.result.success) {
        // 检查是否返回了可用的文件URL
        if (res.result.fileUrl) {
          const videoUrl = this.processFileURL(res.result.fileUrl);
          console.log('获取到有效的视频链接:', videoUrl);
          
          // 直接在当前页面播放视频
          this.playVideoInline(videoUrl);
        }
        // 处理特殊情况：云函数返回了useDirectFileID标志
        else if (res.result.useDirectFileID && res.result.fileID) {
          console.log('云函数返回了直接文件ID模式:', res.result.fileID);
          
          // 使用文件ID尝试获取临时URL
          const formattedFileID = this.formatFileID(res.result.fileID);
          console.log('格式化后的文件ID:', formattedFileID);
          
          wx.cloud.getTempFileURL({
            fileList: [formattedFileID],
            success: tempUrlRes => {
              console.log('直接模式获取临时URL结果:', tempUrlRes);
              if (tempUrlRes.fileList && tempUrlRes.fileList.length > 0 && 
                  tempUrlRes.fileList[0].status === 0 && tempUrlRes.fileList[0].tempFileURL) {
                
                const directVideoUrl = this.processFileURL(tempUrlRes.fileList[0].tempFileURL);
                // 直接在当前页面播放视频
                this.playVideoInline(directVideoUrl);
              } else {
                // 尝试备用方法
                console.log('直接模式获取临时URL失败，尝试备用方法');
                setTimeout(() => this.fallbackGetVideo(currentData), 1000);
              }
            },
            fail: err => {
              console.error('直接模式获取临时URL失败:', err);
              setTimeout(() => this.fallbackGetVideo(currentData), 1000);
            }
          });
        }
        else {
          // 没有fileUrl也没有useDirectFileID，可能是其他操作成功但没有返回可用链接
          console.log('云函数返回成功但未提供可用链接');
          wx.showToast({
            title: res.result.message || '获取视频链接失败',
            icon: 'none',
            duration: 2000
          });
          
          // 尝试备用方法
          if (currentData.videoFile) {
            setTimeout(() => {
              console.log('尝试使用已有视频文件ID:', currentData.videoFile);
              this.fallbackGetVideo(currentData);
            }, 1500);
          }
        }
      } else {
        // 如果通过云函数获取失败，显示云函数返回的错误消息
        if (res.result && res.result.message) {
          wx.showToast({
            title: res.result.message,
            icon: 'none',
            duration: 3000
          });
        } else {
          wx.showToast({
            title: '获取视频失败',
            icon: 'none',
            duration: 2000
          });
        }
        
        // 1.5秒后尝试备用方法
        setTimeout(() => {
          if (currentData.videoFile) {
            console.log('尝试使用已有文件ID:', currentData.videoFile);
            this.fallbackGetVideo(currentData);
          } else {
            wx.showToast({
              title: '无可用备用视频源',
              icon: 'none'
            });
          }
        }, 1500);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('调用云函数获取视频信息失败:', err);
      
      wx.showToast({
        title: '云函数调用失败',
        icon: 'none',
        duration: 2000
      });
      
      // 如果云函数调用失败，延迟后尝试使用已有的fileID
      setTimeout(() => {
        if (currentData.videoFile) {
          this.fallbackGetVideo(currentData);
        } else {
          wx.showToast({
            title: '获取视频信息失败',
            icon: 'none'
          });
        }
      }, 1000);
    });
    
    this.hideMediaSelect();
  },

  // 备份方法：在主要方法失败时尝试获取视频URL
  fallbackGetVideo: function(currentData) {
    if (!currentData) {
      console.error('备份方法缺少数据对象');
      return;
    }
    
    console.log('使用备用方法获取视频 - 会话ID:', currentData.sessionId);
    
    // 提取文件ID
    let fileID = '';
    if (currentData.videoFile) {
      fileID = currentData.videoFile;
    } else if (currentData.videoPath) {
      fileID = currentData.videoPath;
    } else {
      console.error('无法获取视频路径');
      return;
    }
    
    console.log('使用备用方法获取视频URL, 路径:', fileID);
    
    // 首先通过processFileURL处理路径
    const processedUrl = this.processFileURL(fileID);
    if (processedUrl) {
      console.log('备用方法通过处理获得视频URL:', processedUrl);
      this.playVideoInline(processedUrl);
      return;
    }
    
    // 尝试使用媒体获取API
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'getMediaFile',
        sessionId: currentData.sessionId,
        mediaType: 'video'
      },
      success: res => {
        console.log('备用方法获取视频结果:', res);
        
        if (res.result && res.result.success && res.result.fileUrl) {
          // 确保URL格式正确，移除时间戳参数
          const videoUrl = this.processFileURL(res.result.fileUrl);
          console.log('备用方法成功获取视频URL:', videoUrl);
          
          this.playVideoInline(videoUrl);
        } else {
          console.error('备用方法获取视频失败');
          
          // 最后尝试：如果是完整路径，直接构建URL
          if (fileID.includes('/users/') && fileID.includes('/videos/')) {
            // 提取文件路径
            let filePath = fileID;
            if (fileID.startsWith('cloud://')) {
              const pathStartIndex = fileID.indexOf('/', 8);
              if (pathStartIndex !== -1) {
                filePath = fileID.substring(pathStartIndex + 1);
              }
            }
            
            // 构建直接URL
            const directUrl = `https://776c-wlksapp-4g54hauu5cbf43dc-1329876191.tcb.qcloud.la/${filePath}`;
            console.log('构建的直接视频URL:', directUrl);
            this.playVideoInline(directUrl);
          }
        }
      },
      fail: err => {
        console.error('备用方法获取视频URL失败:', err);
      }
    });
  },
  
  // 尝试获取临时视频URL
  tryGetTempVideoUrl: function(fileID) {
    if (!fileID) {
      console.error('文件ID为空');
      return;
    }
    
    console.log('尝试获取视频临时URL, 路径:', fileID);
    
    // 处理文件URL，确保格式正确
    const processedUrl = this.processFileURL(fileID);
    if (processedUrl) {
      console.log('成功处理视频URL:', processedUrl);
      this.playVideoInline(processedUrl);
      return;
    }
    
    // 如果处理失败，尝试使用云函数获取
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'getTempFileURL',
        fileID: fileID
      },
      success: res => {
        console.log('通过云函数获取视频URL结果:', res);
        
        if (res.result && res.result.success && res.result.fileUrl) {
          // 处理URL格式，移除时间戳参数
          const videoUrl = this.processFileURL(res.result.fileUrl);
          console.log('成功获取视频URL:', videoUrl);
          
          this.playVideoInline(videoUrl);
        } else {
          console.error('获取视频URL失败');
          // 尝试通过直接调用API获取临时URL
          wx.cloud.getTempFileURL({
            fileList: [fileID],
            success: tempUrlRes => {
              console.log('通过API获取临时URL结果:', tempUrlRes);
              
              if (tempUrlRes.fileList && tempUrlRes.fileList.length > 0 && 
                  tempUrlRes.fileList[0].tempFileURL) {
                const videoUrl = this.processFileURL(tempUrlRes.fileList[0].tempFileURL);
                console.log('通过API成功获取视频临时URL:', videoUrl);
                
                this.playVideoInline(videoUrl);
              } else {
                console.error('通过API获取临时URL也失败');
              }
            },
            fail: tempUrlErr => {
              console.error('通过API获取临时URL失败:', tempUrlErr);
            }
          });
        }
      },
      fail: err => {
        console.error('通过云函数获取视频URL失败:', err);
      }
    });
  },

  // 添加查看C区图片的方法
  viewCAreaImage: function() {
    const index = this.data.currentMediaIndex;
    if (index === null) return;
    
    const currentData = this.data.storedData[index];
    
    // 首先检查是否存在直接可用的cAreaImageUrl（带签名的临时URL）
    if (currentData.cAreaImageUrl) {
      console.log('使用数据库中已有的C区图片临时URL:', currentData.cAreaImageUrl);
      
      // 显示加载中提示
      wx.showLoading({
        title: '加载数据库URL...',
        mask: true
      });
      
      // 预览图片
      wx.previewImage({
        urls: [currentData.cAreaImageUrl],
        current: currentData.cAreaImageUrl,
        success: () => {
          console.log('成功使用数据库中的URL预览C区图片');
          wx.hideLoading();
        },
        fail: previewErr => {
          console.error('使用数据库中的URL预览C区图片失败:', previewErr);
          wx.hideLoading();
          wx.showToast({
            title: 'URL已过期，正在重新获取',
            icon: 'none',
            duration: 2000
          });
          // 如果预览失败，再尝试其他方法
          this.fallbackGetCAreaImage(currentData);
        }
      });
      
      this.hideMediaSelect();
      return;
    }
    
    // 如果没有cAreaImageUrl，则检查是否有cAreaImage路径
    if (!currentData.cAreaImage && !currentData.groupName && !currentData.sessionId) {
      wx.showToast({
        title: '暂无C区图片',
        icon: 'none'
      });
      this.hideMediaSelect();
      return;
    }
    
    console.log('查看C区图片');
    
    // 提前准备最重要的信息用于调试
    console.log('准备请求C区图片，会话信息:', {
      groupName: currentData.groupName,
      sessionId: currentData.sessionId,
      cAreaImage: currentData.cAreaImage
    });
    
    // 显示加载中
    wx.showLoading({
      title: '正在加载图片...',
      mask: true
    });
    
    // 调用云函数获取最新的文件信息
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'getMediaFile',
        mediaType: 'cAreaImage',
        sessionId: currentData.groupName || currentData.sessionId
      }
    }).then(res => {
      wx.hideLoading();
      console.log('从云函数获取C区图片信息结果:', res);
      
      if (res.result && res.result.success) {
        // 检查是否返回了可用的文件URL
        if (res.result.fileUrl) {
          const imageUrl = res.result.fileUrl;
          console.log('获取到有效的C区图片链接:', imageUrl);
          
          // 检查返回的URL是否是cloud://格式，如果是，需要先获取临时URL
          if (imageUrl.startsWith('cloud://')) {
            console.log('返回的是文件ID格式，需要再次调用云函数获取临时URL');
            
            // 使用云函数获取临时URL，解决环境权限问题
            wx.cloud.callFunction({
              name: 'getUserData',
              data: {
                action: 'getTempFileURL',
                fileID: imageUrl
              },
              success: cloudRes => {
                console.log('通过云函数获取临时URL结果:', cloudRes);
                wx.hideLoading();
                
                if (cloudRes.result && cloudRes.result.success && cloudRes.result.fileUrl) {
                  const tempUrl = cloudRes.result.fileUrl;
                  console.log('通过云函数成功获取临时URL:', tempUrl);
                  
                  wx.previewImage({
                    urls: [tempUrl],
                    current: tempUrl,
                    fail: previewErr => {
                      console.error('预览C区图片失败:', previewErr);
                      wx.showToast({
                        title: '预览图片失败',
                        icon: 'none'
                      });
                      setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
                    }
                  });
                } else {
                  console.log('通过云函数未能获取临时URL，尝试备用方法');
                  setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
                }
              },
              fail: err => {
                wx.hideLoading();
                console.error('调用云函数获取临时URL失败:', err);
                setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
              }
            });
          } else {
            // 如果不是cloud://格式，直接使用URL
            wx.previewImage({
              urls: [imageUrl],
              current: imageUrl,
              fail: previewErr => {
                console.error('预览C区图片失败:', previewErr);
                wx.showToast({
                  title: '预览图片失败',
                  icon: 'none'
                });
                
                // 预览失败时，如果使用的是直接文件ID，尝试备用方法
                if (res.result.useDirectFileID && currentData.cAreaImage) {
                  console.log('直接使用文件ID失败，尝试备用方法');
                  setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
                }
              }
            });
          }
        }
        // 处理特殊情况：云函数返回了useDirectFileID标志
        else if (res.result.useDirectFileID && res.result.fileID) {
          console.log('云函数返回了直接文件ID模式:', res.result.fileID);
          
          // 使用文件ID尝试获取临时URL
          const formattedFileID = this.formatFileID(res.result.fileID);
          console.log('格式化后的文件ID:', formattedFileID);
          
          wx.cloud.getTempFileURL({
            fileList: [formattedFileID],
            success: tempUrlRes => {
              console.log('直接模式获取临时URL结果:', tempUrlRes);
              if (tempUrlRes.fileList && tempUrlRes.fileList.length > 0 && 
                  tempUrlRes.fileList[0].status === 0 && tempUrlRes.fileList[0].tempFileURL) {
                
                const directImageUrl = tempUrlRes.fileList[0].tempFileURL;
                wx.previewImage({
                  urls: [directImageUrl],
                  current: directImageUrl,
                  fail: directPreviewErr => {
                    console.error('直接模式预览图片失败:', directPreviewErr);
                    setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
                  }
                });
              } else {
                // 尝试备用方法
                console.log('直接模式获取临时URL失败，尝试备用方法');
                setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
              }
            },
            fail: err => {
              console.error('直接模式获取临时URL失败:', err);
              setTimeout(() => this.fallbackGetCAreaImage(currentData), 1000);
            }
          });
        }
        else {
          // 没有fileUrl也没有useDirectFileID，可能是其他操作成功但没有返回可用链接
          console.log('云函数返回成功但未提供可用链接');
          wx.showToast({
            title: res.result.message || '获取图片链接失败',
            icon: 'none',
            duration: 2000
          });
          
          // 尝试备用方法
          if (currentData.cAreaImage) {
            setTimeout(() => {
              console.log('尝试使用已有C区图片ID:', currentData.cAreaImage);
              this.fallbackGetCAreaImage(currentData);
            }, 1500);
          }
        }
      } else {
        // 如果通过云函数获取失败，显示云函数返回的错误消息
        if (res.result && res.result.message) {
          wx.showToast({
            title: res.result.message,
            icon: 'none',
            duration: 3000
          });
        } else {
          wx.showToast({
            title: '获取图片失败',
            icon: 'none',
            duration: 2000
          });
        }
        
        // 1.5秒后尝试备用方法
        setTimeout(() => {
          if (currentData.cAreaImage) {
            console.log('尝试使用已有C区图片ID:', currentData.cAreaImage);
            this.fallbackGetCAreaImage(currentData);
          } else {
            wx.showToast({
              title: '无可用备用图片源',
              icon: 'none'
            });
          }
        }, 1500);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('调用云函数获取C区图片信息失败:', err);
      
      wx.showToast({
        title: '云函数调用失败',
        icon: 'none',
        duration: 2000
      });
      
      // 如果云函数调用失败，延迟后尝试使用已有的fileID
      setTimeout(() => {
        if (currentData.cAreaImage) {
          this.fallbackGetCAreaImage(currentData);
        } else {
          wx.showToast({
            title: '获取图片信息失败',
            icon: 'none'
          });
        }
      }, 1000);
    });
    
    this.hideMediaSelect();
  },

  // 备用方法获取C区图片
  fallbackGetCAreaImage: function(currentData) {
    if (!currentData || !currentData.cAreaImage) {
      wx.showToast({
        title: '暂无C区图片',
        icon: 'none'
      });
      return;
    }
    
    try {
      console.log('使用备用方法获取C区图片');
      console.log('C区图片原始ID:', currentData.cAreaImage);
      
      // 格式化文件ID
      const formattedCAreaID = this.formatFileID(currentData.cAreaImage);
      console.log('C区图片格式化后ID:', formattedCAreaID);
      
      // 显示加载提示
      wx.showLoading({
        title: '尝试备用方法...',
        mask: true
      });
      
      // 通过云函数获取临时文件链接
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getTempFileURL',
          fileID: formattedCAreaID
        },
        success: res => {
          wx.hideLoading();
          console.log('备用方法通过云函数获取临时链接结果:', res);
          
          if (res.result && res.result.success && (res.result.fileUrl || res.result.isDirect)) {
            const imageUrl = res.result.fileUrl;
            console.log('备用方法获取到C区图片链接:', imageUrl);
            
            // 检查是否是直接链接
            const isDirect = res.result.isDirect === true;
            console.log('是否是直接链接:', isDirect);
            
            wx.previewImage({
              urls: [imageUrl],
              current: imageUrl,
              fail: previewErr => {
                console.error('预览C区图片失败:', previewErr);
                wx.showToast({
                  title: '预览图片失败',
                  icon: 'none'
                });
                
                // 如果预览失败，尝试最后的fallback
                this.finalFallbackForCAreaImage(currentData, formattedCAreaID);
              }
            });
          } else {
            console.error('备用方法获取C区图片链接失败:', res.result ? res.result.message : '未知错误');
            
            // 最终备用方法
            this.finalFallbackForCAreaImage(currentData, formattedCAreaID);
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('备用方法调用云函数失败:', err);
          
          // 最终备用方法
          this.finalFallbackForCAreaImage(currentData, formattedCAreaID);
        }
      });
    } catch (error) {
      console.error('处理C区图片ID时出错:', error);
      wx.showToast({
        title: '处理图片ID失败',
        icon: 'none'
      });
    }
  },
  
  // 最终备用方法，使用base64或直接显示
  finalFallbackForCAreaImage: function(currentData, formattedCAreaID) {
    // 最终备用方法：直接下载文件并转换为base64
    wx.showLoading({
      title: '尝试最终备用方案...',
      mask: true
    });
    
    // 直接下载文件并转换为base64
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'downloadFileAsBase64',
        fileID: formattedCAreaID
      },
      success: base64Res => {
        wx.hideLoading();
        console.log('下载文件为base64结果:', base64Res);
        
        if (base64Res.result && base64Res.result.success) {
          if (base64Res.result.dataUrl) {
            // 将base64图片保存到本地，然后预览
            const fs = wx.getFileSystemManager();
            const filePath = `${wx.env.USER_DATA_PATH}/temp_c_area_${Date.now()}.png`;
            
            try {
              // 将base64写入临时文件
              fs.writeFileSync(
                filePath,
                base64Res.result.dataUrl.replace(/^data:image\/\w+;base64,/, ''),
                'base64'
              );
              
              console.log('成功将base64保存为临时文件:', filePath);
              
              // 预览图片
              wx.previewImage({
                urls: [filePath],
                current: filePath,
                fail: previewErr => {
                  console.error('预览保存的C区图片失败:', previewErr);
                  
                  // 如果预览失败，尝试直接设置到页面中显示
                  this.setData({
                    previewImageUrls: [base64Res.result.dataUrl],
                    showLocalPreview: true
                  });
                }
              });
            } catch (fsErr) {
              console.error('保存base64到文件失败:', fsErr);
              
              // 直接在页面中显示base64图片
              this.setData({
                previewImageUrls: [base64Res.result.dataUrl],
                showLocalPreview: true
              });
              
              wx.showToast({
                title: '使用内置预览显示图片',
                icon: 'none'
              });
            }
          } else if (base64Res.result.fileUrl) {
            // 如果返回了直接链接而不是base64
            console.log('接收到直接链接:', base64Res.result.fileUrl);
            
            wx.previewImage({
              urls: [base64Res.result.fileUrl],
              current: base64Res.result.fileUrl,
              fail: previewErr => {
                console.error('预览直接链接失败:', previewErr);
                
                // 在页面中显示图片
                this.setData({
                  previewImageUrls: [base64Res.result.fileUrl],
                  showLocalPreview: true
                });
                
                wx.showToast({
                  title: '使用内置预览显示图片',
                  icon: 'none'
                });
              }
            });
          } else {
            wx.showToast({
              title: '图片数据格式无效',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: base64Res.result ? base64Res.result.message : '无法获取图片',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: base64Err => {
        wx.hideLoading();
        console.error('下载文件为base64失败:', base64Err);
        
        wx.showToast({
          title: '所有获取图片方式都失败',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 添加查看T区图片的方法
  viewTAreaImage: function() {
    const index = this.data.currentMediaIndex;
    if (index === null) return;
    
    const currentData = this.data.storedData[index];
    
    // 首先检查是否存在直接可用的tAreaImageUrl（带签名的临时URL）
    if (currentData.tAreaImageUrl) {
      console.log('使用数据库中已有的T区图片临时URL:', currentData.tAreaImageUrl);
      
      // 显示加载中提示
      wx.showLoading({
        title: '加载数据库URL...',
        mask: true
      });
      
      // 预览图片
      wx.previewImage({
        urls: [currentData.tAreaImageUrl],
        current: currentData.tAreaImageUrl,
        success: () => {
          console.log('成功使用数据库中的URL预览T区图片');
          wx.hideLoading();
        },
        fail: previewErr => {
          console.error('使用数据库中的URL预览T区图片失败:', previewErr);
          wx.hideLoading();
          wx.showToast({
            title: 'URL已过期，正在重新获取',
            icon: 'none',
            duration: 2000
          });
          // 如果预览失败，再尝试其他方法
          this.fallbackGetTAreaImage(currentData);
        }
      });
      
      this.hideMediaSelect();
      return;
    }
    
    // 如果没有tAreaImageUrl，则检查是否有tAreaImage路径
    if (!currentData.tAreaImage && !currentData.groupName && !currentData.sessionId) {
      wx.showToast({
        title: '暂无T区图片',
        icon: 'none'
      });
      this.hideMediaSelect();
      return;
    }
    
    console.log('查看T区图片');
    
    // 提前准备最重要的信息用于调试
    console.log('准备请求T区图片，会话信息:', {
      groupName: currentData.groupName,
      sessionId: currentData.sessionId,
      tAreaImage: currentData.tAreaImage
    });
    
    // 显示加载中
    wx.showLoading({
      title: '正在加载图片...',
      mask: true
    });
    
    // 调用云函数获取最新的文件信息
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'getMediaFile',
        mediaType: 'tAreaImage',
        sessionId: currentData.groupName || currentData.sessionId
      }
    }).then(res => {
      wx.hideLoading();
      console.log('从云函数获取T区图片信息结果:', res);
      
      if (res.result && res.result.success) {
        // 检查是否返回了可用的文件URL
        if (res.result.fileUrl) {
          const imageUrl = res.result.fileUrl;
          console.log('获取到有效的T区图片链接:', imageUrl);
          
          // 检查返回的URL是否是cloud://格式，如果是，需要先获取临时URL
          if (imageUrl.startsWith('cloud://')) {
            console.log('返回的是文件ID格式，需要再次调用云函数获取临时URL');
            
            // 使用云函数获取临时URL，解决环境权限问题
            wx.cloud.callFunction({
              name: 'getUserData',
              data: {
                action: 'getTempFileURL',
                fileID: imageUrl
              },
              success: cloudRes => {
                console.log('通过云函数获取临时URL结果:', cloudRes);
                wx.hideLoading();
                
                if (cloudRes.result && cloudRes.result.success && cloudRes.result.fileUrl) {
                  const tempUrl = cloudRes.result.fileUrl;
                  console.log('通过云函数成功获取临时URL:', tempUrl);
                  
                  wx.previewImage({
                    urls: [tempUrl],
                    current: tempUrl,
                    fail: previewErr => {
                      console.error('预览T区图片失败:', previewErr);
                      wx.showToast({
                        title: '预览图片失败',
                        icon: 'none'
                      });
                      setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
                    }
                  });
                } else {
                  console.log('通过云函数未能获取临时URL，尝试备用方法');
                  setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
                }
              },
              fail: err => {
                wx.hideLoading();
                console.error('调用云函数获取临时URL失败:', err);
                setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
              }
            });
          } else {
            // 如果不是cloud://格式，直接使用URL
            wx.previewImage({
              urls: [imageUrl],
              current: imageUrl,
              fail: previewErr => {
                console.error('预览T区图片失败:', previewErr);
                wx.showToast({
                  title: '预览图片失败',
                  icon: 'none'
                });
                
                // 预览失败时，如果使用的是直接文件ID，尝试备用方法
                if (res.result.useDirectFileID && currentData.tAreaImage) {
                  console.log('直接使用文件ID失败，尝试备用方法');
                  setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
                }
              }
            });
          }
        }
        // 处理特殊情况：云函数返回了useDirectFileID标志
        else if (res.result.useDirectFileID && res.result.fileID) {
          console.log('云函数返回了直接文件ID模式:', res.result.fileID);
          
          // 使用文件ID尝试获取临时URL
          const formattedFileID = this.formatFileID(res.result.fileID);
          console.log('格式化后的文件ID:', formattedFileID);
          
          wx.cloud.getTempFileURL({
            fileList: [formattedFileID],
            success: tempUrlRes => {
              console.log('直接模式获取临时URL结果:', tempUrlRes);
              if (tempUrlRes.fileList && tempUrlRes.fileList.length > 0 && 
                  tempUrlRes.fileList[0].status === 0 && tempUrlRes.fileList[0].tempFileURL) {
                
                const directImageUrl = tempUrlRes.fileList[0].tempFileURL;
                wx.previewImage({
                  urls: [directImageUrl],
                  current: directImageUrl,
                  fail: directPreviewErr => {
                    console.error('直接模式预览图片失败:', directPreviewErr);
                    setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
                  }
                });
              } else {
                // 尝试备用方法
                console.log('直接模式获取临时URL失败，尝试备用方法');
                setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
              }
            },
            fail: err => {
              console.error('直接模式获取临时URL失败:', err);
              setTimeout(() => this.fallbackGetTAreaImage(currentData), 1000);
            }
          });
        }
        else {
          // 没有fileUrl也没有useDirectFileID，可能是其他操作成功但没有返回可用链接
          console.log('云函数返回成功但未提供可用链接');
          wx.showToast({
            title: res.result.message || '获取图片链接失败',
            icon: 'none',
            duration: 2000
          });
          
          // 尝试备用方法
          if (currentData.tAreaImage) {
            setTimeout(() => {
              console.log('尝试使用已有T区图片ID:', currentData.tAreaImage);
              this.fallbackGetTAreaImage(currentData);
            }, 1500);
          }
        }
      } else {
        // 如果通过云函数获取失败，显示云函数返回的错误消息
        if (res.result && res.result.message) {
          wx.showToast({
            title: res.result.message,
            icon: 'none',
            duration: 3000
          });
        } else {
          wx.showToast({
            title: '获取图片失败',
            icon: 'none',
            duration: 2000
          });
        }
        
        // 1.5秒后尝试备用方法
        setTimeout(() => {
          if (currentData.tAreaImage) {
            console.log('尝试使用已有T区图片ID:', currentData.tAreaImage);
            this.fallbackGetTAreaImage(currentData);
          } else {
            wx.showToast({
              title: '无可用备用图片源',
              icon: 'none'
            });
          }
        }, 1500);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('调用云函数获取T区图片信息失败:', err);
      
      wx.showToast({
        title: '云函数调用失败',
        icon: 'none',
        duration: 2000
      });
      
      // 如果云函数调用失败，延迟后尝试使用已有的fileID
      setTimeout(() => {
        if (currentData.tAreaImage) {
          this.fallbackGetTAreaImage(currentData);
        } else {
          wx.showToast({
            title: '获取图片信息失败',
            icon: 'none'
          });
        }
      }, 1000);
    });
    
    this.hideMediaSelect();
  },

  // 备用方法获取T区图片
  fallbackGetTAreaImage: function(currentData) {
    if (!currentData || !currentData.tAreaImage) {
      wx.showToast({
        title: '暂无T区图片',
        icon: 'none'
      });
      return;
    }
    
    try {
      console.log('使用备用方法获取T区图片');
      console.log('T区图片原始ID:', currentData.tAreaImage);
      
      // 格式化文件ID
      const formattedTAreaID = this.formatFileID(currentData.tAreaImage);
      console.log('T区图片格式化后ID:', formattedTAreaID);
      
      // 显示加载提示
      wx.showLoading({
        title: '尝试备用方法...',
        mask: true
      });
      
      // 通过云函数获取临时文件链接
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getTempFileURL',
          fileID: formattedTAreaID
        },
        success: res => {
          wx.hideLoading();
          console.log('备用方法通过云函数获取临时链接结果:', res);
          
          if (res.result && res.result.success && (res.result.fileUrl || res.result.isDirect)) {
            const imageUrl = res.result.fileUrl;
            console.log('备用方法获取到T区图片链接:', imageUrl);
            
            // 检查是否是直接链接
            const isDirect = res.result.isDirect === true;
            console.log('是否是直接链接:', isDirect);
            
            wx.previewImage({
              urls: [imageUrl],
              current: imageUrl,
              fail: previewErr => {
                console.error('预览T区图片失败:', previewErr);
                wx.showToast({
                  title: '预览图片失败',
                  icon: 'none'
                });
                
                // 如果预览失败，尝试最后的fallback
                this.finalFallbackForTAreaImage(currentData, formattedTAreaID);
              }
            });
          } else {
            console.error('备用方法获取T区图片链接失败:', res.result ? res.result.message : '未知错误');
            
            // 最终备用方法
            this.finalFallbackForTAreaImage(currentData, formattedTAreaID);
          }
        },
        fail: err => {
          wx.hideLoading();
          console.error('备用方法调用云函数失败:', err);
          
          // 最终备用方法
          this.finalFallbackForTAreaImage(currentData, formattedTAreaID);
        }
      });
    } catch (error) {
      console.error('处理T区图片ID时出错:', error);
      wx.showToast({
        title: '处理图片ID失败',
        icon: 'none'
      });
    }
  },
  
  // 最终备用方法，使用base64或直接显示
  finalFallbackForTAreaImage: function(currentData, formattedTAreaID) {
    // 最终备用方法：直接下载文件并转换为base64
    wx.showLoading({
      title: '尝试最终备用方案...',
      mask: true
    });
    
    // 直接下载文件并转换为base64
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'downloadFileAsBase64',
        fileID: formattedTAreaID
      },
      success: base64Res => {
        wx.hideLoading();
        console.log('下载文件为base64结果:', base64Res);
        
        if (base64Res.result && base64Res.result.success) {
          if (base64Res.result.dataUrl) {
            // 将base64图片保存到本地，然后预览
            const fs = wx.getFileSystemManager();
            const filePath = `${wx.env.USER_DATA_PATH}/temp_t_area_${Date.now()}.png`;
            
            try {
              // 将base64写入临时文件
              fs.writeFileSync(
                filePath,
                base64Res.result.dataUrl.replace(/^data:image\/\w+;base64,/, ''),
                'base64'
              );
              
              console.log('成功将base64保存为临时文件:', filePath);
              
              // 预览图片
              wx.previewImage({
                urls: [filePath],
                current: filePath,
                fail: previewErr => {
                  console.error('预览保存的T区图片失败:', previewErr);
                  
                  // 如果预览失败，尝试直接设置到页面中显示
                  this.setData({
                    previewImageUrls: [base64Res.result.dataUrl],
                    showLocalPreview: true
                  });
                }
              });
            } catch (fsErr) {
              console.error('保存base64到文件失败:', fsErr);
              
              // 直接在页面中显示base64图片
              this.setData({
                previewImageUrls: [base64Res.result.dataUrl],
                showLocalPreview: true
              });
              
              wx.showToast({
                title: '使用内置预览显示图片',
                icon: 'none'
              });
            }
          } else if (base64Res.result.fileUrl) {
            // 如果返回了直接链接而不是base64
            console.log('接收到直接链接:', base64Res.result.fileUrl);
            
            wx.previewImage({
              urls: [base64Res.result.fileUrl],
              current: base64Res.result.fileUrl,
              fail: previewErr => {
                console.error('预览直接链接失败:', previewErr);
                
                // 在页面中显示图片
                this.setData({
                  previewImageUrls: [base64Res.result.fileUrl],
                  showLocalPreview: true
                });
                
                wx.showToast({
                  title: '使用内置预览显示图片',
                  icon: 'none'
                });
              }
            });
          } else {
            wx.showToast({
              title: '图片数据格式无效',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: base64Res.result ? base64Res.result.message : '无法获取图片',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: base64Err => {
        wx.hideLoading();
        console.error('下载文件为base64失败:', base64Err);
        
        wx.showToast({
          title: '所有获取图片方式都失败',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 从文件ID提取纯路径，无论输入格式如何
  extractFilePath(fileID) {
    if (!fileID) return null;
    
    try {
      // 处理已经是纯路径的情况
      if (fileID.startsWith('/')) {
        return fileID;
      }
      
      // 处理cloud://开头的文件ID
      if (fileID.startsWith('cloud://')) {
        // 找到第一个斜杠，它标志着路径的开始
        const pathStartIndex = fileID.indexOf('/', 8); // 跳过'cloud://'前缀
        if (pathStartIndex !== -1) {
          // 提取纯路径部分（从斜杠开始）
          return fileID.substring(pathStartIndex);
        }
      }
      
      // 如果以上方法都失败，尝试从文件名构建一个合理的路径
      const fileName = fileID.split('/').pop();
      if (fileName) {
        if (fileName.includes('c_area')) {
          return `/analysis_images/c_area/${fileName}`;
        } else if (fileName.includes('t_area')) {
          return `/analysis_images/t_area/${fileName}`;
        }
      }
      
      return null;
    } catch (error) {
      console.error('提取文件路径出错:', error);
      return null;
    }
  },

  // 处理昵称修改
  onNicknameChange: function(e) {
    const newNickname = e.detail.value.trim();
    if (newNickname && newNickname !== this.data.userInfo.nickName) {
      this.updateNickname(newNickname);
    }
  },

  // 在适当位置添加时间格式化函数
  formatDisplayTime: function(createTime, options = {}) {
    console.log('formatDisplayTime调用参数:', { createTime, options });
    
    // 兼容旧的调用方式
    if (typeof options === 'string') {
      // 旧的调用方式: formatDisplayTime('', 'YYYY_MM_DD_HH_MM_SS')
      return this.formatTimeString(options);
    } else if (typeof options === 'object' && options !== null) {
      if (options.tAreaImage || options.cAreaImage || options.resultFilePath) {
        // 新的调用方式: formatDisplayTime(createTime, {tAreaImage: '...', ...})
        const { customName, sessionId, tAreaImage, cAreaImage, resultFilePath } = options;

        // 1. 优先从文件路径中提取时间，因为这是最准确的
        const paths = [tAreaImage, cAreaImage, resultFilePath].filter(Boolean);
        for (const path of paths) {
          console.log('尝试从路径提取时间:', path);
      
          // 增强的正则表达式，可以匹配路径中的时间格式
          const timePatterns = [
            // 匹配路径中的完整时间格式 /YYYY_MM_DD_HH_MM_SS/ 或 /YYYY-MM-DD-HH-MM-SS/
            /[\/\\](\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})[\/\\]/,
            // 匹配单独的时间字符串 YYYY_MM_DD_HH_MM_SS 或 YYYY-MM-DD-HH-MM-SS
            /(\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})/
          ];
          
          // 尝试所有正则表达式
          for (const pattern of timePatterns) {
            const match = path.match(pattern);
            if (match && match[1]) {
              console.log('从路径中成功提取时间:', match[1]);
              return this.formatTimeString(match[1]);
            }
          }
        }
        
        // 2. 尝试从sessionId提取时间
        if (sessionId) {
          const timePatterns = [
            /[\/\\](\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})[\/\\]/,
            /(\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})/
          ];
          
          for (const pattern of timePatterns) {
            const match = sessionId.match(pattern);
            if (match && match[1]) {
              console.log('从sessionId中提取时间:', match[1]);
              return this.formatTimeString(match[1]);
            }
          }
          
          // 如果sessionId本身就是时间格式
          if (/^\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}$/.test(sessionId)) {
            console.log('sessionId本身是时间格式:', sessionId);
            return this.formatTimeString(sessionId);
          }
        }
        
        // 3. 检查customName，但只有当它不是时间格式时才作为真正的自定义名称使用
        if (customName) {
          // 检查customName是否是时间格式
          if (/^\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}$/.test(customName) || 
              /^\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}$/.test(customName)) {
            // 如果是时间格式，将其视为时间戳而非自定义名称
            console.log('customName是时间格式，将其作为时间戳处理:', customName);
            return this.formatTimeString(customName);
          } else {
            // 如果不是时间格式，才作为真正的自定义名称使用
            console.log('使用真正的自定义名称:', customName);
            return customName;
          }
        }
        
        // 如果无法解析为时间，直接返回sessionId
        if (sessionId) {
          return sessionId;
          }
      } else if (options.tAreaImage === undefined && Object.keys(options).length > 0) {
        // 旧的调用方式: formatDisplayTime('', item)
        // 这种情况下，options是整个item对象
        const item = options;
            
        // 1. 优先从路径提取时间
        // 尝试从item的各个字段中提取时间
        const paths = [
          item.tAreaImage,
          item.cAreaImage,
          item.resultFilePath
        ].filter(Boolean);
        
        for (const path of paths) {
          // 增强的正则表达式，可以匹配路径中的时间格式
          const timePatterns = [
            // 匹配路径中的完整时间格式 /YYYY_MM_DD_HH_MM_SS/ 或 /YYYY-MM-DD-HH-MM-SS/
            /[\/\\](\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})[\/\\]/,
            // 匹配单独的时间字符串 YYYY_MM_DD_HH_MM_SS 或 YYYY-MM-DD-HH-MM-SS
            /(\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2})/
          ];
          
          // 尝试所有正则表达式
          for (const pattern of timePatterns) {
            const match = path.match(pattern);
            if (match && match[1]) {
              console.log('从路径中提取到时间:', match[1]);
              return this.formatTimeString(match[1]);
            }
          }
        }
        
        // 2. 尝试从sessionId提取时间
        if (item.sessionId) {
          if (/^\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}$/.test(item.sessionId)) {
            console.log('sessionId是时间格式:', item.sessionId);
            return this.formatTimeString(item.sessionId);
          }
        }
        
        // 3. 检查customName，但只有当它不是时间格式时才作为真正的自定义名称使用
        if (item.customName) {
          if (/^\d{4}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}[-_]\d{2}$/.test(item.customName) || 
              /^\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}$/.test(item.customName)) {
            console.log('customName是时间格式，将其作为时间戳处理:', item.customName);
            return this.formatTimeString(item.customName);
          } else {
            console.log('使用真正的自定义名称:', item.customName);
            return item.customName;
      }
    }
    
        // 如果无法解析为时间，直接返回sessionId
        if (item.sessionId) {
          return item.sessionId;
        }
      }
    }

    // 4. 最后尝试使用createTime
    if (createTime) {
      const date = new Date(createTime);
      if (!isNaN(date)) {
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hour = ('0' + date.getHours()).slice(-2);
        const minute = ('0' + date.getMinutes()).slice(-2);
        const second = ('0' + date.getSeconds()).slice(-2);
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      }
    }

    return '时间未知';
  },
  
  // 辅助函数：格式化时间字符串
  formatTimeString: function(timeStr) {
    if (!timeStr) return '时间未知';
    
    // 统一格式（将破折号转为下划线）
    const normalizedStr = timeStr.replace(/-/g, '_');
    
    // 匹配YYYY_MM_DD_HH_MM_SS格式
    const timePattern = /^(\d{4})_(\d{2})_(\d{2})_(\d{2})_(\d{2})_(\d{2})$/;
    const match = timePattern.exec(normalizedStr);
    
    if (match && match.length >= 7) {
      const year = match[1];
      const month = match[2];
      const day = match[3];
      const hour = match[4];
      const minute = match[5];
      const second = match[6];
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
    
    return timeStr;
  },

  // 格式化文件ID，保留完整的环境信息
  formatFileID: function(fileID) {
    if (!fileID) return '';
    
    console.log('处理原始文件ID:', fileID);
    
    try {
      // 检查是否已经是有效的云文件ID格式
      if (fileID.startsWith('cloud://')) {
        // 尝试提取完整的环境+存储空间ID部分
        const pathStartIndex = fileID.indexOf('/', 8); // 跳过'cloud://'前缀
        
        if (pathStartIndex !== -1) {
          const envFullId = fileID.substring(8, pathStartIndex);
          // 检查是否已包含完整环境信息
          if (envFullId.includes('.')) {
            // 已经是完整格式，直接返回
            console.log('文件ID已包含完整环境信息，无需修改');
            return fileID;
          } else {
            // 只有环境ID，没有存储空间ID
            // 提取纯路径部分
            const purePath = fileID.substring(pathStartIndex);
            
            // 尝试从应用程序获取完整的环境ID
            const app = getApp();
            const fullEnvId = app.globalData.cloudFullEnvId || `${envFullId}.776c_${envFullId}_1329876191`;
            
            const result = `cloud://${fullEnvId}${purePath}`;
            console.log('添加存储空间ID后的文件ID:', result);
            return result;
          }
        }
      }
      
      // 处理纯路径的情况
      if (fileID.startsWith('/')) {
        const app = getApp();
        // 使用应用程序中的完整环境ID或构建一个
        const envId = app.globalData.cloudEnvId || 'wlksapp_4g54hauu5cbf43dc';
        const fullEnvId = app.globalData.cloudFullEnvId || `${envId}.776c_${envId}_1329876191`;
        
        const result = `cloud://${fullEnvId}${fileID}`;
        console.log('从纯路径构建的文件ID:', result);
        return result;
      }
      
      // 如果以上都失败，尝试从文件名构建一个路径
      const fileName = fileID.split('/').pop();
      if (fileName) {
        // 确定类型并构建路径
        let path = '';
        if (fileName.includes('c_area')) {
          path = `/analysis_images/c_area/${fileName}`;
        } else if (fileName.includes('t_area')) {
          path = `/analysis_images/t_area/${fileName}`;
        } else {
          // 无法确定路径，返回原始ID
          console.warn('无法解析文件路径，保持原始fileID:', fileID);
          return fileID;
        }
        
        // 构建完整的文件ID
        const app = getApp();
        const envId = app.globalData.cloudEnvId || 'wlksapp_4g54hauu5cbf43dc';
        const fullEnvId = app.globalData.cloudFullEnvId || `${envId}.776c_${envId}_1329876191`;
        
        const result = `cloud://${fullEnvId}${path}`;
        console.log('从文件名构建的文件ID:', result);
        return result;
      }
      
      // 如果无法处理，保持原样
      console.warn('无法处理的文件ID格式，保持原始fileID:', fileID);
      return fileID;
    } catch (error) {
      console.error('格式化文件ID出错:', error);
      return fileID;
    }
  },

  // 保留原来的查看图片方法（如果需要同时查看所有图片的功能）
  viewImages() {
    const index = this.data.currentMediaIndex;
    if (index === null) return;
    
    const currentData = this.data.storedData[index];
    // 收集有效的图片fileID
    const imageFileIDs = [];
    
    if (currentData.cAreaImage) {
      const formattedCAreaID = this.formatFileID(currentData.cAreaImage);
      console.log('C区图片原始ID:', currentData.cAreaImage);
      console.log('C区图片格式化后ID:', formattedCAreaID);
      imageFileIDs.push(formattedCAreaID);
    }
    
    if (currentData.tAreaImage) {
      const formattedTAreaID = this.formatFileID(currentData.tAreaImage);
      console.log('T区图片原始ID:', currentData.tAreaImage);
      console.log('T区图片格式化后ID:', formattedTAreaID);
      imageFileIDs.push(formattedTAreaID);
    }
    
    if (imageFileIDs.length > 0) {
      console.log('正在获取图片临时链接:', imageFileIDs);
      // 显示加载中
      wx.showLoading({
        title: '正在加载图片...',
        mask: true
      });
      
      // 使用双重方式获取图片URL - 方式1: 直接getTempFileURL
      wx.cloud.getTempFileURL({
        fileList: imageFileIDs,
        success: res => {
          console.log('获取图片临时链接成功:', res);
          
          if (res.fileList && res.fileList.length > 0) {
            // 过滤掉获取失败的图片链接
            const imageUrls = res.fileList
              .filter(file => file.status === 0 && file.tempFileURL)
              .map(file => file.tempFileURL);
            
            console.log('有效的图片链接:', imageUrls);
            
            if (imageUrls.length > 0) {
              wx.hideLoading();
              this.previewImages(imageUrls);
              return;
            }
          }
          
          // 如果直接方式失败，尝试第二种方式: 通过云函数
          this.getImagesViaCloudFunction(currentData);
        },
        fail: err => {
          console.error('获取图片临时链接失败:', err);
          // 失败时尝试第二种方式
          this.getImagesViaCloudFunction(currentData);
        }
      });
    } else {
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
    }
  },
  
  // 通过云函数获取图片
  getImagesViaCloudFunction(data) {
    console.log('尝试通过云函数获取图片URL');
    const imageRequests = [];
    
    if (data.cAreaImage) {
      imageRequests.push({
        sessionId: data.sessionId,
        mediaType: 'cAreaImage'
      });
    }
    
    if (data.tAreaImage) {
      imageRequests.push({
        sessionId: data.sessionId, 
        mediaType: 'tAreaImage'
      });
    }
    
    if (imageRequests.length === 0) {
      wx.hideLoading();
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
      return;
    }
    
    let completedRequests = 0;
    const imageUrls = [];
    
    // 处理每个请求完成的逻辑
    const handleRequestComplete = () => {
      completedRequests++;
      if (completedRequests === imageRequests.length) {
        wx.hideLoading();
        if (imageUrls.length > 0) {
          this.previewImages(imageUrls);
        } else {
          wx.showToast({
            title: '获取图片失败',
            icon: 'none'
          });
        }
      }
    };
    
    // 逐个请求图片URL
    imageRequests.forEach(request => {
      wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getMediaFile',
          sessionId: request.sessionId,
          mediaType: request.mediaType
        },
        success: res => {
          console.log(`云函数获取${request.mediaType}结果:`, res);
          if (res.result && res.result.success && res.result.fileUrl) {
            imageUrls.push(res.result.fileUrl);
          }
          handleRequestComplete();
        },
        fail: err => {
          console.error(`云函数获取${request.mediaType}失败:`, err);
          handleRequestComplete();
        }
      });
    });
  },
  
  // 预览图片
  previewImages(urls) {
    if (!urls || urls.length === 0) {
      wx.showToast({
        title: '无有效图片',
        icon: 'none'
      });
      return;
    }
    
    // 输出URL信息，便于调试
    urls.forEach((url, index) => {
      console.log(`图片${index+1} URL:`, url);
    });
    
    // 使用微信预览图片接口
    wx.previewImage({
      urls: urls,
      current: urls[0],
      success: () => {
        console.log('预览图片成功');
      },
      fail: err => {
        console.error('预览图片失败:', err);
        
        // 提供备用显示方式：在当前页面添加图片元素
        this.setData({
          previewImageUrls: urls,
          showLocalPreview: true
        });
        
        wx.showToast({
          title: '图片预览方式切换',
          icon: 'none'
        });
      }
    });
    
    this.hideMediaSelect();
  },

  // 修改视频播放错误处理
  onVideoError: function(e) {
    console.error('视频播放错误:', e.detail);
    
    // 获取当前播放的视频URL
    const videoUrl = this.data.currentVideoUrl;
    console.log('出错的视频URL:', videoUrl);
    
    // 如果URL包含云存储地址和时间戳，尝试刷新时间戳
    if (videoUrl && videoUrl.includes('tcb.qcloud.la')) {
      const index = this.data.currentMediaIndex;
      if (index !== null && this.data.storedData[index]) {
        const currentData = this.data.storedData[index];
        
        wx.showToast({
          title: '视频链接已过期，正在重新获取',
          icon: 'none',
          duration: 2000
        });
        
        // 延迟一秒后尝试使用备用方法获取视频
        setTimeout(() => {
          this.closeVideoPlayer();
          this.fallbackGetVideo(currentData);
        }, 1000);
        
        return;
      }
    }
    
    // 一般错误处理
    wx.showToast({
      title: '视频播放失败',
      icon: 'none',
      duration: 2000
    });
    
    // 关闭视频播放器
    this.setData({
      showVideoPlayer: false,
      currentVideoUrl: ''
    });
  },

  // 处理文件URL路径，确保格式正确
  processFileURL(url) {
    if (!url) return '';

    // 如果已经是正确的URL格式，仅需修正域名格式和处理查询参数
    if (url.startsWith('https://') || url.startsWith('http://')) {
      // 替换域名中的下划线为连字符，统一格式
      let processedUrl = url.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
      
      // 检查是否是视频URL
      const isVideo = url.includes('.mp4') || url.includes('/videos/');
      console.log('URL类型检查:', isVideo ? '视频URL' : '图片URL');
      
      if (isVideo) {
        // 视频URL需要保留查询参数和签名，否则在浏览器中无法播放
        // 检查是否有查询参数
        const urlParts = processedUrl.split('?');
        
        if (urlParts.length > 1) {
          console.log('视频URL已有查询参数:', urlParts[1]);
          // 检查是否需要添加时间戳
          if (!urlParts[1].includes('t=')) {
            const timestamp = Date.now();
            processedUrl = `${processedUrl}&t=${timestamp}`;
            console.log('添加时间戳参数:', processedUrl);
          }
        } else {
          // 没有查询参数，添加时间戳
          const timestamp = Date.now();
          processedUrl = `${processedUrl}?t=${timestamp}`;
          console.log('添加时间戳参数:', processedUrl);
        }
      } else {
        // 非视频URL(图片等)，移除时间戳参数以保持和图片URL相同的格式
        processedUrl = processedUrl.split('?')[0];
      }
      
      console.log('处理后的URL:', processedUrl);
      return processedUrl;
    }

    // 检查是否是简化的视频路径
    if (url.includes('/videos/') && !url.includes('/users/')) {
      console.log('检测到简化视频路径，尝试获取完整路径');
      // 获取当前用户信息
      const userPath = this.getCachedDataPath();
      if (userPath) {
        // 从文件路径提取文件名
        const fileName = url.split('/').pop();
        if (fileName) {
          // 构建完整路径
          const fullPath = `${userPath}/videos/${fileName}`;
          console.log('构建的完整视频路径:', fullPath);
          
          // 获取文件下载地址
          return this.getFileDownloadURL(fullPath);
        }
      }
    }

    // 尝试获取文件下载地址
    return this.getFileDownloadURL(url);
  },

  // 获取文件下载URL
  async getFileDownloadURL(fileID) {
    try {
      console.log('获取文件下载URL, fileID:', fileID);
      
      // 尝试通过云函数获取临时URL
      const result = await wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getTempFileURL',
          fileID: fileID
        }
      });
      
      console.log('获取临时URL结果:', result);
      
      if (result && result.result && result.result.success && result.result.fileUrl) {
        // 确保URL格式正确，移除时间戳参数
        let fileUrl = result.result.fileUrl;
        fileUrl = fileUrl.split('?')[0]; // 移除任何查询参数
        
        console.log('成功获取并处理文件URL:', fileUrl);
        return fileUrl;
      } else {
        console.error('获取临时URL失败:', result && result.result ? result.result.message : '未知错误');
        
        // 尝试直接构建URL
        if (fileID.startsWith('cloud://')) {
          const envId = fileID.substring(8, fileID.indexOf('/', 8));
          const filePath = fileID.substring(fileID.indexOf('/', 8) + 1);
          
          // 规范化环境ID，替换下划线为连字符
          const normalizedEnvId = envId.replace(/_/g, '-');
          const directUrl = `https://776c-${normalizedEnvId}-1329876191.tcb.qcloud.la/${filePath}`;
          
          console.log('构建的直接URL:', directUrl);
          return directUrl;
        }
        
        return '';
      }
    } catch (error) {
      console.error('获取文件下载URL失败:', error);
      return '';
    }
  },

  // 尝试通过文件路径获取视频URL
  async tryGetTempVideoUrl(videoPath) {
    if (!videoPath) return '';
    
    try {
      console.log('尝试获取视频临时URL, 路径:', videoPath);
      
      // 通过processFileURL处理文件URL，确保格式正确
      // 这将保留视频URL的查询参数和签名
      const processedUrl = this.processFileURL(videoPath);
      if (processedUrl) {
        console.log('成功处理视频URL:', processedUrl);
        return processedUrl;
      }
      
      // 如果处理失败，尝试使用云函数获取
      const result = await wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getTempFileURL',
          fileID: videoPath
        }
      });
      
      console.log('通过云函数获取视频URL结果:', result);
      
      if (result && result.result && result.result.success && result.result.fileUrl) {
        // 使用processFileURL处理URL，确保格式正确并保留查询参数
        const fileUrl = this.processFileURL(result.result.fileUrl);
        
        console.log('成功获取视频URL:', fileUrl);
        return fileUrl;
      }
      
      console.error('获取视频URL失败');
      return '';
    } catch (error) {
      console.error('获取视频临时URL失败:', error);
      return '';
    }
  },

  // 备用方法：如果主方法失败，尝试获取视频
  async fallbackGetVideo(videoData) {
    console.log('使用备用方法获取视频URL');
    
    // 检查视频数据
    if (!videoData) {
      console.error('备用方法：无效的视频数据');
      return;
    }
    
    console.log('备用方法数据:', videoData);
    
    // 获取视频路径
    let videoPath = videoData.videoFile || videoData.videoPath;
    
    if (!videoPath) {
      console.error('备用方法：无视频路径');
      wx.showToast({
        title: '无有效视频路径',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '尝试获取视频...',
      mask: true
    });
    
    try {
      // 首先通过processFileURL处理路径
      // 这将保留视频URL的查询参数和签名
      const processedUrl = this.processFileURL(videoPath);
      if (processedUrl) {
        console.log('备用方法通过处理获得视频URL:', processedUrl);
        this.playVideoInline(processedUrl);
        wx.hideLoading();
        return;
      }
      
      // 尝试获取媒体文件
      let sessionId = videoData.groupName || videoData.sessionId;
      
      if (!sessionId) {
        console.log('无会话ID，尝试通过其他方式获取视频');
        // 尝试使用fileID直接获取临时URL
        await this.getDirectTempUrl(videoPath);
        wx.hideLoading();
        return;
      }
      
      console.log('尝试通过会话ID获取视频:', sessionId);
      
      const result = await wx.cloud.callFunction({
        name: 'getUserData',
        data: {
          action: 'getMediaFile',
          sessionId: sessionId,
          mediaType: 'video'
        }
      });
      
      console.log('备用方法获取视频结果:', result);
      
      if (result && result.result && result.result.success && result.result.fileUrl) {
        // 使用processFileURL处理URL，确保格式正确并保留查询参数
        const fileUrl = this.processFileURL(result.result.fileUrl);
        
        console.log('备用方法成功获取视频URL:', fileUrl);
        this.playVideoInline(fileUrl);
        wx.hideLoading();
        return;
      }
      
      console.error('备用方法获取视频URL失败');
      
      // 最后尝试：直接构建URL (包含时间戳)
      if (videoPath.includes('/users/') && videoPath.includes('/videos/')) {
        // 提取文件路径
        let filePath = videoPath;
        if (videoPath.startsWith('cloud://')) {
          const pathStartIndex = videoPath.indexOf('/', 8);
          if (pathStartIndex !== -1) {
            filePath = videoPath.substring(pathStartIndex + 1);
          }
        }
        
        // 构建直接URL，添加时间戳
        const timestamp = Date.now();
        const directUrl = `https://776c-wlksapp-4g54hauu5cbf43dc-1329876191.tcb.qcloud.la/${filePath}?t=${timestamp}`;
        console.log('构建的直接视频URL:', directUrl);
        this.playVideoInline(directUrl);
      } else {
        wx.showToast({
          title: '无法获取视频',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('备用方法获取视频URL失败:', error);
      wx.showToast({
        title: '获取视频失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 辅助函数：直接获取文件临时URL
  async getDirectTempUrl(fileID) {
    if (!fileID) {
      console.error('getDirectTempUrl: 无效的文件ID');
      return;
    }
    
    console.log('尝试直接获取临时URL, fileID:', fileID);
    
    // 确保文件ID格式正确
    let formattedFileID = fileID;
    
    if (!fileID.startsWith('cloud://')) {
      // 添加cloud://前缀
      formattedFileID = `cloud://wlksapp-4g54hauu5cbf43dc/${fileID}`;
    }
    
    // 替换文件ID中的下划线为连字符
    formattedFileID = formattedFileID.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
    
    console.log('格式化后的文件ID:', formattedFileID);
    
    try {
      // 直接获取临时URL
      const result = await wx.cloud.getTempFileURL({
        fileList: [formattedFileID]
      });
      
      console.log('直接获取临时URL结果:', result);
      
      if (result && result.fileList && result.fileList.length > 0 && 
          result.fileList[0].status === 0 && result.fileList[0].tempFileURL) {
        
        // 处理获取到的URL
        const tempUrl = this.processFileURL(result.fileList[0].tempFileURL);
        console.log('成功获取到临时URL:', tempUrl);
        
        // 播放视频
        this.playVideoInline(tempUrl);
        return true;
      }
      
      // 如果获取临时URL失败，尝试使用直接URL
      // 提取路径部分
      const pathPart = fileID.replace(/^cloud:\/\/[^\/]+\//, '');
      if (pathPart && (pathPart.includes('/videos/') || pathPart.includes('.mp4'))) {
        // 构建直接URL
        const timestamp = Date.now();
        const directUrl = `https://776c-wlksapp-4g54hauu5cbf43dc-1329876191.tcb.qcloud.la/${pathPart}?t=${timestamp}`;
        console.log('构建的直接URL:', directUrl);
        
        // 播放视频
        this.playVideoInline(directUrl);
        return true;
      }
      
      console.error('无法获取有效URL');
      wx.showToast({
        title: '无法获取视频URL',
        icon: 'none'
      });
      return false;
    } catch (error) {
      console.error('直接获取临时URL失败:', error);
      wx.showToast({
        title: '获取视频URL失败',
        icon: 'none'
      });
      return false;
    }
  },
  
  // 在当前页面内播放视频
  playVideoInline(videoUrl) {
    if (!videoUrl) {
      console.error('playVideoInline: 无效的视频URL');
      wx.showToast({
        title: '无效的视频URL',
        icon: 'none'
      });
      return;
    }
    
    console.log('在页面内播放视频:', videoUrl);
    
    // 设置播放器URL并显示
    this.setData({
      showVideoPlayer: true,
      currentVideoUrl: videoUrl
    });
  },

  // --- 新增搜索相关方法 ---
  handleSearchInput: function(e) {
    const query = e.detail.value;
    this.setData({
      searchQuery: query
    });
    // 添加防抖
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.filterData(query);
    }, 300); 
  },

  filterData: function(query) {
    const lowerCaseQuery = query.toLowerCase().trim(); // 转小写并去空格
    console.log(`开始过滤，搜索词: "${lowerCaseQuery}"`); // 日志1：开始过滤

    if (!lowerCaseQuery) {
      // 如果搜索词为空，显示所有数据
      console.log("搜索词为空，显示所有数据"); // 日志2：搜索词为空
      this.setData({
        filteredStoredData: [...this.data.storedData], // 使用展开运算符创建副本
        selectedDataIndexes: [], // 清空选择
        selectedVideoIndexes: [],
        selectedImageIndexes: [],
        canExport: false
      });
      return;
    }

    const filtered = this.data.storedData.filter(item => { // index 参数不再需要
      // 构建数据组标签，例如 "数据组1", "数据组 1"
      const dataGroupTag = `数据组${item.originalIndex + 1}`;
      const dataGroupTagWithSpace = `数据组 ${item.originalIndex + 1}`;
      
      const displayTime = String(item.displayTime || '').toLowerCase();
      const sessionId = String(item.sessionId || '').toLowerCase();
      const customName = String(item.customName || '').toLowerCase();
      const tcValue = String(item.tcValue || '').toLowerCase();
      const cAreaValue = String(item.cAreaValue || '').toLowerCase();
      const tAreaValue = String(item.tAreaValue || '').toLowerCase();

      return displayTime.includes(lowerCaseQuery) ||
             sessionId.includes(lowerCaseQuery) ||
             customName.includes(lowerCaseQuery) ||
             tcValue.includes(lowerCaseQuery) ||
             cAreaValue.includes(lowerCaseQuery) ||
             tAreaValue.includes(lowerCaseQuery) ||
             dataGroupTag.includes(lowerCaseQuery) ||
             dataGroupTagWithSpace.includes(lowerCaseQuery) ||
             String(item.originalIndex + 1) === lowerCaseQuery; // 直接搜索数字
    });

    this.setData({
      filteredStoredData: filtered,
      selectedDataIndexes: [],
      selectedVideoIndexes: [],
      selectedImageIndexes: [],
      canExport: false
    });
  },
  
  // 切换分析类型（正式/临时）
  toggleAnalysisType: function(e) {
    if (this.data.isMultiSelectMode) return; // 多选模式下不允许切换
    
    const index = e.currentTarget.dataset.index;
    if (isNaN(index) || index < 0 || index >= this.data.filteredStoredData.length) {
      console.error('toggleAnalysisType: Invalid index', index);
      return;
    }
    
    // 获取原始数据项
    const item = this.data.filteredStoredData[index];
    if (!item) {
      console.error('toggleAnalysisType: filteredStoredData中索引无效');
      return;
    }
    
    // 确定要使用的ID (优先级: _id > sessionId > taskId)
    let itemId = null;
    if (item._id && typeof item._id === 'string') {
      itemId = item._id;
      console.log('使用_id作为记录标识:', itemId);
    } else if (item.sessionId && typeof item.sessionId === 'string') {
      itemId = item.sessionId;
      console.log('使用sessionId作为记录标识:', itemId);
    } else if (item.taskId && typeof item.taskId === 'string') {
      itemId = item.taskId;
      console.log('使用taskId作为记录标识:', itemId);
    } else {
      console.error('toggleAnalysisType: 无法确定记录ID', item);
      wx.showToast({
        title: '无法识别记录',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 获取当前的isTemp状态
    const currentIsTemp = !!item.isTemp;
    
    // 创建数据的深拷贝
    const filteredDataCopy = JSON.parse(JSON.stringify(this.data.filteredStoredData));
    const storedDataCopy = JSON.parse(JSON.stringify(this.data.storedData));
    
    // 更新临时显示状态
    filteredDataCopy[index].isTemp = !currentIsTemp;
    
    // 在storedData中查找并更新对应项
    const originalIndex = this.data.storedData.findIndex(dataItem => 
      (dataItem._id && item._id && dataItem._id === item._id) || 
      (dataItem.sessionId && item.sessionId && dataItem.sessionId === item.sessionId) ||
      (dataItem.taskId && item.taskId && dataItem.taskId === item.taskId)
    );
    
    if (originalIndex !== -1) {
      storedDataCopy[originalIndex].isTemp = !currentIsTemp;
    }
    
    // 使用setTimeout避免引起循环更新
    setTimeout(() => {
      this.setData({
        filteredStoredData: filteredDataCopy,
        storedData: storedDataCopy
      }, () => {
        // 在状态更新后再调用服务器端更新
        setTimeout(() => {
          this.updateAnalysisType(itemId, !currentIsTemp);
        }, 0);
      });
    }, 0);
  },
  
  // 更新数据库中的分析类型
  updateAnalysisType: function(itemId, isTemp) {
    if (!itemId) {
      console.error('updateAnalysisType: Invalid itemId');
      return;
    }
    
    console.log(`准备更新分析类型: itemId=${itemId}, isTemp=${isTemp}`);
    
    // 显示加载提示
    wx.showLoading({
      title: '更新中...',
      mask: true
    });
    
    // 调用云函数更新数据库
    wx.cloud.callFunction({
      name: 'getUserData',
      data: {
        action: 'updateAnalysisType',
        itemId: itemId,
        isTemp: isTemp
      },
      success: res => {
        wx.hideLoading();
        console.log('更新分析类型返回结果:', res.result);
        
        if (res.result && res.result.success) {
          // 如果是新创建的记录，提示用户并刷新列表
          if (res.result.isNewRecord) {
            wx.showToast({
              title: '已创建新记录',
              icon: 'success',
              duration: 1500
            });
            
            // 延迟刷新数据列表
            setTimeout(() => {
              this.loadUserStoredData();
            }, 1500);
          } else {
            // 普通成功情况
            wx.showToast({
              title: isTemp ? '已设为临时' : '已设为正式',
              icon: 'success',
              duration: 1500
            });
          }
          
          // 找到并更新本地数据中的对应项
          this.updateLocalDataItemType(itemId, isTemp);
        } else {
          // 处理错误情况
          const errorMsg = (res.result && res.result.message) ? res.result.message : '未知错误';
          console.error('更新分析类型失败:', errorMsg);
          
          // 显示错误提示，并根据错误类型提供选项
          if (errorMsg.includes('未找到')) {
            // 记录不存在的情况，显示模态提示框
            wx.showModal({
              title: '更新失败',
              content: '未找到要更新的记录。是否刷新数据列表重试？',
              confirmText: '刷新',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 用户选择刷新，重新加载数据
                  this.loadUserStoredData();
                } else {
                  // 用户取消，恢复原始状态
                  this.restoreOriginalState(itemId, !isTemp);
                }
              }
            });
          } else {
            // 其他错误情况
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            });
            // 恢复原始状态
            this.restoreOriginalState(itemId, !isTemp);
          }
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('调用更新分析类型云函数失败:', err);
        
        wx.showModal({
          title: '网络错误',
          content: '连接服务器失败，请检查网络连接后重试',
          showCancel: false,
          confirmText: '知道了'
        });
        
        // 恢复原始状态
        this.restoreOriginalState(itemId, !isTemp);
      }
    });
  },
  
  // 更新本地数据中的分析类型
  updateLocalDataItemType: function(itemId, isTemp) {
    try {
      let updated = false;
      
      // 在storedData中查找并更新
      const storedDataCopy = [...this.data.storedData];
      for (let i = 0; i < storedDataCopy.length; i++) {
        const item = storedDataCopy[i];
        // 尝试匹配各种可能的ID
        if ((item._id && item._id === itemId) || 
            (item.sessionId && item.sessionId === itemId) ||
            (item.taskId && item.taskId === itemId)) {
          console.log(`在storedData[${i}]中找到匹配项，更新isTemp=${isTemp}`);
          storedDataCopy[i].isTemp = isTemp;
          updated = true;
        }
      }
      
      // 更新filteredStoredData
      const filteredDataCopy = [...this.data.filteredStoredData];
      for (let i = 0; i < filteredDataCopy.length; i++) {
        const item = filteredDataCopy[i];
        // 尝试匹配各种可能的ID
        if ((item._id && item._id === itemId) || 
            (item.sessionId && item.sessionId === itemId) ||
            (item.taskId && item.taskId === itemId)) {
          console.log(`在filteredStoredData[${i}]中找到匹配项，更新isTemp=${isTemp}`);
          filteredDataCopy[i].isTemp = isTemp;
          updated = true;
        }
      }
      
      // 如果找到并更新了数据，更新状态
      if (updated) {
        this.setData({
          storedData: storedDataCopy,
          filteredStoredData: filteredDataCopy
        });
      } else {
        console.warn(`在本地数据中未找到ID为${itemId}的记录`);
      }
    } catch (error) {
      console.error('更新本地数据类型失败:', error);
    }
  },
  
  // 恢复原始状态
  restoreOriginalState: function(itemId, originalIsTemp) {
    console.log(`更新失败，恢复原始状态: itemId=${itemId}, originalIsTemp=${originalIsTemp}`);
    this.updateLocalDataItemType(itemId, originalIsTemp);
  },

  // --- 搜索相关方法结束 ---

  // --- 搜索框聚焦/失焦处理 ---
  handleFocus: function() {
    this.setData({ isSearchFocused: true });
  },

  handleBlur: function() {
    this.setData({ isSearchFocused: false });
  },
  // --- 聚焦/失焦处理结束 ---

  // 添加tab切换函数
  switchTab: function(e) {
    const page = e.currentTarget.dataset.page;
    
    if (page === this.data.currentTab) {
      return; // 如果点击当前页，不做操作
    }
    
    // 根据页面名称进行跳转
    if (page === 'index') {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    } else if (page === 'addDevice') {
      wx.reLaunch({
        url: '/pages/addDevice/addDevice'
      });
    } else if (page === 'me') {
      wx.reLaunch({
        url: '/pages/me/me'
      });
    }
  },

  preventTouchMove: function() {
    // Do nothing, just prevent the event from propagating
  },
});