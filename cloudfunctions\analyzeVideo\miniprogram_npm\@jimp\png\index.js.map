{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _pngjs = require(\"pngjs\");\n\nvar _utils = require(\"@jimp/utils\");\n\nvar MIME_TYPE = 'image/png'; // PNG filter types\n\nvar PNG_FILTER_AUTO = -1;\nvar PNG_FILTER_NONE = 0;\nvar PNG_FILTER_SUB = 1;\nvar PNG_FILTER_UP = 2;\nvar PNG_FILTER_AVERAGE = 3;\nvar PNG_FILTER_PATH = 4;\n\nvar _default = function _default() {\n  return {\n    mime: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, ['png']),\n    constants: {\n      MIME_PNG: MIME_TYPE,\n      PNG_FILTER_AUTO: PNG_FILTER_AUTO,\n      PNG_FILTER_NONE: PNG_FILTER_NONE,\n      PNG_FILTER_SUB: PNG_FILTER_SUB,\n      PNG_FILTER_UP: PNG_FILTER_UP,\n      PNG_FILTER_AVERAGE: PNG_FILTER_AVERAGE,\n      PNG_FILTER_PATH: PNG_FILTER_PATH\n    },\n    hasAlpha: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, true),\n    decoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, _pngjs.PNG.sync.read),\n    encoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (data) {\n      var png = new _pngjs.PNG({\n        width: data.bitmap.width,\n        height: data.bitmap.height\n      });\n      png.data = data.bitmap.data;\n      return _pngjs.PNG.sync.write(png, {\n        width: data.bitmap.width,\n        height: data.bitmap.height,\n        deflateLevel: data._deflateLevel,\n        deflateStrategy: data._deflateStrategy,\n        filterType: data._filterType,\n        colorType: typeof data._colorType === 'number' ? data._colorType : data._rgba ? 6 : 2,\n        inputHasAlpha: data._rgba\n      });\n    }),\n    \"class\": {\n      _deflateLevel: 9,\n      _deflateStrategy: 3,\n      _filterType: PNG_FILTER_AUTO,\n      _colorType: null,\n\n      /**\n       * Sets the deflate level used when saving as PNG format (default is 9)\n       * @param {number} l Deflate level to use 0-9. 0 is no compression. 9 (default) is maximum compression.\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      deflateLevel: function deflateLevel(l, cb) {\n        if (typeof l !== 'number') {\n          return _utils.throwError.call(this, 'l must be a number', cb);\n        }\n\n        if (l < 0 || l > 9) {\n          return _utils.throwError.call(this, 'l must be a number 0 - 9', cb);\n        }\n\n        this._deflateLevel = Math.round(l);\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      },\n\n      /**\n       * Sets the deflate strategy used when saving as PNG format (default is 3)\n       * @param {number} s Deflate strategy to use 0-3.\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      deflateStrategy: function deflateStrategy(s, cb) {\n        if (typeof s !== 'number') {\n          return _utils.throwError.call(this, 's must be a number', cb);\n        }\n\n        if (s < 0 || s > 3) {\n          return _utils.throwError.call(this, 's must be a number 0 - 3', cb);\n        }\n\n        this._deflateStrategy = Math.round(s);\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      },\n\n      /**\n       * Sets the filter type used when saving as PNG format (default is automatic filters)\n       * @param {number} f The quality to use -1-4.\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      filterType: function filterType(f, cb) {\n        if (typeof f !== 'number') {\n          return _utils.throwError.call(this, 'n must be a number', cb);\n        }\n\n        if (f < -1 || f > 4) {\n          return _utils.throwError.call(this, 'n must be -1 (auto) or a number 0 - 4', cb);\n        }\n\n        this._filterType = Math.round(f);\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      },\n\n      /**\n       * Sets the color type used when saving as PNG format\n       * @param {number} s color type to use 0, 2, 4, 6.\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      colorType: function colorType(s, cb) {\n        if (typeof s !== 'number') {\n          return _utils.throwError.call(this, 's must be a number', cb);\n        }\n\n        if (s !== 0 && s !== 2 && s !== 4 && s !== 6) {\n          return _utils.throwError.call(this, 's must be a number 0, 2, 4, 6.', cb);\n        }\n\n        this._colorType = Math.round(s);\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      }\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}