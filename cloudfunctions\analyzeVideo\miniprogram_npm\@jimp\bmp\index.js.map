{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _bmpJs = _interopRequireDefault(require(\"bmp-js\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar MIME_TYPE = 'image/bmp';\nvar MIME_TYPE_SECOND = 'image/x-ms-bmp';\n\nfunction toAGBR(image) {\n  return (0, _utils.scan)(image, 0, 0, image.bitmap.width, image.bitmap.height, function (x, y, index) {\n    var red = this.bitmap.data[index + 0];\n    var green = this.bitmap.data[index + 1];\n    var blue = this.bitmap.data[index + 2];\n    var alpha = this.bitmap.data[index + 3];\n    this.bitmap.data[index + 0] = alpha;\n    this.bitmap.data[index + 1] = blue;\n    this.bitmap.data[index + 2] = green;\n    this.bitmap.data[index + 3] = red;\n  }).bitmap;\n}\n\nfunction fromAGBR(bitmap) {\n  return (0, _utils.scan)({\n    bitmap: bitmap\n  }, 0, 0, bitmap.width, bitmap.height, function (x, y, index) {\n    var alpha = this.bitmap.data[index + 0];\n    var blue = this.bitmap.data[index + 1];\n    var green = this.bitmap.data[index + 2];\n    var red = this.bitmap.data[index + 3];\n    this.bitmap.data[index + 0] = red;\n    this.bitmap.data[index + 1] = green;\n    this.bitmap.data[index + 2] = blue;\n    this.bitmap.data[index + 3] = bitmap.is_with_alpha ? alpha : 0xff;\n  }).bitmap;\n}\n\nvar decode = function decode(data) {\n  return fromAGBR(_bmpJs[\"default\"].decode(data));\n};\n\nvar encode = function encode(image) {\n  return _bmpJs[\"default\"].encode(toAGBR(image)).data;\n};\n\nvar _default = function _default() {\n  var _decoders, _encoders;\n\n  return {\n    mime: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, ['bmp']),\n    constants: {\n      MIME_BMP: MIME_TYPE,\n      MIME_X_MS_BMP: MIME_TYPE_SECOND\n    },\n    decoders: (_decoders = {}, (0, _defineProperty2[\"default\"])(_decoders, MIME_TYPE, decode), (0, _defineProperty2[\"default\"])(_decoders, MIME_TYPE_SECOND, decode), _decoders),\n    encoders: (_encoders = {}, (0, _defineProperty2[\"default\"])(_encoders, MIME_TYPE, encode), (0, _defineProperty2[\"default\"])(_encoders, MIME_TYPE_SECOND, encode), _encoders)\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}