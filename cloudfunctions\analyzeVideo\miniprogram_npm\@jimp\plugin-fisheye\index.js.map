{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Creates a circle out of an image.\n * @param {object} options (optional) r: radius of effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    fisheye: function fisheye() {\n      var _this = this;\n\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n        r: 2.5\n      };\n      var cb = arguments.length > 1 ? arguments[1] : undefined;\n\n      if (typeof options === 'function') {\n        cb = options;\n        options = {\n          r: 2.5\n        };\n      }\n\n      var source = this.cloneQuiet();\n      var _source$bitmap = source.bitmap,\n          width = _source$bitmap.width,\n          height = _source$bitmap.height;\n      source.scanQuiet(0, 0, width, height, function (x, y) {\n        var hx = x / width;\n        var hy = y / height;\n        var r = Math.sqrt(Math.pow(hx - 0.5, 2) + Math.pow(hy - 0.5, 2));\n        var rn = 2 * Math.pow(r, options.r);\n        var cosA = (hx - 0.5) / r;\n        var sinA = (hy - 0.5) / r;\n        var newX = Math.round((rn * cosA + 0.5) * width);\n        var newY = Math.round((rn * sinA + 0.5) * height);\n        var color = source.getPixelColor(newX, newY);\n\n        _this.setPixelColor(color, x, y);\n      });\n      /* Set center pixel color, otherwise it will be transparent */\n\n      this.setPixelColor(source.getPixelColor(width / 2, height / 2), width / 2, height / 2);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}