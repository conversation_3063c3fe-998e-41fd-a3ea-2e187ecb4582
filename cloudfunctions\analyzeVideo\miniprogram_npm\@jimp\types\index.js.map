{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _timm = require(\"timm\");\n\nvar _jpeg = _interopRequireDefault(require(\"@jimp/jpeg\"));\n\nvar _png = _interopRequireDefault(require(\"@jimp/png\"));\n\nvar _bmp = _interopRequireDefault(require(\"@jimp/bmp\"));\n\nvar _tiff = _interopRequireDefault(require(\"@jimp/tiff\"));\n\nvar _gif = _interopRequireDefault(require(\"@jimp/gif\"));\n\nvar _default = function _default() {\n  return (0, _timm.mergeDeep)((0, _jpeg[\"default\"])(), (0, _png[\"default\"])(), (0, _bmp[\"default\"])(), (0, _tiff[\"default\"])(), (0, _gif[\"default\"])());\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}