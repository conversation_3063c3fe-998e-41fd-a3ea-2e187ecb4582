{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Displaces the image based on the provided displacement map\n * @param {object} map the source Jimp instance\n * @param {number} offset the maximum displacement value\n * @param {function(<PERSON>rro<PERSON>, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    displace: function displace(map, offset, cb) {\n      if ((0, _typeof2[\"default\"])(map) !== 'object' || map.constructor !== this.constructor) {\n        return _utils.throwError.call(this, 'The source must be a Jimp image', cb);\n      }\n\n      if (typeof offset !== 'number') {\n        return _utils.throwError.call(this, 'factor must be a number', cb);\n      }\n\n      var source = this.cloneQuiet();\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var displacement = map.bitmap.data[idx] / 256 * offset;\n        displacement = Math.round(displacement);\n        var ids = this.getPixelIndex(x + displacement, y);\n        this.bitmap.data[ids] = source.bitmap.data[idx];\n        this.bitmap.data[ids + 1] = source.bitmap.data[idx + 1];\n        this.bitmap.data[ids + 2] = source.bitmap.data[idx + 2];\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}