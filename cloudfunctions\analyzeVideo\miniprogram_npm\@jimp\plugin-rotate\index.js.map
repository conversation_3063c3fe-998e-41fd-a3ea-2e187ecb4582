{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Rotates an image clockwise by an arbitrary number of degrees. NB: 'this' must be a Jimp object.\n * @param {number} deg the number of degrees to rotate the image by\n * @param {string|boolean} mode (optional) resize mode or a boolean, if false then the width and height of the image will not be changed\n */\nfunction advancedRotate(deg, mode) {\n  deg %= 360;\n  var rad = deg * Math.PI / 180;\n  var cosine = Math.cos(rad);\n  var sine = Math.sin(rad); // the final width and height will change if resize == true\n\n  var w = this.bitmap.width;\n  var h = this.bitmap.height;\n\n  if (mode === true || typeof mode === 'string') {\n    // resize the image to it maximum dimension and blit the existing image\n    // onto the center so that when it is rotated the image is kept in bounds\n    // http://stackoverflow.com/questions/3231176/how-to-get-size-of-a-rotated-rectangle\n    // Plus 1 border pixel to ensure to show all rotated result for some cases.\n    w = Math.ceil(Math.abs(this.bitmap.width * cosine) + Math.abs(this.bitmap.height * sine)) + 1;\n    h = Math.ceil(Math.abs(this.bitmap.width * sine) + Math.abs(this.bitmap.height * cosine)) + 1; // Ensure destination to have even size to a better result.\n\n    if (w % 2 !== 0) {\n      w++;\n    }\n\n    if (h % 2 !== 0) {\n      h++;\n    }\n\n    var c = this.cloneQuiet();\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n      this.bitmap.data.writeUInt32BE(this._background, idx);\n    });\n    var max = Math.max(w, h, this.bitmap.width, this.bitmap.height);\n    this.resize(max, max, mode);\n    this.blit(c, this.bitmap.width / 2 - c.bitmap.width / 2, this.bitmap.height / 2 - c.bitmap.height / 2);\n  }\n\n  var bW = this.bitmap.width;\n  var bH = this.bitmap.height;\n  var dstBuffer = Buffer.alloc(this.bitmap.data.length);\n\n  function createTranslationFunction(deltaX, deltaY) {\n    return function (x, y) {\n      return {\n        x: x + deltaX,\n        y: y + deltaY\n      };\n    };\n  }\n\n  var translate2Cartesian = createTranslationFunction(-(bW / 2), -(bH / 2));\n  var translate2Screen = createTranslationFunction(bW / 2 + 0.5, bH / 2 + 0.5);\n\n  for (var y = 1; y <= bH; y++) {\n    for (var x = 1; x <= bW; x++) {\n      var cartesian = translate2Cartesian(x, y);\n      var source = translate2Screen(cosine * cartesian.x - sine * cartesian.y, cosine * cartesian.y + sine * cartesian.x);\n      var dstIdx = bW * (y - 1) + x - 1 << 2;\n\n      if (source.x >= 0 && source.x < bW && source.y >= 0 && source.y < bH) {\n        var srcIdx = (bW * (source.y | 0) + source.x | 0) << 2;\n        var pixelRGBA = this.bitmap.data.readUInt32BE(srcIdx);\n        dstBuffer.writeUInt32BE(pixelRGBA, dstIdx);\n      } else {\n        // reset off-image pixels\n        dstBuffer.writeUInt32BE(this._background, dstIdx);\n      }\n    }\n  }\n\n  this.bitmap.data = dstBuffer;\n\n  if (mode === true || typeof mode === 'string') {\n    // now crop the image to the final size\n    var _x = bW / 2 - w / 2;\n\n    var _y = bH / 2 - h / 2;\n\n    this.crop(_x, _y, w, h);\n  }\n}\n\nvar _default = function _default() {\n  return {\n    /**\n     * Rotates the image clockwise by a number of degrees. By default the width and height of the image will be resized appropriately.\n     * @param {number} deg the number of degrees to rotate the image by\n     * @param {string|boolean} mode (optional) resize mode or a boolean, if false then the width and height of the image will not be changed\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    rotate: function rotate(deg, mode, cb) {\n      // enable overloading\n      if (typeof mode === 'undefined' || mode === null) {\n        // e.g. image.resize(120);\n        // e.g. image.resize(120, null, cb);\n        // e.g. image.resize(120, undefined, cb);\n        mode = true;\n      }\n\n      if (typeof mode === 'function' && typeof cb === 'undefined') {\n        // e.g. image.resize(120, cb);\n        cb = mode;\n        mode = true;\n      }\n\n      if (typeof deg !== 'number') {\n        return _utils.throwError.call(this, 'deg must be a number', cb);\n      }\n\n      if (typeof mode !== 'boolean' && typeof mode !== 'string') {\n        return _utils.throwError.call(this, 'mode must be a boolean or a string', cb);\n      }\n\n      advancedRotate.call(this, deg, mode, cb);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}