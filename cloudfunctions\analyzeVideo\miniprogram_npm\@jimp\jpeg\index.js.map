{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _jpegJs = _interopRequireDefault(require(\"jpeg-js\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar MIME_TYPE = 'image/jpeg';\n\nvar _default = function _default() {\n  return {\n    mime: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, ['jpeg', 'jpg', 'jpe']),\n    constants: {\n      MIME_JPEG: MIME_TYPE\n    },\n    decoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, _jpegJs[\"default\"].decode),\n    encoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (image) {\n      return _jpegJs[\"default\"].encode(image.bitmap, image._quality).data;\n    }),\n    \"class\": {\n      // The quality to be used when saving JPEG images\n      _quality: 100,\n\n      /**\n       * Sets the quality of the image when saving as JPEG format (default is 100)\n       * @param {number} n The quality to use 0-100\n       * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n       * @returns {Jimp} this for chaining of methods\n       */\n      quality: function quality(n, cb) {\n        if (typeof n !== 'number') {\n          return _utils.throwError.call(this, 'n must be a number', cb);\n        }\n\n        if (n < 0 || n > 100) {\n          return _utils.throwError.call(this, 'n must be a number 0 - 100', cb);\n        }\n\n        this._quality = Math.round(n);\n\n        if ((0, _utils.isNodePattern)(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      }\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}