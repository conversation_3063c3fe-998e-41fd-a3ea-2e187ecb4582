{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _default = function _default() {\n  return {\n    /**\n     * Uniformly scales the image by a factor.\n     * @param {number} f the factor to scale the image by\n     * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    scale: function scale(f, mode, cb) {\n      if (typeof f !== 'number') {\n        return _utils.throwError.call(this, 'f must be a number', cb);\n      }\n\n      if (f < 0) {\n        return _utils.throwError.call(this, 'f must be a positive number', cb);\n      }\n\n      if (typeof mode === 'function' && typeof cb === 'undefined') {\n        cb = mode;\n        mode = null;\n      }\n\n      var w = this.bitmap.width * f;\n      var h = this.bitmap.height * f;\n      this.resize(w, h, mode);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Scale the image to the largest size that fits inside the rectangle that has the given width and height.\n     * @param {number} w the width to resize the image to\n     * @param {number} h the height to resize the image to\n     * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    scaleToFit: function scaleToFit(w, h, mode, cb) {\n      if (typeof w !== 'number' || typeof h !== 'number') {\n        return _utils.throwError.call(this, 'w and h must be numbers', cb);\n      }\n\n      if (typeof mode === 'function' && typeof cb === 'undefined') {\n        cb = mode;\n        mode = null;\n      }\n\n      var f = w / h > this.bitmap.width / this.bitmap.height ? h / this.bitmap.height : w / this.bitmap.width;\n      this.scale(f, mode);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}