{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Masks a source image on to this image using average pixel colour. A completely black pixel on the mask will turn a pixel in the image completely transparent.\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the horizontal position to blit the image\n * @param {number} y the vertical position to blit the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    mask: function mask(src) {\n      var x = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      var y = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n      var cb = arguments.length > 3 ? arguments[3] : undefined;\n\n      if (!(src instanceof this.constructor)) {\n        return _utils.throwError.call(this, 'The source must be a Jimp image', cb);\n      }\n\n      if (typeof x !== 'number' || typeof y !== 'number') {\n        return _utils.throwError.call(this, 'x and y must be numbers', cb);\n      } // round input\n\n\n      x = Math.round(x);\n      y = Math.round(y);\n      var w = this.bitmap.width;\n      var h = this.bitmap.height;\n      var baseImage = this;\n      src.scanQuiet(0, 0, src.bitmap.width, src.bitmap.height, function (sx, sy, idx) {\n        var destX = x + sx;\n        var destY = y + sy;\n\n        if (destX >= 0 && destY >= 0 && destX < w && destY < h) {\n          var dstIdx = baseImage.getPixelIndex(destX, destY);\n          var data = this.bitmap.data;\n          var avg = (data[idx + 0] + data[idx + 1] + data[idx + 2]) / 3;\n          baseImage.bitmap.data[dstIdx + 3] *= avg / 255;\n        }\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}