{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _omggif = _interopRequireDefault(require(\"omggif\"));\n\nvar _gifwrap = require(\"gifwrap\");\n\nvar MIME_TYPE = 'image/gif';\n\nvar _default = function _default() {\n  return {\n    mime: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, ['gif']),\n    constants: {\n      MIME_GIF: MIME_TYPE\n    },\n    decoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (data) {\n      var gifObj = new _omggif[\"default\"].GifReader(data);\n      var gifData = Buffer.alloc(gifObj.width * gifObj.height * 4);\n      gifObj.decodeAndBlitFrameRGBA(0, gifData);\n      return {\n        data: gifData,\n        width: gifObj.width,\n        height: gifObj.height\n      };\n    }),\n    encoders: (0, _defineProperty2[\"default\"])({}, MIME_TYPE, function (data) {\n      var bitmap = new _gifwrap.BitmapImage(data.bitmap);\n\n      _gifwrap.GifUtil.quantizeDekker(bitmap, 256);\n\n      var newFrame = new _gifwrap.GifFrame(bitmap);\n      var gifCodec = new _gifwrap.GifCodec();\n      return gifCodec.encodeGif([newFrame], {}).then(function (newGif) {\n        return newGif.buffer;\n      });\n    })\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}