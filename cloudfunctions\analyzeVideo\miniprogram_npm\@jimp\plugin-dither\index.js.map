{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Apply a ordered dithering effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction dither(cb) {\n  var rgb565Matrix = [1, 9, 3, 11, 13, 5, 15, 7, 4, 12, 2, 10, 16, 8, 14, 6];\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n    var thresholdId = ((y & 3) << 2) + x % 4;\n    var dither = rgb565Matrix[thresholdId];\n    this.bitmap.data[idx] = Math.min(this.bitmap.data[idx] + dither, 0xff);\n    this.bitmap.data[idx + 1] = Math.min(this.bitmap.data[idx + 1] + dither, 0xff);\n    this.bitmap.data[idx + 2] = Math.min(this.bitmap.data[idx + 2] + dither, 0xff);\n  });\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nvar _default = function _default() {\n  return {\n    dither565: dither,\n    dither16: dither\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}