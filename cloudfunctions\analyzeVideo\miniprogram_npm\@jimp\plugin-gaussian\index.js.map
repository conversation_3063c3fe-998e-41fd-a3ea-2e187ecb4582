{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Applies a true Gaussian blur to the image (warning: this is VERY slow)\n * @param {number} r the pixel radius of the blur\n * @param {function(<PERSON><PERSON><PERSON>, Jim<PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    gaussian: function gaussian(r, cb) {\n      // http://blog.ivank.net/fastest-gaussian-blur.html\n      if (typeof r !== 'number') {\n        return _utils.throwError.call(this, 'r must be a number', cb);\n      }\n\n      if (r < 1) {\n        return _utils.throwError.call(this, 'r must be greater than 0', cb);\n      }\n\n      var rs = Math.ceil(r * 2.57); // significant radius\n\n      var range = rs * 2 + 1;\n      var rr2 = r * r * 2;\n      var rr2pi = rr2 * Math.PI;\n      var weights = [];\n\n      for (var y = 0; y < range; y++) {\n        weights[y] = [];\n\n        for (var x = 0; x < range; x++) {\n          var dsq = Math.pow(x - rs, 2) + Math.pow(y - rs, 2);\n          weights[y][x] = Math.exp(-dsq / rr2) / rr2pi;\n        }\n      }\n\n      for (var _y = 0; _y < this.bitmap.height; _y++) {\n        for (var _x = 0; _x < this.bitmap.width; _x++) {\n          var red = 0;\n          var green = 0;\n          var blue = 0;\n          var alpha = 0;\n          var wsum = 0;\n\n          for (var iy = 0; iy < range; iy++) {\n            for (var ix = 0; ix < range; ix++) {\n              var x1 = Math.min(this.bitmap.width - 1, Math.max(0, ix + _x - rs));\n              var y1 = Math.min(this.bitmap.height - 1, Math.max(0, iy + _y - rs));\n              var weight = weights[iy][ix];\n\n              var _idx = y1 * this.bitmap.width + x1 << 2;\n\n              red += this.bitmap.data[_idx] * weight;\n              green += this.bitmap.data[_idx + 1] * weight;\n              blue += this.bitmap.data[_idx + 2] * weight;\n              alpha += this.bitmap.data[_idx + 3] * weight;\n              wsum += weight;\n            }\n\n            var idx = _y * this.bitmap.width + _x << 2;\n            this.bitmap.data[idx] = Math.round(red / wsum);\n            this.bitmap.data[idx + 1] = Math.round(green / wsum);\n            this.bitmap.data[idx + 2] = Math.round(blue / wsum);\n            this.bitmap.data[idx + 3] = Math.round(alpha / wsum);\n          }\n        }\n      }\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}