{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional)\n * opacity - opacity of the shadow between 0 and 1\n * size,- of the shadow\n * blur - how blurry the shadow is\n * x- x position of shadow\n * y - y position of shadow\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    shadow: function shadow() {\n      var _this = this;\n\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var cb = arguments.length > 1 ? arguments[1] : undefined;\n\n      if (typeof options === 'function') {\n        cb = options;\n        options = {};\n      }\n\n      var _options = options,\n          _options$opacity = _options.opacity,\n          opacity = _options$opacity === void 0 ? 0.7 : _options$opacity,\n          _options$size = _options.size,\n          size = _options$size === void 0 ? 1.1 : _options$size,\n          _options$x = _options.x,\n          x = _options$x === void 0 ? -25 : _options$x,\n          _options$y = _options.y,\n          y = _options$y === void 0 ? 25 : _options$y,\n          _options$blur = _options.blur,\n          blur = _options$blur === void 0 ? 5 : _options$blur; // clone the image\n\n      var orig = this.clone();\n      var shadow = this.clone(); // turn all it's pixels black\n\n      shadow.scan(0, 0, shadow.bitmap.width, shadow.bitmap.height, function (x, y, idx) {\n        shadow.bitmap.data[idx] = 0x00;\n        shadow.bitmap.data[idx + 1] = 0x00;\n        shadow.bitmap.data[idx + 2] = 0x00; // up the opacity a little,\n\n        shadow.bitmap.data[idx + 3] = shadow.constructor.limit255(shadow.bitmap.data[idx + 3] * opacity);\n        _this.bitmap.data[idx] = 0x00;\n        _this.bitmap.data[idx + 1] = 0x00;\n        _this.bitmap.data[idx + 2] = 0x00;\n        _this.bitmap.data[idx + 3] = 0x00;\n      }); // enlarge it. This creates a \"shadow\".\n\n      shadow.resize(shadow.bitmap.width * size, shadow.bitmap.height * size).blur(blur); // Then blit the \"shadow\" onto the background and the image on top of that.\n\n      this.composite(shadow, x, y);\n      this.composite(orig, 0, 0);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}