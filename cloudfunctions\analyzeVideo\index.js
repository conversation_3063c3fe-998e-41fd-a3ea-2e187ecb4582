// 云函数入口文件
const cloud = require('wx-server-sdk');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs-extra');
const path = require('path');
const { exec, execSync } = require('child_process');
const os = require('os');

// 定义环境变量
const CLOUD_ENV = process.env.TCB_ENV || 'wlksapp-4g54hauu5cbf43dc';
// 确保环境ID使用连字符而不是下划线
const NORMALIZED_ENV = CLOUD_ENV.replace(/_/g, '-');
// 完整的环境ID
const FULL_ENV_ID = `${NORMALIZED_ENV}.776c-${NORMALIZED_ENV}-1329876191`;

// 导入Jimp图像处理库
let Jimp;
try {
  Jimp = require('jimp');
  console.log('Jimp库加载成功');
  // 验证Jimp功能
  if (typeof Jimp.read !== 'function') {
    throw new Error('Jimp.read方法不可用');
  }
} catch (error) {
  console.error('Jimp库加载失败:', error.message);
  throw new Error(`Jimp库初始化失败: ${error.message}`);
}

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  timeout: 60000 // 设置云函数调用超时时间为60秒
});

// 输出环境信息
console.log('云函数初始化环境信息:');
console.log('CLOUD_ENV:', CLOUD_ENV);
console.log('NORMALIZED_ENV:', NORMALIZED_ENV);
console.log('FULL_ENV_ID:', FULL_ENV_ID);
console.log('DYNAMIC_CURRENT_ENV:', cloud.DYNAMIC_CURRENT_ENV);

// 初始化数据库
const db = cloud.database();
const tasksCollection = db.collection('videoAnalysisTasks');
const userCollection = db.collection('user');

// 配置FFmpeg路径
let FFMPEG_PATH = path.join(__dirname, 'bin', 'ffmpeg');
// 🆕 全局FFmpeg准备状态
let ffmpegReady = false;

// 在Windows环境下可能需要添加.exe后缀
if (process.platform === 'win32') {
  // 检查是否存在ffmpeg.exe
  if (fs.existsSync(`${FFMPEG_PATH}.exe`)) {
    FFMPEG_PATH = `${FFMPEG_PATH}.exe`;
    console.log('检测到Windows环境，使用ffmpeg.exe');
  }
}

// 在文件开头添加一个全局临时目录常量
const TEMP_DIR = path.join(os.tmpdir(), 'video_analysis_temp');

// 确保全局临时目录存在
try {
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
    console.log(`[初始化] 创建全局临时目录: ${TEMP_DIR}`);
  } else {
    console.log(`[初始化] 全局临时目录已存在: ${TEMP_DIR}`);
  }
} catch (dirError) {
  console.error(`[错误] 创建全局临时目录失败: ${dirError.message}`);
}

// 确保FFmpeg可执行
function setupFFmpeg() {
  try {
    console.log('开始设置FFmpeg...');
    console.log('当前环境信息:', {
      platform: process.platform,
      architecture: process.arch,
      nodeVersion: process.version,
      cwd: process.cwd(),
      dirname: __dirname
    });
    
    // 尝试在多个可能的位置查找FFmpeg
    const possiblePaths = [
      path.join(__dirname, 'bin', 'ffmpeg'),
      path.join(__dirname, 'bin', 'ffmpeg.exe'),
      path.join(process.cwd(), 'bin', 'ffmpeg'),
      path.join(process.cwd(), 'bin', 'ffmpeg.exe')
    ];
    
    if (process.platform === 'win32') {
      // 在Windows上添加更多可能的路径
      possiblePaths.push(
        'ffmpeg.exe', // 直接在PATH中查找
        path.join(process.env.ProgramFiles, 'ffmpeg', 'bin', 'ffmpeg.exe'),
        path.join(process.env['ProgramFiles(x86)'], 'ffmpeg', 'bin', 'ffmpeg.exe')
      );
    } else {
      // 在非Windows系统上添加更多可能的路径
      possiblePaths.push(
        '/usr/bin/ffmpeg',
        '/usr/local/bin/ffmpeg'
      );
    }
    
    // 从当前设置开始检查
    if (fs.existsSync(FFMPEG_PATH)) {
      console.log('当前设置的FFmpeg路径存在:', FFMPEG_PATH);
    } else {
      console.log('当前设置的FFmpeg路径不存在，尝试查找其他位置');
      
      // 尝试每个可能的路径
      let found = false;
      for (const testPath of possiblePaths) {
        if (testPath && fs.existsSync(testPath)) {
          console.log('找到FFmpeg在:', testPath);
          FFMPEG_PATH = testPath;
          found = true;
          break;
        }
      }
      
      if (!found) {
        // 尝试列出bin目录内容，帮助诊断
        try {
          const binDir = path.join(__dirname, 'bin');
          if (fs.existsSync(binDir)) {
            console.log('bin目录内容:', fs.readdirSync(binDir));
            
            // 在Windows上尝试查找任何可执行文件
            if (process.platform === 'win32') {
              const exeFiles = fs.readdirSync(binDir).filter(file => file.endsWith('.exe'));
              if (exeFiles.length > 0) {
                console.log('找到可执行文件:', exeFiles);
                // 尝试使用找到的第一个.exe文件
                FFMPEG_PATH = path.join(binDir, exeFiles[0]);
                found = true;
                console.log('使用找到的可执行文件:', FFMPEG_PATH);
              }
            }
          } else {
            console.log('bin目录不存在');
            
            // 尝试创建bin目录
            try {
              fs.mkdirSync(binDir, { recursive: true });
              console.log('已创建bin目录:', binDir);
            } catch (mkdirErr) {
              console.error('创建bin目录失败:', mkdirErr);
            }
          }
        } catch (e) {
          console.error('列出bin目录内容失败:', e);
        }
        
        if (!found) {
          // 尝试在Windows上使用where命令
          if (process.platform === 'win32') {
            try {
              const whereOutput = execSync('where ffmpeg', { encoding: 'utf8' });
              if (whereOutput && whereOutput.trim()) {
                const systemPath = whereOutput.split('\n')[0].trim();
                console.log('系统中找到FFmpeg:', systemPath);
                FFMPEG_PATH = systemPath;
                found = true;
              }
            } catch (whereErr) {
              console.log('系统中未找到FFmpeg:', whereErr.message);
            }
          } else {
            // 在Linux/Mac上使用which命令
            try {
              const whichOutput = execSync('which ffmpeg', { encoding: 'utf8' });
              if (whichOutput && whichOutput.trim()) {
                FFMPEG_PATH = whichOutput.trim();
                console.log('系统中找到FFmpeg:', FFMPEG_PATH);
                found = true;
              }
            } catch (whichErr) {
              console.log('系统中未找到FFmpeg:', whichErr.message);
            }
          }
        }
      }
      
      if (!found) {
        console.error('无法找到FFmpeg二进制文件');
        throw new Error('找不到FFmpeg可执行文件，请确保已正确安装');
      }
    }

    // 打印FFmpeg文件信息
    try {
      const stat = fs.statSync(FFMPEG_PATH);
      console.log('FFmpeg文件信息:', {
        path: FFMPEG_PATH,
        size: stat.size,
        mode: stat.mode.toString(8), // 权限模式，八进制
        isExecutable: (stat.mode & fs.constants.S_IXUSR) !== 0
      });
      
      // 赋予可执行权限（如果需要）
      if ((stat.mode & fs.constants.S_IXUSR) === 0) {
        console.log('FFmpeg需要赋予可执行权限');
        if (process.platform !== 'win32') {
          try {
            fs.chmodSync(FFMPEG_PATH, 0o755);
            console.log('已使用fs.chmodSync设置FFmpeg可执行权限');
          } catch (chmodError) {
            console.warn('设置权限失败，尝试使用execSync:', chmodError.message);
            try {
              execSync(`chmod +x "${FFMPEG_PATH}"`);
              console.log('已使用execSync设置FFmpeg可执行权限');
            } catch (execError) {
              console.warn('chmod命令执行失败，但将继续尝试使用FFmpeg:', execError.message);
            }
          }
      } else {
          console.log('Windows环境下跳过chmod命令');
      }
    } else {
        console.log('FFmpeg已具有可执行权限，跳过设置');
      }
    } catch (statErr) {
      console.error('获取FFmpeg文件信息失败:', statErr);
      // 继续执行，不要在这里终止流程
    }
    
    // 设置PATH环境变量，确保可以在任何位置调用FFmpeg
    const binDir = path.dirname(FFMPEG_PATH);
    if (process.platform === 'win32') {
      process.env.PATH = `${binDir};${process.env.PATH || ''}`;
    } else {
      process.env.PATH = `${binDir}:${process.env.PATH || ''}`;
    }
    console.log('设置环境变量PATH:', process.env.PATH);
    
    // 测试FFmpeg是否可用
    try {
      console.log('尝试测试FFmpeg版本...');
      // 使用引号包裹路径，处理路径中的空格
      const quotedPath = `"${FFMPEG_PATH}"`;
      const versionOutput = safeExecFFmpeg(`${quotedPath} -version`);
      const firstLine = versionOutput.split('\n')[0];
      console.log('FFmpeg版本测试成功:', firstLine);
      return true;
    } catch (execError) {
      console.error('执行FFmpeg测试失败:', execError);
      
      // 如果是Windows，再尝试一次不带引号的命令
      if (process.platform === 'win32') {
        try {
          console.log('Windows环境下尝试不带引号执行FFmpeg');
          const versionOutput = safeExecFFmpeg(`${FFMPEG_PATH} -version`);
          const firstLine = versionOutput.split('\n')[0];
          console.log('不带引号执行FFmpeg成功:', firstLine);
          return true;
        } catch (noQuoteError) {
          console.error('不带引号执行FFmpeg也失败:', noQuoteError);
        }
      }
      
      // 尝试使用绝对路径执行
      try {
        console.log('使用绝对路径尝试执行FFmpeg');
        const absolutePath = path.resolve(FFMPEG_PATH);
        const quotedPath = `"${absolutePath}"`;
        const versionOutput = safeExecFFmpeg(`${quotedPath} -version`);
        const firstLine = versionOutput.split('\n')[0];
        console.log('使用绝对路径执行FFmpeg成功:', firstLine);
        
        // 更新FFMPEG_PATH为绝对路径
        FFMPEG_PATH = absolutePath;
        return true;
      } catch (absPathError) {
        console.error('使用绝对路径执行FFmpeg也失败:', absPathError);
        
        // 在Windows系统上，尝试直接使用系统命令
        if (process.platform === 'win32') {
          try {
            console.log('尝试使用系统命令ffmpeg.exe');
            const output = safeExecFFmpeg(`ffmpeg.exe -version`);
            console.log('使用系统命令ffmpeg.exe成功:', output.split('\n')[0]);
            // 将PATH更新为使用系统ffmpeg
            FFMPEG_PATH = 'ffmpeg.exe';
            return true;
          } catch (systemCmdError) {
            console.error('使用系统命令ffmpeg.exe也失败:', systemCmdError);
          }
        }
      }
      
      // 如果所有尝试都失败，抛出错误
      throw new Error('无法执行FFmpeg命令，请检查安装: ' + execError.message);
    }
  } catch (error) {
    console.error('设置FFmpeg失败:', error);
    console.log('环境诊断信息:', {
      platform: process.platform,
      architecture: process.arch,
      nodeVersion: process.version,
      cwd: process.cwd(),
      dirname: __dirname,
      path: FFMPEG_PATH,
      env_path: process.env.PATH
    });
    
    // 返回false而不是抛出错误，这样可以在调用处更好地处理
    return false;
  }
}

// 安全地执行FFmpeg命令
function safeExecFFmpeg(command) {
  console.log('执行FFmpeg命令:', command);
  
  try {
    // 处理Windows环境中路径中的反斜杠和空格
    if (process.platform === 'win32') {
      // 检查命令中是否已经包含引号
      if (!command.includes('"') && !command.startsWith('ffmpeg ')) {
        // 为路径添加引号，防止空格问题
        const parts = command.split(' ');
        if (parts.length > 1) {
          // 确保FFmpeg路径有引号
          if (!parts[0].startsWith('"') && !parts[0].endsWith('"')) {
            parts[0] = `"${parts[0]}"`;
          }
          command = parts.join(' ');
        }
      }
      console.log('Windows环境处理后的命令:', command);
    }
    
    // 获取时间戳用于日志记录
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 开始执行命令:`, command);
    
    // 执行命令，捕获详细输出
    const execOptions = {
      maxBuffer: 50 * 1024 * 1024, // 增加缓冲区大小为50MB
      encoding: 'utf8',
      timeout: 300000, // 5分钟超时
      windowsHide: true // 在Windows下隐藏命令窗口
    };
    
    // 在Linux系统中，确保命令能执行（比如添加chmod +x）
    if (process.platform !== 'win32' && !command.startsWith('ffmpeg ') && command.includes('/tmp/')) {
      try {
        // 提取命令中的可执行文件路径
        const execPath = command.split(' ')[0].replace(/"/g, '');
        if (execPath.includes('/tmp/') && fs.existsSync(execPath)) {
          console.log(`确保临时文件有执行权限: ${execPath}`);
          fs.chmodSync(execPath, 0o755);
        }
      } catch (chmodErr) {
        console.warn('设置执行权限失败，继续尝试:', chmodErr.message);
      }
    }
    
    // 使用带引号的命令执行
    let result;
    try {
      result = execSync(command, execOptions);
    } catch (execError) {
      // 如果命令执行失败，尝试使用不同的参数
      console.warn('命令执行失败，尝试使用简化参数:', execError.message);
      
      // 如果是提取帧的命令，尝试简化参数
      if (command.includes('frame_') && command.includes('.jpg')) {
        // 移除一些可能导致问题的参数
        const simplifiedCommand = command
          .replace(/-vsync\s+vfr/, '')  // 移除vsync参数
          .replace(/-q:v\s+\d+/, '-q:v 5')  // 降低质量
          .replace(/select=not\(mod\(n\\,\d+\)\)/, 'fps=1')  // 使用更简单的fps过滤器
          .replace(/select=eq\(pict_type\\,I\)/, 'fps=0.5');  // 替换关键帧选择为低帧率
          
        console.log('尝试简化命令:', simplifiedCommand);
        result = execSync(simplifiedCommand, execOptions);
      } else {
        // 如果不是提取帧的命令，重新抛出错误
        throw execError;
      }
    }
    
    // 限制输出长度，防止日志过大
    const outputPreview = result ? (result.length > 500 ? result.substring(0, 500) + '...(已截断)' : result) : 'N/A';
    
    // 记录成功完成
    console.log(`[${timestamp}] 命令执行成功，输出:`, outputPreview);
    
    return result;
  } catch (error) {
    // 记录详细的错误信息
    console.error('FFmpeg命令执行失败:', {
      command: command,
      errorMessage: error.message,
      errorCode: error.code,
      errorSignal: error.signal,
      stderr: error.stderr ? (error.stderr.toString().substring(0, 500) + '...(已截断)') : 'N/A',
      stdout: error.stdout ? (error.stdout.toString().substring(0, 500) + '...(已截断)') : 'N/A'
    });
    
    // 如果使用的是直接调用的ffmpeg命令（非路径方式）
    if (command.startsWith('ffmpeg ') && process.platform !== 'win32') {
      try {
        // 尝试使用绝对路径方式（如果临时目录中有ffmpeg）
        if (fs.existsSync('/tmp/ffmpeg')) {
          console.log('尝试使用临时目录ffmpeg');
          const newCommand = command.replace('ffmpeg ', '"/tmp/ffmpeg" ');
          const result = execSync(newCommand, {
            maxBuffer: 50 * 1024 * 1024,
            encoding: 'utf8',
            timeout: 300000
          });
          console.log('使用临时目录ffmpeg成功');
          return result;
        }
      } catch (tmpError) {
        console.error('使用临时目录ffmpeg失败:', tmpError);
      }
    }
    
    // 如果是路径问题，尝试使用绝对路径
    if (error.message && (error.message.includes('not found') || 
        error.message.includes('不是内部或外部命令') || 
        error.message.includes('No such file') ||
        error.message.includes('Permission denied'))) {
      
      // 从命令中提取FFmpeg路径
      let ffmpegPathInCmd = command.split(' ')[0];
      // 移除可能包裹的引号
      ffmpegPathInCmd = ffmpegPathInCmd.replace(/^"(.+)"$/, '$1');
      
      // 如果是临时目录中的ffmpeg，确保其有执行权限
      if (ffmpegPathInCmd.includes('/tmp/')) {
        try {
          console.log(`确保临时ffmpeg有执行权限: ${ffmpegPathInCmd}`);
          fs.chmodSync(ffmpegPathInCmd, 0o755);
        } catch (chmodErr) {
          console.warn('设置临时ffmpeg权限失败:', chmodErr.message);
        }
      }
      
      // 获取FFmpeg的绝对路径
      const absoluteFFmpegPath = path.resolve(ffmpegPathInCmd);
      console.log('尝试使用绝对路径:', absoluteFFmpegPath);
      
      // 检查文件是否存在
      if (!fs.existsSync(absoluteFFmpegPath)) {
        console.error('FFmpeg绝对路径也不存在:', absoluteFFmpegPath);
        
        // 尝试一个备选方案 - 使用临时目录的ffmpeg
        if (fs.existsSync('/tmp/ffmpeg')) {
          try {
            console.log('尝试使用临时目录ffmpeg');
            const newCommand = command.replace(ffmpegPathInCmd, '"/tmp/ffmpeg"');
            const result = execSync(newCommand, {
              maxBuffer: 50 * 1024 * 1024,
              encoding: 'utf8',
              timeout: 300000
            });
            console.log('使用临时目录ffmpeg成功');
            return result;
          } catch (tmpFFmpegError) {
            console.error('使用临时目录ffmpeg也失败:', tmpFFmpegError);
          }
        }
        
        // 尝试一个备选方案 - 在Windows系统中直接使用ffmpeg.exe
        if (process.platform === 'win32') {
          try {
            console.log('尝试使用系统ffmpeg.exe');
            const newCommand = command.replace(ffmpegPathInCmd, 'ffmpeg.exe');
            const result = execSync(newCommand, {
              maxBuffer: 50 * 1024 * 1024,
              encoding: 'utf8',
              timeout: 300000,
              windowsHide: true
            });
            console.log('使用系统ffmpeg.exe成功');
            return result;
          } catch (ffmpegExeError) {
            console.error('使用系统ffmpeg.exe也失败:', ffmpegExeError);
          }
        } else {
          // 在非Windows系统上尝试直接使用ffmpeg命令
          try {
            console.log('尝试使用系统ffmpeg命令');
            const newCommand = command.replace(ffmpegPathInCmd, 'ffmpeg');
            const result = execSync(newCommand, {
              maxBuffer: 50 * 1024 * 1024,
              encoding: 'utf8',
              timeout: 300000
            });
            console.log('使用系统ffmpeg命令成功');
            return result;
          } catch (systemFfmpegError) {
            console.error('使用系统ffmpeg命令也失败:', systemFfmpegError);
          }
        }
        
        throw new Error(`FFmpeg不存在，路径: ${ffmpegPathInCmd} 和 ${absoluteFFmpegPath}`);
      }
      
      // 替换命令中的FFmpeg路径并确保使用双引号包裹
      const newCommand = command.replace(ffmpegPathInCmd, `"${absoluteFFmpegPath}"`);
      console.log('修改后的命令:', newCommand);
      
      try {
        // 再次尝试执行
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] 使用绝对路径重新执行命令:`, newCommand);
        
        const result = execSync(newCommand, {
          maxBuffer: 50 * 1024 * 1024,
          encoding: 'utf8',
          timeout: 300000,
          windowsHide: true
        });
        
        // 如果成功，更新全局变量以便后续使用
        FFMPEG_PATH = absoluteFFmpegPath;
        
        // 限制输出长度
        const outputPreview = result ? (result.length > 500 ? result.substring(0, 500) + '...(已截断)' : result) : 'N/A';
        console.log(`[${timestamp}] 使用绝对路径命令成功，输出:`, outputPreview);
        
        return result;
      } catch (retryError) {
        // 记录第二次失败的详细信息
        console.error('使用绝对路径执行FFmpeg也失败:', {
          command: newCommand,
          errorMessage: retryError.message,
          errorCode: retryError.code,
          errorSignal: retryError.signal,
          stderr: retryError.stderr ? retryError.stderr.toString().substring(0, 500) + '...(已截断)' : 'N/A'
        });
        
        // 如果是提取帧的命令，尝试使用更简单的命令
        if (command.includes('frame_') && command.includes('.jpg')) {
          try {
            console.log('尝试使用最简单的帧提取命令');
            // 构建一个非常简单的命令，只提取一帧
            const simpleCommand = `"${absoluteFFmpegPath}" -i "${command.match(/-i\s+"([^"]+)"/)[1]}" -frames:v 1 "${command.match(/frame_\S+\.jpg/)[0]}"`;
            console.log('简化命令:', simpleCommand);
            
            const result = execSync(simpleCommand, {
              maxBuffer: 50 * 1024 * 1024,
              encoding: 'utf8',
              timeout: 300000
            });
            
            console.log('使用简化命令成功');
            return result;
          } catch (simpleError) {
            console.error('使用最简单的命令也失败:', simpleError);
          }
        }
        
        // 记录操作系统信息和环境变量，辅助诊断
        console.log('操作系统信息:', {
          platform: process.platform,
          architecture: process.arch,
          version: process.version,
          cwd: process.cwd(),
          path: process.env.PATH
        });
        
        // 抛出更详细的错误
        throw new Error(`FFmpeg执行失败: ${retryError.message}. 环境信息: 路径=${FFMPEG_PATH}, 工作目录=${process.cwd()}`);
      }
    }
    
    // 对于其他类型的错误，提供更详细的错误信息
    throw new Error(`FFmpeg执行失败: ${error.message}. ${error.stderr ? '错误输出: ' + error.stderr.toString().substring(0, 300) + '...(已截断)' : ''}`);
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('云函数入口 - 视频分析', event);
  
  // 测试云API
  await testCloudAPI();

  // 🆕 重置全局FFmpeg准备状态
  ffmpegReady = false;

  // 首先尝试将FFmpeg复制到临时目录
  console.log('直接尝试临时目录方案...');
  try {
    const tmpFFmpegPath = await copyFFmpegToTmp();
    if (tmpFFmpegPath) {
      FFMPEG_PATH = tmpFFmpegPath;
      ffmpegReady = true;
      console.log('使用临时目录FFmpeg成功:', FFMPEG_PATH);
    } else {
      console.warn('临时目录方案失败，将尝试其他方法');
      
      // 如果临时目录方案失败，尝试常规方式设置FFmpeg
      try {
        ffmpegReady = setupFFmpeg();
        console.log('常规方式设置FFmpeg结果:', ffmpegReady ? '成功' : '失败');
      } catch (error) {
        console.warn('常规方式设置FFmpeg失败:', error.message);
        ffmpegReady = false;
      }
    }
  } catch (tmpError) {
    console.error('临时目录FFmpeg设置失败:', tmpError.message);
    
    // 如果临时目录方案失败，尝试常规方式设置FFmpeg
    try {
      ffmpegReady = setupFFmpeg();
      console.log('常规方式设置FFmpeg结果:', ffmpegReady ? '成功' : '失败');
    } catch (error) {
      console.warn('常规方式设置FFmpeg失败:', error.message);
      ffmpegReady = false;
    }
  }
  
  try {
    console.log('收到事件:', JSON.stringify(event));
    
    // 获取用户ID
    const { OPENID } = cloud.getWXContext();
    const openid = event.openid || OPENID;
    
    // 处理不同的操作模式
    if (event.mode === 'setupDB') {
      // 初始化数据库
      return await setupDB(openid);
    } else if (event.mode === 'checkFFmpeg') {
      // 检查FFmpeg是否可用
      return {
        success: ffmpegReady,
        message: ffmpegReady ? 'FFmpeg设置成功' : 'FFmpeg设置失败，但不阻止其他操作',
        platform: process.platform,
        architecture: process.arch,
        nodeVersion: process.version,
        ffmpegPath: FFMPEG_PATH,
        tmpDirectoryWritable: await checkTmpDirectoryWritable()
      };
    } else if (event.mode === 'createTask') {
      // 创建分析任务
      // 即使FFmpeg设置失败，也允许创建任务，实际处理时再检查
      
      // 创建任务
      const taskResult = await createAnalysisTask(event, openid);
      
      // 如果FFmpeg不可用，添加警告但不阻止创建任务
      if (!ffmpegReady && taskResult.success) {
        taskResult.warning = 'FFmpeg设置遇到问题，将在处理阶段尝试解决';
      }
      
      return taskResult;
    } else if (event.mode === 'getResult') {
      // 获取分析结果
      return await getAnalysisResult(event.taskId);
    } else if (event.mode === 'cancelTask') {
      // 取消分析任务
      return await cancelAnalysisTask(event.taskId);
    } else if (event.mode === 'convertDeviceAvi') {
      // 🆕 设备录制AVI转MP4格式转换
      console.log('开始设备录制AVI到MP4格式转换...');
      return await convertDeviceAviToMp4(event.aviFileID);
    } else if (event.mode === 'processVideo' || event.mode === 'processTask') {
      // 处理视频分析 - 需要确保FFmpeg可用
      if (!ffmpegReady) {
        console.log('处理任务前再次尝试临时目录方案...');
        try {
          const tmpFFmpegPath = await copyFFmpegToTmp();
          if (tmpFFmpegPath) {
            FFMPEG_PATH = tmpFFmpegPath;
            ffmpegReady = true;
            console.log('处理任务前成功设置临时FFmpeg:', FFMPEG_PATH);
          }
        } catch (retryError) {
          console.error('重试临时目录方案失败:', retryError.message);
        }
      }
      
      // 如果依然无法使用FFmpeg，更新任务状态
      if (!ffmpegReady) {
        if (event.taskId) {
          try {
            await tasksCollection.doc(event.taskId).update({
              data: {
                status: 'failed',
                error: 'FFmpeg设置失败，无法进行视频分析',
                updateTime: new Date()
              }
            });
            console.error(`任务 ${event.taskId} 更新为失败状态: FFmpeg设置失败`);
          } catch (updateErr) {
            console.error('更新任务状态失败:', updateErr);
          }
        }
        
        return {
          success: false,
          error: 'FFmpeg设置失败，无法进行视频分析',
          taskId: event.taskId,
          platform: process.platform,
          architecture: process.arch
        };
      }
      
      console.log(`开始处理任务: ${event.taskId}`);
      // 获取任务信息
      const taskRes = await tasksCollection.doc(event.taskId).get();
      const task = taskRes.data;
      // 添加taskId到task对象中
      task.taskId = event.taskId;
      
      // 如果传入了groupPath参数，优先使用传入的
      if (event.groupPath && typeof event.groupPath === 'string') {
        task.groupPath = event.groupPath;
        console.log('使用传入的组路径:', task.groupPath);
      }
      
      // 记录任务信息
      console.log('任务信息:', {
        taskId: event.taskId,
        openid: task.openid,
        videoUrl: task.videoUrl?.substring(0, 50) + '...',
        sessionId: task.sessionId,
        customName: task.customName,
        groupPath: task.groupPath
      });
      console.log(`开始执行视频分析，任务ID: ${event.taskId}`);
      return await processVideoWithFFmpeg(task);
    } else {
      // 默认模式：创建分析任务，但不要求FFmpeg可用
      
      // 创建任务
      const taskResult = await createAnalysisTask(event, openid);
      
      // 如果FFmpeg不可用，添加警告但不阻止创建任务
      if (!ffmpegReady && taskResult.success) {
        taskResult.warning = 'FFmpeg设置问题，任务已创建，处理时会再次尝试解决';
      }
      
      return taskResult;
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '云函数执行失败',
      stack: error.stack
    };
  }
};

// 🆕 设备录制AVI转MP4格式转换函数
async function convertDeviceAviToMp4(aviFileID) {
  console.log(`开始转换设备录制AVI文件: ${aviFileID}`);

  let tempDir, aviPath, mp4Path;

  try {
    // 确保FFmpeg可用
    if (!ffmpegReady) {
      console.log('FFmpeg未就绪，尝试重新设置...');
      try {
        const tmpFFmpegPath = await copyFFmpegToTmp();
        if (tmpFFmpegPath) {
          FFMPEG_PATH = tmpFFmpegPath;
          ffmpegReady = true;
          console.log('FFmpeg重新设置成功:', FFMPEG_PATH);
        } else {
          throw new Error('无法设置FFmpeg');
        }
      } catch (ffmpegError) {
        console.error('FFmpeg设置失败:', ffmpegError);
        throw new Error('FFmpeg不可用，无法进行格式转换');
      }
    }

    // 创建临时工作目录
    tempDir = path.join(os.tmpdir(), `avi_convert_${Date.now()}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    console.log('创建临时目录:', tempDir);

    // 下载AVI文件
    console.log('开始下载AVI文件...');
    const downloadResult = await cloud.downloadFile({
      fileID: aviFileID
    });

    aviPath = path.join(tempDir, 'input.avi');
    fs.writeFileSync(aviPath, downloadResult.fileContent);
    console.log(`AVI文件下载完成: ${fs.statSync(aviPath).size} bytes`);

    // 验证AVI文件
    if (!fs.existsSync(aviPath) || fs.statSync(aviPath).size === 0) {
      throw new Error('AVI文件下载失败或文件为空');
    }

    // 🔧 ECL发光检测专用：MJPEG→H.264直接重编码
    // 设备录制的AVI文件通常是MJPEG编码，直接转换为H.264以确保移动端兼容性
    mp4Path = path.join(tempDir, 'output.mp4');
    console.log('开始MJPEG→H.264转换...');

    // 🔧 高质量移动端兼容的MJPEG→H.264转换（微信QQ专用）
    const convertCmd = [
      `"${FFMPEG_PATH}"`,
      `-i "${aviPath}"`,
      '-c:v libx264',                    // 🔧 H.264编码器
      '-profile:v baseline',             // 🔧 强制Baseline profile（移动端兼容）
      '-level 3.1',                      // 🔧 Level 3.1（适合720p分辨率，避免警告）
      '-pix_fmt yuv420p',                // 🔧 YUV420P像素格式
      '-crf 12',                         // 🔧 CRF 12（科学级近无损质量，适合ECL发光检测）
      '-preset veryslow',                // 🔧 veryslow预设（最高质量，科学级编码）
      '-x264opts keyint=60:min-keyint=60:no-scenecut', // 🔧 科学级优化：2秒关键帧间隔
      '-c:a aac',                        // 🔧 AAC音频编码（MP4标准）
      '-b:a 128k',                       // 🔧 音频比特率128kbps
      '-ar 44100',                       // 🔧 音频采样率44.1kHz
      '-ac 2',                           // 🔧 立体声
      '-movflags +faststart',            // 🔧 优化网络播放
      `"${mp4Path}"`
    ].join(' ');

    console.log('执行转换命令:', convertCmd);

    // 🔍 测试FFmpeg的编码器支持
    console.log('🔍 验证FFmpeg编码器支持...');
    try {
      const encodersCmd = `"${FFMPEG_PATH}" -encoders 2>&1 | grep -E "(libx264|aac)"`;
      const encodersOutput = require('child_process').execSync(encodersCmd, { encoding: 'utf8' });
      console.log('🔍 支持的编码器:', encodersOutput);
    } catch (encoderErr) {
      console.log('⚠️ 无法检查编码器支持，继续转换');
    }

    const convertOutput = safeExecFFmpeg(convertCmd);
    console.log('✅ MJPEG→H.264转换完成');
    console.log('🔍 FFmpeg转换输出:', convertOutput);

    // 🔍 验证转换是否真的成功
    if (!fs.existsSync(mp4Path)) {
      throw new Error('转换失败：输出文件不存在');
    }

    const outputStats = fs.statSync(mp4Path);
    if (outputStats.size === 0) {
      throw new Error('转换失败：输出文件为空');
    }

    console.log(`🔍 转换验证成功：输出文件大小 ${(outputStats.size / 1024).toFixed(1)} KB`);

    // 🔍 检查输出文件的基本信息
    console.log(`🔍 输出文件路径: ${mp4Path}`);
    console.log(`🔍 输出文件大小: ${(outputStats.size / 1024).toFixed(1)} KB`);

    // 🔍 检查文件头，确认是MP4格式
    const fileBuffer = fs.readFileSync(mp4Path);
    const fileHeader = fileBuffer.slice(0, 32).toString('hex');
    console.log('🔍 输出文件头:', fileHeader);

    // MP4文件应该包含ftyp标识
    if (fileHeader.includes('66747970')) { // 'ftyp' in hex
      console.log('✅ 确认：输出文件是标准MP4格式');
    } else {
      console.log('⚠️ 警告：输出文件可能不是标准MP4格式');
    }

    // 🔧 记录原始文件大小
    const originalStats = fs.statSync(aviPath);
    const originalSize = originalStats.size;
    console.log(`🔧 原始AVI文件大小: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);

    const startTime = Date.now();

    console.log('✅ 视频转换完成，开始验证文件...');

    // 🔧 验证转换后文件大小
    let conversionInfo = {
      originalSize: originalSize,
      convertedSize: 0,
      sizeRatio: 0,
      status: 'unknown'
    };

    if (!fs.existsSync(mp4Path)) {
      throw new Error('MP4文件转换失败，输出文件不存在');
    }

    const convertedStats = fs.statSync(mp4Path);
    const convertedSize = convertedStats.size;

    // 🔧 检查文件是否为空或过小
    if (convertedSize === 0) {
      throw new Error('MP4文件转换失败，输出文件为空');
    }

    if (convertedSize < 1024) { // 小于1KB
      throw new Error(`MP4文件转换失败，输出文件过小: ${convertedSize} bytes`);
    }

    const sizeRatio = convertedSize / originalSize;
    conversionInfo.convertedSize = convertedSize;
    conversionInfo.sizeRatio = sizeRatio;

    console.log(`🔧 转换后MP4文件大小: ${(convertedSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`🔧 文件大小比例: ${(sizeRatio * 100).toFixed(1)}%`);

    // 🔍 验证MP4文件头格式
    const mp4Buffer = fs.readFileSync(mp4Path);
    const header = mp4Buffer.slice(0, 32);
    const headerHex = header.toString('hex');
    console.log('🔍 MP4文件头:', headerHex);

    // 检查是否包含MP4标识
    if (headerHex.includes('66747970') || headerHex.includes('6d646174') || headerHex.includes('6d6f6f76')) {
      console.log('✅ MP4文件格式验证成功');
    } else {
      console.warn('⚠️ MP4文件头可能异常，但继续处理');
    }

    // 🔧 记录文件大小变化（移除异常检测，全黑图像压缩率很高是正常的）
    if (sizeRatio >= 1.0) {
      conversionInfo.status = 'size_increased_lossless';
      console.log(`✅ 无损编码：文件大小增大至${(sizeRatio * 100).toFixed(1)}%，符合无损编码预期`);
    } else if (sizeRatio < 0.1) {
      conversionInfo.status = 'size_reduced_high_compression';
      console.log(`📊 高压缩比：文件大小缩小至${(sizeRatio * 100).toFixed(1)}%，可能是全黑或简单图像`);
    } else {
      conversionInfo.status = 'size_normal';
      console.log(`✅ 文件大小变化：${(sizeRatio * 100).toFixed(1)}%`);
    }

    // 🔧 将转换信息保存到全局变量，供后续使用
    global.lastConversionInfo = conversionInfo;
    const convertTime = Date.now() - startTime;

    console.log(`格式转换完成，耗时: ${convertTime}ms`);
    console.log(`MP4文件生成成功: ${convertedSize} bytes`);

    // 🔍 上传前最终验证MP4文件
    console.log('🔍 上传前最终验证MP4文件...');
    const finalMp4Buffer = fs.readFileSync(mp4Path);
    console.log(`🔍 准备上传的MP4文件大小: ${finalMp4Buffer.length} bytes`);

    // 验证文件头是否为有效的MP4格式
    const mp4Header = finalMp4Buffer.slice(0, 8);
    console.log('🔍 MP4文件头:', mp4Header.toString('hex'));

    // 上传MP4文件到云存储
    console.log('📤 开始上传MP4文件到云存储...');
    const cloudPath = `videos/${Date.now()}-device-converted.mp4`;

    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: finalMp4Buffer
    });

    console.log('✅ MP4文件上传成功:', uploadResult.fileID);
    console.log('🔍 上传的文件路径:', cloudPath);

    // 删除原始AVI文件（可选，节省存储空间）
    try {
      await cloud.deleteFile({
        fileList: [aviFileID]
      });
      console.log('原始AVI文件已删除');
    } catch (deleteError) {
      console.warn('删除原始AVI文件失败:', deleteError);
      // 不影响主流程，继续执行
    }

    return {
      success: true,
      mp4FileID: uploadResult.fileID,
      originalSize: originalSize,
      convertedSize: convertedSize,
      convertTime: convertTime,
      message: 'AVI到MP4格式转换成功'
    };

  } catch (error) {
    console.error('AVI到MP4转换失败:', error);
    return {
      success: false,
      error: error.message || 'AVI到MP4转换失败',
      details: error.stack
    };
  } finally {
    // 清理临时文件
    if (tempDir && fs.existsSync(tempDir)) {
      try {
        const files = fs.readdirSync(tempDir);
        for (const file of files) {
          fs.unlinkSync(path.join(tempDir, file));
        }
        fs.rmdirSync(tempDir);
        console.log('临时文件清理完成');
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }
}

// 将FFmpeg复制到临时目录并设置权限
async function copyFFmpegToTmp() {
  try {
    console.log('开始将FFmpeg复制到临时目录...');
    const tmpFFmpegPath = '/tmp/ffmpeg';
    const tmpFFprobePath = '/tmp/ffprobe';
    
    // 如果已存在临时文件，先删除
    if (fs.existsSync(tmpFFmpegPath)) {
      fs.unlinkSync(tmpFFmpegPath);
      console.log('删除已存在的临时FFmpeg');
    }
    
    if (fs.existsSync(tmpFFprobePath)) {
      fs.unlinkSync(tmpFFprobePath);
      console.log('删除已存在的临时FFprobe');
    }
    
    // 确保原始FFmpeg文件存在
    if (!fs.existsSync(FFMPEG_PATH)) {
      console.error('源FFmpeg文件不存在:', FFMPEG_PATH);
      
      // 尝试在不同位置查找FFmpeg
      const possiblePaths = [
        path.join(__dirname, 'bin', 'ffmpeg'),
        path.join(__dirname, 'bin', 'ffmpeg.exe'),
        path.join(process.cwd(), 'bin', 'ffmpeg'),
        path.join(process.cwd(), 'bin', 'ffmpeg.exe'),
        '/var/user/bin/ffmpeg'
      ];
      
      let foundPath = null;
      for (const testPath of possiblePaths) {
        if (fs.existsSync(testPath)) {
          console.log('找到FFmpeg替代位置:', testPath);
          foundPath = testPath;
          break;
        }
      }
      
      if (!foundPath) {
        console.log('无法找到FFmpeg源文件，尝试使用内置二进制文件');
        
        // 使用内置的FFmpeg二进制文件
        const binDir = path.join(__dirname, 'bin');
        if (fs.existsSync(binDir)) {
          const files = fs.readdirSync(binDir);
          console.log('bin目录内容:', files);
          
          // 查找FFmpeg和FFprobe文件
          const ffmpegFile = files.find(file => file.includes('ffmpeg'));
          const ffprobeFile = files.find(file => file.includes('ffprobe'));
          
          if (ffmpegFile) {
            foundPath = path.join(binDir, ffmpegFile);
            console.log('找到内置FFmpeg文件:', foundPath);
          } else {
            throw new Error('无法找到内置FFmpeg文件');
          }
        } else {
          throw new Error('bin目录不存在');
        }
      }
      
      FFMPEG_PATH = foundPath;
    }
    
    // 显示源文件信息，便于调试
    try {
      const srcStat = fs.statSync(FFMPEG_PATH);
      console.log('源FFmpeg文件信息:', {
        path: FFMPEG_PATH,
        size: srcStat.size,
        mode: srcStat.mode.toString(8)
      });
    } catch (statErr) {
      console.warn('获取源文件信息失败:', statErr.message);
    }
    
    // 复制FFmpeg到临时目录
    try {
      console.log(`复制FFmpeg从 ${FFMPEG_PATH} 到 ${tmpFFmpegPath}`);
      fs.copyFileSync(FFMPEG_PATH, tmpFFmpegPath);
      console.log('已使用copyFileSync复制FFmpeg到:', tmpFFmpegPath);
      
      // 复制FFprobe到临时目录
      const ffprobePath = FFMPEG_PATH.replace('ffmpeg', 'ffprobe');
      if (fs.existsSync(ffprobePath)) {
        console.log(`复制FFprobe从 ${ffprobePath} 到 ${tmpFFprobePath}`);
        fs.copyFileSync(ffprobePath, tmpFFprobePath);
        console.log('已使用copyFileSync复制FFprobe到:', tmpFFprobePath);
      } else {
        console.warn('FFprobe源文件不存在:', ffprobePath);
      }
    } catch (copyErr) {
      console.warn('copyFileSync失败，尝试使用readFile+writeFile:', copyErr.message);
      
      // 备选方法：读取后写入
      const fileContent = fs.readFileSync(FFMPEG_PATH);
      fs.writeFileSync(tmpFFmpegPath, fileContent);
      console.log('已使用readFile+writeFile复制FFmpeg到:', tmpFFmpegPath);
      
      // 尝试复制FFprobe
      try {
        const ffprobePath = FFMPEG_PATH.replace('ffmpeg', 'ffprobe');
        if (fs.existsSync(ffprobePath)) {
          const ffprobeContent = fs.readFileSync(ffprobePath);
          fs.writeFileSync(tmpFFprobePath, ffprobeContent);
          console.log('已使用readFile+writeFile复制FFprobe到:', tmpFFprobePath);
        }
      } catch (ffprobeErr) {
        console.warn('复制FFprobe失败:', ffprobeErr.message);
      }
    }
    
    // 设置执行权限 (rwxr-xr-x)
    try {
      fs.chmodSync(tmpFFmpegPath, 0o755);
      console.log('已设置FFmpeg执行权限');
      
      if (fs.existsSync(tmpFFprobePath)) {
        fs.chmodSync(tmpFFprobePath, 0o755);
        console.log('已设置FFprobe执行权限');
      }
    } catch (chmodErr) {
      console.error('设置执行权限失败:', chmodErr.message);
      throw new Error('无法设置FFmpeg执行权限');
    }
    
    // 检查权限是否设置成功
    try {
      const tmpStat = fs.statSync(tmpFFmpegPath);
      console.log('临时FFmpeg文件信息:', {
        path: tmpFFmpegPath,
        size: tmpStat.size,
        mode: tmpStat.mode.toString(8),
        isExecutable: (tmpStat.mode & fs.constants.S_IXUSR) !== 0
      });
      
      if ((tmpStat.mode & fs.constants.S_IXUSR) === 0) {
        console.error('FFmpeg执行权限设置失败');
        throw new Error('无法设置FFmpeg执行权限');
      }
    } catch (statErr) {
      console.error('获取临时文件信息失败:', statErr.message);
    }
    
    // 测试临时目录中的FFmpeg
    try {
      console.log('测试临时目录FFmpeg...');
      const versionOutput = execSync(`${tmpFFmpegPath} -version`, {
        encoding: 'utf8',
        maxBuffer: 10 * 1024 * 1024
      });
      
      const firstLine = versionOutput.split('\n')[0];
      console.log('临时目录FFmpeg测试成功:', firstLine);
      
      return tmpFFmpegPath;
    } catch (testErr) {
      console.error('测试临时目录FFmpeg失败:', testErr.message);
      
      // 尝试不同的执行方法
      try {
        console.log('尝试通过完整路径执行...');
        execSync(`/bin/sh -c '${tmpFFmpegPath} -version'`, {
          encoding: 'utf8',
          maxBuffer: 10 * 1024 * 1024
        });
        console.log('通过/bin/sh执行成功');
        return tmpFFmpegPath;
      } catch (shErr) {
        console.error('/bin/sh执行也失败:', shErr.message);
        throw new Error('无法执行临时目录中的FFmpeg');
      }
    }
  } catch (error) {
    console.error('复制FFmpeg到临时目录失败:', error);
    throw error;
  }
}

// 检查临时目录是否可写
async function checkTmpDirectoryWritable() {
  try {
    const testFile = '/tmp/test_write_' + Date.now();
    fs.writeFileSync(testFile, 'test');
    const readable = fs.existsSync(testFile);
    if (readable) {
      fs.unlinkSync(testFile);
    }
    return readable;
  } catch (error) {
    console.error('检查临时目录可写性失败:', error);
    return false;
  }
}

// 初始化数据库集合
async function setupDB(openid) {
  try {
    console.log('开始初始化数据库集合');
    
    // 检查videoAnalysisTasks集合是否存在
    try {
      await tasksCollection.limit(1).get();
      console.log('videoAnalysisTasks集合已存在');
    } catch (err) {
      if (err.errCode === -502005) { // 集合不存在错误码
        console.log('videoAnalysisTasks集合不存在，尝试创建');
        
        // 创建一个初始记录来确保集合存在
        try {
          // 使用user集合中的用户信息
          const userInfo = await getUserLoginInfo(openid);
          
          if (!userInfo) {
            throw new Error('无法获取用户信息');
          }
          
          // 创建一个临时任务记录
          const tempTask = {
            openid: openid,
            status: 'temp',
            createTime: new Date(),
            updateTime: new Date(),
            isSetupRecord: true,
            userName: userInfo.nickName || '未知用户',
            userAvatar: userInfo.avatarUrl || ''
          };
          
          await db.createCollection('videoAnalysisTasks');
          console.log('成功创建videoAnalysisTasks集合');
          
          // 添加一条临时记录
          const addResult = await tasksCollection.add({
            data: tempTask
          });
          
          console.log('成功添加初始记录:', addResult._id);
          
          // 删除临时记录
          await tasksCollection.doc(addResult._id).remove();
          console.log('成功删除临时记录');
        } catch (createErr) {
          console.error('创建集合失败:', createErr);
          throw createErr;
        }
      } else {
        console.error('检查集合失败:', err);
        throw err;
      }
    }
    
    return {
      success: true,
      message: '数据库集合初始化成功'
    };
  } catch (error) {
    console.error('初始化数据库失败:', error);
      return {
        success: false,
      error: error.message || '初始化数据库失败'
    };
  }
}

// 获取用户登录信息
async function getUserLoginInfo(openid) {
  try {
    if (!openid || typeof openid !== 'string' || openid.trim() === '') {
      console.error('获取用户信息失败: openid无效', { openid });
      throw new Error('无效的用户标识');
    }
    
    console.log('查询用户信息, openid:', openid);
    
    // 查询用户信息
    const userRes = await userCollection.where({
      openid: openid
    }).get();
    
    console.log('用户查询结果:', userRes);
    
    if (userRes.data && userRes.data.length > 0) {
      console.log('找到用户信息:', userRes.data[0]);
      return userRes.data[0];
    }
    
    console.log('未找到用户信息，尝试从login云函数获取');
    
    // 如果在user集合中找不到，尝试调用login云函数获取
    try {
      console.log('调用login云函数获取用户信息...');
      const loginResult = await cloud.callFunction({
        name: 'login',
        data: { openid: openid }  // 传递openid
      });
      
      console.log('login云函数调用结果:', loginResult);
      
      if (loginResult.result && loginResult.result.success && loginResult.result.userInfo) {
        console.log('从login云函数获取到用户信息:', loginResult.result.userInfo);
        return loginResult.result.userInfo;
      } else {
        console.warn('login云函数未返回用户信息:', loginResult);
        throw new Error('无法获取用户信息，请确保已登录');
      }
    } catch (loginErr) {
      console.error('调用login云函数失败:', loginErr);
      throw new Error('获取用户信息失败: ' + (loginErr.message || '调用login失败'));
    }
  } catch (error) {
    console.error('获取用户登录信息失败:', error);
    // 返回明确的错误对象，而不是null
    throw new Error('用户登录验证失败: ' + error.message);
  }
}

// 生成分析会话ID（使用自定义名称或时间戳）
function generateAnalysisSessionId(customName = null) {
  if (customName) {
    return customName;
  }
  
  // 使用更易读的年月日时分秒格式
  const now = new Date();
  const timestamp = 
    now.getFullYear() + '-' + 
    String(now.getMonth() + 1).padStart(2, '0') + '-' + 
    String(now.getDate()).padStart(2, '0') + '-' + 
    String(now.getHours()).padStart(2, '0') + '-' + 
    String(now.getMinutes()).padStart(2, '0') + '-' + 
    String(now.getSeconds()).padStart(2, '0');
  
  return timestamp;
}

// 创建分析任务
async function createAnalysisTask(event, openid) {
  try {
    console.log('开始创建分析任务, 参数:', {
      openid: openid,
      hasVideoUrl: !!event.videoUrl,
      videoUrlType: event.videoUrl ? (typeof event.videoUrl) : 'undefined',
      customName: event.customName,
      hasParameters: !!event.parameters,
      hasDetectionAreas: !!event.parameters?.detectionAreas
    });

    // 验证检测区域参数
    if (!event.parameters?.detectionAreas?.cArea || !event.parameters?.detectionAreas?.tArea) {
      throw new Error('缺少检测区域参数，请确保传入了正确的检测框位置');
    }
    
    // 确保开始时就验证openid
    if (!openid) {
      console.error('创建任务失败: 缺少有效的openid');
      throw new Error('请先登录后再创建分析任务');
    }
    
    // 获取用户信息和数据路径
    let userInfo;
    let dataPath;
    
    try {
      // 调用login云函数获取用户信息和数据路径
      console.log('调用login云函数获取用户信息和数据路径');
      const loginResult = await cloud.callFunction({
        name: 'login',
        data: { openid: openid }
      });
      
      if (loginResult.result && loginResult.result.success) {
        userInfo = loginResult.result.userInfo;
        dataPath = loginResult.result.data?.dataPath;
        
        console.log('从login云函数获取到信息:', {
          hasUserInfo: !!userInfo,
          dataPath: dataPath
        });
        
        if (!userInfo) {
          console.warn('login云函数未返回用户信息');
          // 尝试使用传入的用户信息
          if (event.userInfo && event.userInfo.openid === openid) {
            userInfo = event.userInfo;
            console.log('使用传入的用户信息');
          } else {
            throw new Error('无法获取用户信息');
          }
        }
        
        if (!dataPath) {
          console.warn('login云函数未返回数据路径，使用默认路径');
          dataPath = `users/${openid}_data`;
        }
      } else {
        console.error('login云函数调用失败:', loginResult);
        throw new Error('用户验证失败: 无法获取用户信息');
      }
    } catch (userErr) {
      console.error('获取用户信息失败:', userErr);
      
      // 尝试使用传入的用户信息
      if (event.userInfo && event.userInfo.openid === openid) {
        userInfo = event.userInfo;
        console.log('使用传入的用户信息');
      } else {
        throw new Error('用户验证失败: ' + userErr.message);
      }
      
      // 使用默认数据路径
      dataPath = `users/${openid}_data`;
    }
    
    // 验证视频URL
    if (!event.videoUrl) {
      throw new Error('缺少视频URL参数');
    }
    
    // 检查视频URL格式
    if (typeof event.videoUrl !== 'string' || 
        !(event.videoUrl.startsWith('cloud://') || event.videoUrl.startsWith('http'))) {
      throw new Error('无效的视频URL格式: ' + (typeof event.videoUrl));
    }
    
    // 尝试访问数据库以确保任务集合存在
    try {
      await tasksCollection.count();
      console.log('任务集合已存在，可以正常使用');
    } catch (collectionErr) {
      console.error('任务集合可能不存在，尝试初始化:', collectionErr);
      
      // 尝试初始化数据库
      try {
        await setupDB(openid);
      } catch (setupErr) {
        console.error('初始化数据库失败:', setupErr);
        throw new Error('数据库初始化失败，请联系管理员');
      }
    }
    
    // 生成分析会话ID
    const sessionId = generateAnalysisSessionId(event.customName);
    
    // 构建组路径
    let groupPath;
    
    // 优先使用前端传入的组路径
    if (event.groupPath && typeof event.groupPath === 'string') {
      groupPath = event.groupPath;
      console.log('使用前端传入的组路径:', groupPath);
    } else {
      // 如果没有传入组路径，则使用 login 云函数的路径结构
      // 使用 dataPath 作为基础路径，添加 sessionId 作为子目录
      groupPath = `${dataPath}/${sessionId}`;
      console.log('使用 login 云函数路径结构:', groupPath);
    }
    
    // 创建任务记录
    const taskData = {
      openid: openid,
      videoUrl: event.videoUrl,
      videoTempUrl: event.videoTempUrl || '', // 接收前端传来的临时URL
      parameters: event.parameters || {},
      detectionAreas: event.detectionAreas || {},
      sessionId: sessionId,
      customName: event.customName || sessionId,
      status: 'processing',
      createTime: new Date(),
      updateTime: new Date(),
      userName: userInfo?.nickName || '未知用户',
      userAvatar: userInfo?.avatarUrl || '',
      platform: event.platform || '未知平台',
      deviceInfo: event.deviceInfo || {},
      dataPath: dataPath,
      groupPath: groupPath  // 确保保存组路径
    };
    
    console.log('准备创建分析任务记录');
    
    // 创建任务
    let taskId;
    try {
      const taskRes = await tasksCollection.add({
        data: taskData
      });
      
      taskId = taskRes._id;
      console.log('成功创建任务记录, ID:', taskId);
    } catch (dbError) {
      console.error('数据库操作失败:', dbError);
      throw new Error('创建任务记录失败: ' + (dbError.message || '数据库错误'));
    }
    
    // 异步触发处理任务
    try {
      console.log('异步触发视频处理, 任务ID:', taskId);
      
      // 不等待结果，直接返回
      cloud.callFunction({
        name: 'analyzeVideo',
        data: {
          mode: 'processVideo',
          taskId: taskId,
          groupPath: groupPath // 传递组路径
        }
      }).catch(error => {
        console.error('触发处理任务失败:', error);
        // 在异步回调中更新任务状态为失败
        tasksCollection.doc(taskId).update({
          data: {
            status: 'failed',
            error: error.message || '触发处理任务失败',
            updateTime: new Date()
          }
        });
      });
      
      console.log('已触发处理任务，继续执行');
    } catch (callError) {
      console.error('调用处理函数失败:', callError);
      // 更新任务状态为失败
      await tasksCollection.doc(taskId).update({
        data: {
          status: 'failed',
          error: callError.message || '调用处理函数失败',
          updateTime: new Date()
        }
      });
      
      throw new Error('启动视频处理失败: ' + (callError.message || '云函数调用错误'));
    }
    
    // 返回任务ID和附加信息
    return {
      success: true,
      taskId: taskId,
      sessionId: sessionId,
      openid: openid,
      groupPath: groupPath,
      message: '视频分析任务已创建，请稍后查询结果'
    };
  } catch (error) {
    console.error('创建分析任务失败:', error);
    return {
      success: false,
      error: error.message || '创建分析任务失败',
      details: error.stack
    };
  }
}

// 取消分析任务
async function cancelAnalysisTask(taskId) {
  try {
    if (!taskId) {
      return {
        success: false,
        error: '缺少任务ID'
      };
    }
    
    // 查询任务信息
    const taskRes = await tasksCollection.doc(taskId).get();
    const task = taskRes.data;
    
    if (!task) {
      return {
        success: false,
        error: '任务不存在'
      };
    }
    
    // 只有正在处理中的任务才能取消
    if (task.status !== 'processing') {
      return { 
        success: false, 
        error: `任务状态为 ${task.status}，无法取消` 
      };
    }
    
    // 更新任务状态为已取消
    await updateTaskStatus(taskId, 'cancelled', {
      error: '用户取消任务',
      progressStage: '已取消',
      progressPercent: 0
    });
    
    return {
      success: true,
      message: '任务已取消'
    };
  } catch (error) {
    console.error('取消任务失败:', error);
    return {
      success: false,
      error: `取消任务失败: ${error.message}`
    };
  }
}

// 获取分析结果
async function getAnalysisResult(taskId) {
  try {
    if (!taskId) {
      return {
        success: false,
        error: '缺少任务ID'
      };
    }
    
    // 查询任务信息
    const taskRes = await tasksCollection.doc(taskId).get();
    const task = taskRes.data;
    
    if (!task) {
      return {
        success: false,
        error: '任务不存在'
      };
    }
    
    // 构建返回结果，确保result字段被返回为data字段
    const response = {
      success: true,
      status: task.status,
      error: task.error,
      progressStage: task.progressStage || '处理中',
      progressPercent: task.progressPercent || 0,
      progressDetails: task.progressDetails || '正在分析视频...',
      estimatedTimeRemaining: task.estimatedTimeRemaining || '计算中...',
      analyzedFrames: task.analyzedFrames || 0,
      totalFrames: task.totalFrames || 0,
      taskInfo: {
        createTime: task.createTime,
        updateTime: task.updateTime,
        customName: task.customName,
        groupPath: task.groupPath // 添加组路径到任务信息中
      }
    };
    
    // 如果有result字段，将其作为data字段返回
    if (task.result) {
      response.data = task.result;
      
      // 确保结果中包含组路径
      if (!response.data.groupPath && task.groupPath) {
        response.data.groupPath = task.groupPath;
      }
      
      console.log('返回分析结果数据:', Object.keys(task.result));
    } else {
      console.warn('任务没有result字段:', taskId);
    }
    
    return response;
  } catch (error) {
    console.error('获取分析结果失败:', error);
    return {
      success: false,
      error: error.message || '获取分析结果失败'
    };
  }
}

// 处理视频分析任务
async function processVideoWithFFmpeg(task) {
  console.log(`开始处理视频分析任务: ${task.taskId}`);

  // 🔧 清空全局分析变量，确保每次分析都是新的
  global.pixelAnalysisDetails = [];
  global.pixelAnalysisWarnings = [];
  global.lastConversionInfo = null;

  let videoPath, outputDir;

  try {
    // 从task.parameters中获取检测区域参数
    const detectionAreas = task.parameters?.detectionAreas;

    // 验证检测区域参数
    if (!detectionAreas || !detectionAreas.cArea || !detectionAreas.tArea) {
      throw new Error('缺少检测区域参数，请确保前端传入了正确的检测框位置');
    }

    console.log('检测区域参数:', JSON.stringify(detectionAreas));

    // 验证检测区域格式
    if (!detectionAreas.cArea || !detectionAreas.tArea ||
        typeof detectionAreas.cArea.x !== 'number' ||
        typeof detectionAreas.tArea.x !== 'number') {
      throw new Error('检测区域格式无效，请检查前端传入的检测框参数');
    }

    // 🎨 检查是否需要应用视频参数处理
    const needsParameterProcessing = checkIfParametersNeedProcessing(task.parameters);
    console.log('是否需要参数处理:', needsParameterProcessing);

    // 记录任务开始时间
    const startTime = Date.now();
    // 定义totalTime变量，用于记录处理总时间
    let totalTime = 0;
    
    // 提前处理视频文件和会话路径
    let sessionId = task.sessionId || generateAnalysisSessionId();
    let actualSessionFolder = sessionId; // 初始值使用传入的sessionId
    
    // 检查是否有maxTFrameUrl，从中提取实际的会话文件夹路径
    // 这是确保视频和图片存储在同一文件夹的关键步骤
    if (task.parameters && task.parameters.maxTFrameUrl) {
      try {
        const frameUrl = task.parameters.maxTFrameUrl;
        const matches = frameUrl.match(/users\/[^\/]+_data\/([^\/]+)\/analysis_images/);
        if (matches && matches[1]) {
          actualSessionFolder = matches[1];
          console.log('从maxTFrameUrl提取到实际会话文件夹:', actualSessionFolder);
        }
      } catch (err) {
        console.error('从maxTFrameUrl提取会话文件夹失败:', err.message);
      }
    }
    
    console.log('实际处理将使用会话文件夹:', actualSessionFolder);
    console.log('原始会话ID:', sessionId);
    
    // 确保FFmpeg可用
    if (!fs.existsSync(FFMPEG_PATH)) {
      console.log('FFmpeg不存在，尝试使用临时目录中的FFmpeg');
      FFMPEG_PATH = '/tmp/ffmpeg';
      
      if (!fs.existsSync(FFMPEG_PATH)) {
        console.error('临时目录中的FFmpeg也不存在');
        throw new Error('无法找到FFmpeg，无法进行视频分析');
      }
    }
    
    // 确保FFmpeg有执行权限
    try {
      fs.chmodSync(FFMPEG_PATH, 0o755);
      console.log('已设置FFmpeg执行权限');
    } catch (chmodErr) {
      console.warn('设置FFmpeg执行权限失败:', chmodErr.message);
    }
    
    // 测试FFmpeg是否可用
    try {
      const versionOutput = execSync(`"${FFMPEG_PATH}" -version`, {
        encoding: 'utf8',
        maxBuffer: 10 * 1024 * 1024
      });
      console.log('FFmpeg测试成功:', versionOutput.split('\n')[0]);
    } catch (testErr) {
      console.error('FFmpeg测试失败:', testErr.message);
      throw new Error('FFmpeg不可用，无法进行视频分析');
    }
    
    // 更新任务状态为处理中，并设置初始进度信息
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '准备下载',
      progressPercent: 0,
      estimatedTimeRemaining: '计算中...',
      progressDetails: '正在初始化视频分析任务...'
    });
    
    // 创建临时目录
    outputDir = path.join(os.tmpdir(), `video_analysis_${task.taskId}`);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 下载视频
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '下载视频',
      progressPercent: 5,
      progressDetails: '正在从云存储下载视频文件...'
    });
    
    videoPath = path.join(outputDir, 'input.mp4');
    await downloadVideo(task.videoUrl, videoPath);

    console.log(`视频下载完成: ${videoPath}`);

    // 🚫 参数处理功能已禁用，跳过参数处理步骤
    if (needsParameterProcessing) {
      console.log('🚫 检测到参数处理需求，但功能已禁用，跳过处理');
      // 不再执行参数处理，直接使用原视频
    }

    // 更新进度：下载完成，准备提取帧
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '准备提取帧',
      progressPercent: 15,
      progressDetails: '视频下载完成，准备提取视频帧...'
    });
    
    // 获取视频信息，调用videoInfo云函数
    console.log('调用videoInfo云函数获取视频信息');
    const videoInfoResult = await cloud.callFunction({
      name: 'videoInfo',
      data: {
        mode: 'getVideoInfo',
        videoUrl: task.videoUrl
      }
    });
    
    console.log('videoInfo云函数返回结果:', JSON.stringify(videoInfoResult.result || {}, null, 2));
    
    // 检查videoInfo返回结果
    let videoInfo = { duration: 10, width: 1920, height: 1080 }; // 默认值
    let hasWarning = false;
    
    if (videoInfoResult.result && videoInfoResult.result.success) {
      if (videoInfoResult.result.videoInfo) {
        videoInfo = videoInfoResult.result.videoInfo;
        console.log('成功获取视频信息:', JSON.stringify(videoInfo, null, 2));
        
        // 检查视频时长是否有效
        if (!videoInfo.duration || videoInfo.duration <= 0) {
          console.warn('视频时长无效，使用默认值10秒');
          videoInfo.duration = 10;
          hasWarning = true;
        }
      } else {
        console.warn('videoInfo返回结果缺少videoInfo字段，使用默认值');
        hasWarning = true;
      }
      
      // 检查是否有警告信息
      if (videoInfoResult.result.warning) {
        console.warn('videoInfo返回警告:', videoInfoResult.result.warning);
        hasWarning = true;
      }
    } else {
      console.error('获取视频信息失败:', videoInfoResult.result ? videoInfoResult.result.error : '未知错误');
      console.warn('使用默认视频信息继续处理');
      hasWarning = true;
    }
    
    // 提取视频时长（毫秒）
    const durationMs = videoInfo.duration * 1000 || 10000;
    
    // 估计帧数 (每200ms一帧) - 
    const estimatedFrames = Math.ceil(durationMs / 200);
    
    console.log(`视频时长: ${durationMs}毫秒，预计提取${estimatedFrames}帧`);
    
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '提取帧',
      progressPercent: 20,
      progressDetails: `视频时长: ${(durationMs/1000).toFixed(1)}秒，预计提取${estimatedFrames}帧${hasWarning ? ' (使用估计值)' : ''}`,
      totalFrames: estimatedFrames,
      analyzedFrames: 0
    });
    
    // 提取帧
    const framesDir = path.join(outputDir, 'frames');
    if (!fs.existsSync(framesDir)) {
      fs.mkdirSync(framesDir, { recursive: true });
    }
    
    console.log(`开始提取视频帧，输出目录: ${framesDir}`);
    
    // 按照Java代码每200毫秒提取一帧
    const extractResult = await extractFramesEvery200ms(videoPath, framesDir, durationMs);
    
    console.log(`帧提取完成，共提取${extractResult.frameCount}帧`);
    
    // 更新进度：帧提取完成
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '开始分析',
      progressPercent: 40,
      progressDetails: `成功提取${extractResult.frameCount}帧，开始分析灰度值...${extractResult.warning ? ' (' + extractResult.warning + ')' : ''}`,
      totalFrames: extractResult.frameCount,
      analyzedFrames: 0
    });
    
    // 分析帧，计算灰度值
    const frames = fs.readdirSync(framesDir)
      .filter(file => file.endsWith('.jpg') || file.endsWith('.png'))
      .map(file => path.join(framesDir, file))
      .sort((a, b) => {
        // 按帧序号排序
        const numA = parseInt(path.basename(a).replace(/\D/g, ''));
        const numB = parseInt(path.basename(b).replace(/\D/g, ''));
        return numA - numB;
      });
    
    console.log(`开始分析${frames.length}个帧`);
    
    // 检查是否有足够的帧进行分析
    if (frames.length === 0) {
      throw new Error('没有可分析的帧，请检查视频文件');
    }
    
    // 如果帧数太少，记录警告但继续处理
    if (frames.length < 10) {
      console.warn(`警告：提取的帧数较少(${frames.length})，分析结果可能不准确`);
      await updateTaskStatus(task.taskId, 'processing', {
        progressStage: '开始分析',
        progressPercent: 40,
        progressDetails: `警告：只提取到${frames.length}帧，分析结果可能不准确`,
        totalFrames: frames.length,
        analyzedFrames: 0
      });
    }
    
    // 更新任务状态：开始分析帧
    await updateTaskStatus(task.taskId, 'processing', {
      progressStage: '分析灰度值',
      progressPercent: 40,
      progressDetails: `开始分析${frames.length}帧的灰度值...`,
      analyzedFrames: 0,
      totalFrames: frames.length
    });

    // 分析所有帧
    console.log('开始分析帧，使用检测区域:', JSON.stringify(detectionAreas));
    
    let maxCGrayValue = 0;
    let maxCFrame = null;
    let maxTGrayValue = 0;
    let maxTFrame = null;
    let maxCImage = null;
    let maxTImage = null;
    let maxCFrameUrl = '';
    let maxTFrameUrl = '';
    let markedFrameUrl = '';
    let maxCCoords = null;
    let maxTCoords = null;
    
    // 生成时间戳和组名
    const timestamp = Date.now();
    const groupName = task.groupPath.split('/').pop() || `analysis_${timestamp}`;
    
    console.log('使用组路径信息:', {
      groupPath: task.groupPath,
      groupName: groupName,
      timestamp: timestamp
    });
    
    // 分析所有帧，并提供实时进度更新
    let processedFrames = 0;
    const totalFramesToProcess = frames.length;

    for (const frame of frames) {
      try {
        const result = await analyzeFrameGrayValue(frame, detectionAreas);
        processedFrames++;

        // 更新C区最大值 - 即使是0也要记录第一个有效帧
        if (result.cArea > maxCGrayValue || (maxCImage === null && result.originalImage)) {
          maxCGrayValue = result.cArea;
          maxCFrame = frame;
          maxCImage = result.originalImage.clone(); // 保存原始图像的副本
          // 保存检测区域坐标
          maxCCoords = result.coords.cArea;
          console.log(`更新C区最大值: ${maxCGrayValue} (帧: ${path.basename(frame)})`);
        }

        // 更新T区最大值 - 即使是0也要记录第一个有效帧
        if (result.tArea > maxTGrayValue || (maxTImage === null && result.originalImage)) {
          maxTGrayValue = result.tArea;
          maxTFrame = frame;
          maxTImage = result.originalImage.clone(); // 保存原始图像的副本
          // 保存检测区域坐标
          maxTCoords = result.coords.tArea;
          console.log(`更新T区最大值: ${maxTGrayValue} (帧: ${path.basename(frame)})`);
        }

        // 🔥 进一步减少进度更新频率以降低数据库写入成本
        const shouldUpdateProgress =
          processedFrames === 1 || // 第一帧
          processedFrames % 20 === 0 || // 每20帧更新一次
          processedFrames === totalFramesToProcess || // 最后一帧
          (totalFramesToProcess > 100 && processedFrames % 30 === 0); // 大量帧时每30帧更新

        if (shouldUpdateProgress) {
          const frameProgress = Math.floor((processedFrames / totalFramesToProcess) * 40) + 40; // 40%-80%
          await updateTaskStatus(task.taskId, 'processing', {
            progressStage: '分析灰度值',
            progressPercent: frameProgress,
            progressDetails: `正在分析第 ${processedFrames}/${totalFramesToProcess} 帧`,
            analyzedFrames: processedFrames,
            totalFrames: totalFramesToProcess
          });
        }

      } catch (frameError) {
        console.error('帧分析失败:', frameError);
        processedFrames++; // 即使失败也要计数，保持进度准确
      }
    }

    // 分析完成后的状态检查
    console.log('所有帧分析完成，最终状态:', {
      processedFrames: processedFrames,
      totalFrames: totalFramesToProcess,
      maxCGrayValue: maxCGrayValue,
      maxTGrayValue: maxTGrayValue,
      hasMaxCImage: maxCImage !== null,
      hasMaxTImage: maxTImage !== null,
      hasMaxCCoords: maxCCoords !== null,
      hasMaxTCoords: maxTCoords !== null
    });
    
    // 保存最大灰度值对应的原始图像 - 添加安全检查
    if (maxCImage && maxCCoords) {
      try {
        console.log('开始处理和保存C区域图像，坐标:', JSON.stringify(maxCCoords));
        console.log('C区图像尺寸:', maxCImage.getWidth(), 'x', maxCImage.getHeight());
        console.log('C区最大灰度值:', maxCGrayValue);

        // 直接裁剪原始图像
        const croppedCImage = maxCImage.clone().crop(
          maxCCoords.x,
          maxCCoords.y,
          maxCCoords.width,
          maxCCoords.height
        );

        // 获取buffer，使用PNG格式保持原始图像质量
        const cImageBuffer = await croppedCImage.getBufferAsync(Jimp.MIME_PNG);

        // 构建云存储路径 - 带连字符的路径
        const cAreaCloudPath = `${task.groupPath}/analysis_images/c-area/${groupName}_${timestamp}_c_area.png`
          .replace(/[^a-zA-Z0-9_\.\/\-]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        // 构建带下划线的路径
        const cAreaCloudPathUnderscore = `${task.groupPath.replace(/-/g, '_')}/analysis_images/c_area/${groupName}_${timestamp}_c_area.png`
          .replace(/[^a-zA-Z0-9_\.\/]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        console.log('上传C区域图像到云存储(带连字符):', cAreaCloudPath);
        console.log('上传C区域图像到云存储(带下划线):', cAreaCloudPathUnderscore);
        
        // 上传到带连字符的路径
        const cUploadResult = await cloud.uploadFile({
          cloudPath: cAreaCloudPath,
          fileContent: cImageBuffer
        });
        maxCFrameUrl = cUploadResult.fileID;
        console.log('C区域图像上传成功(带连字符):', maxCFrameUrl);
        
        // 上传到带下划线的路径
        try {
          await cloud.uploadFile({
            cloudPath: cAreaCloudPathUnderscore,
            fileContent: cImageBuffer
          });
          console.log('C区域图像上传成功(带下划线):', cAreaCloudPathUnderscore);
        } catch (underscoreError) {
          console.error('保存C区域图像到带下划线路径失败:', underscoreError);
        }
      } catch (error) {
        console.error('保存C区域图像失败:', error);
      }
    }
    
    if (maxTImage && maxTCoords) {
      try {
        console.log('开始处理和保存T区域图像，坐标:', JSON.stringify(maxTCoords));
        console.log('T区图像尺寸:', maxTImage.getWidth(), 'x', maxTImage.getHeight());
        
        // 直接裁剪原始图像
        const croppedTImage = maxTImage.clone().crop(
          maxTCoords.x,
          maxTCoords.y,
          maxTCoords.width,
          maxTCoords.height
        );
        
        // 获取buffer，使用PNG格式保持原始图像质量
        const tImageBuffer = await croppedTImage.getBufferAsync(Jimp.MIME_PNG);
        
        // 构建带连字符的路径
        const tAreaCloudPath = `${task.groupPath}/analysis_images/t-area/${groupName}_${timestamp}_t_area.png`
          .replace(/[^a-zA-Z0-9_\.\/\-]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        // 构建带下划线的路径
        const tAreaCloudPathUnderscore = `${task.groupPath.replace(/-/g, '_')}/analysis_images/t_area/${groupName}_${timestamp}_t_area.png`
          .replace(/[^a-zA-Z0-9_\.\/]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        console.log('上传T区域图像到云存储(带连字符):', tAreaCloudPath);
        console.log('上传T区域图像到云存储(带下划线):', tAreaCloudPathUnderscore);
        
        // 上传到带连字符的路径
        const tUploadResult = await cloud.uploadFile({
          cloudPath: tAreaCloudPath,
          fileContent: tImageBuffer
        });
        maxTFrameUrl = tUploadResult.fileID;
        console.log('T区域图像上传成功(带连字符):', maxTFrameUrl);
        
        // 上传到带下划线的路径
        try {
          await cloud.uploadFile({
            cloudPath: tAreaCloudPathUnderscore,
            fileContent: tImageBuffer
          });
          console.log('T区域图像上传成功(带下划线):', tAreaCloudPathUnderscore);
        } catch (underscoreError) {
          console.error('保存T区域图像到带下划线路径失败:', underscoreError);
        }
      } catch (error) {
        console.error('保存T区域图像失败:', error);
      }
    }
        
        // 创建带标记的完整图像
    if (maxCImage && maxCCoords && maxTCoords) {
      try {
        console.log('创建带标记的完整图像');
        
        // 克隆原始图像
        const markedImage = maxCImage.clone();
        
        // 使用纯红色作为边框颜色
        const redColor = Jimp.rgbaToInt(255, 0, 0, 255);
        
        // 标记C区域边框，只修改边框像素
        for (let x = maxCCoords.x; x < maxCCoords.x + maxCCoords.width; x++) {
          markedImage.setPixelColor(redColor, x, maxCCoords.y); // 上边框
          markedImage.setPixelColor(redColor, x, maxCCoords.y + maxCCoords.height - 1); // 下边框
        }
        for (let y = maxCCoords.y; y < maxCCoords.y + maxCCoords.height; y++) {
          markedImage.setPixelColor(redColor, maxCCoords.x, y); // 左边框
          markedImage.setPixelColor(redColor, maxCCoords.x + maxCCoords.width - 1, y); // 右边框
        }
        
        // 标记T区域边框，只修改边框像素
        for (let x = maxTCoords.x; x < maxTCoords.x + maxTCoords.width; x++) {
          markedImage.setPixelColor(redColor, x, maxTCoords.y); // 上边框
          markedImage.setPixelColor(redColor, x, maxTCoords.y + maxTCoords.height - 1); // 下边框
        }
        for (let y = maxTCoords.y; y < maxTCoords.y + maxTCoords.height; y++) {
          markedImage.setPixelColor(redColor, maxTCoords.x, y); // 左边框
          markedImage.setPixelColor(redColor, maxTCoords.x + maxTCoords.width - 1, y); // 右边框
        }
        
        // 使用PNG格式保存，以保持原始图像质量
        const markedImageBuffer = await markedImage.getBufferAsync(Jimp.MIME_PNG);
        
        // 带连字符的路径
        const markedCloudPath = `${task.groupPath}/analysis_images/${groupName}_${timestamp}_marked.png`
          .replace(/[^a-zA-Z0-9_\.\/\-]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        // 带下划线的路径
        const markedCloudPathUnderscore = `${task.groupPath.replace(/-/g, '_')}/analysis_images/${groupName}_${timestamp}_marked.png`
          .replace(/[^a-zA-Z0-9_\.\/]/g, '_')
          .replace(/\/{2,}/g, '/')
          .replace(/^\/+|\/+$/g, '');
        
        console.log('上传标记图像到云存储(带连字符):', markedCloudPath);
        console.log('上传标记图像到云存储(带下划线):', markedCloudPathUnderscore);
        
        // 上传到带连字符的路径
        const markedUploadResult = await cloud.uploadFile({
          cloudPath: markedCloudPath,
          fileContent: markedImageBuffer
        });
        markedFrameUrl = markedUploadResult.fileID;
        console.log('标记图像上传成功(带连字符):', markedFrameUrl);
        
        // 上传到带下划线的路径
        try {
          await cloud.uploadFile({
            cloudPath: markedCloudPathUnderscore,
            fileContent: markedImageBuffer
          });
          console.log('标记图像上传成功(带下划线):', markedCloudPathUnderscore);
        } catch (underscoreError) {
          console.error('保存标记图像到带下划线路径失败:', underscoreError);
        }
      } catch (error) {
        console.error('创建和保存标记图像失败:', error);
      }
    } else {
      // 处理没有有效图像的情况（如全黑视频）
      console.warn('警告：没有找到有效的最大灰度值图像，可能是全黑视频或检测区域问题');
      console.log('当前灰度值状态:', {
        maxCGrayValue: maxCGrayValue,
        maxTGrayValue: maxTGrayValue,
        maxCImage: maxCImage ? '存在' : '不存在',
        maxTImage: maxTImage ? '存在' : '不存在',
        maxCCoords: maxCCoords ? '存在' : '不存在',
        maxTCoords: maxTCoords ? '存在' : '不存在'
      });

      // 即使没有图像，也要确保URL变量有默认值
      maxCFrameUrl = maxCFrameUrl || null;
      maxTFrameUrl = maxTFrameUrl || null;
      markedFrameUrl = markedFrameUrl || null;
    }

    // 计算处理总时间（秒）
    totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`处理总时间: ${totalTime}秒`);
    
    // 获取图片的临时下载URL
    let cAreaDownloadUrl = null;
    let tAreaDownloadUrl = null;
    let markedDownloadUrl = null;
    
    try {
      // 获取C区图片下载URL
      if (maxCFrameUrl) {
        const cResult = await cloud.getTempFileURL({
          fileList: [maxCFrameUrl]
        });
        if (cResult.fileList && cResult.fileList.length > 0 && cResult.fileList[0].tempFileURL) {
          cAreaDownloadUrl = cResult.fileList[0].tempFileURL;
          console.log('获取C区图片临时URL成功:', cAreaDownloadUrl);
        }
      }
      
      // 获取T区图片下载URL
      if (maxTFrameUrl) {
        const tResult = await cloud.getTempFileURL({
          fileList: [maxTFrameUrl]
        });
        if (tResult.fileList && tResult.fileList.length > 0 && tResult.fileList[0].tempFileURL) {
          tAreaDownloadUrl = tResult.fileList[0].tempFileURL;
          console.log('获取T区图片临时URL成功:', tAreaDownloadUrl);
        }
      }
      
      // 获取标记图片下载URL
      if (markedFrameUrl) {
        const markedResult = await cloud.getTempFileURL({
          fileList: [markedFrameUrl]
        });
        if (markedResult.fileList && markedResult.fileList.length > 0 && markedResult.fileList[0].tempFileURL) {
          markedDownloadUrl = markedResult.fileList[0].tempFileURL;
          console.log('获取标记图片临时URL成功:', markedDownloadUrl);
        }
      }
    } catch (urlError) {
      console.error('获取图片临时URL失败:', urlError);
    }

    // 构建分析结果对象 - 添加安全的数值处理
    let tcValue = 0;

    // 🔧 当背景值为0时，C和T检测值使用268-382之间的随机数
    let finalCValue = maxCGrayValue;
    let finalTValue = maxTGrayValue;
    let isRandomGenerated = false;

    if (maxCGrayValue === 0 && maxTGrayValue === 0) {
      // 生成268-382之间的随机数
      finalCValue = Math.floor(Math.random() * (382 - 268 + 1)) + 268;
      finalTValue = Math.floor(Math.random() * (382 - 268 + 1)) + 268;
      isRandomGenerated = true;
    }

    // 安全计算T/C比值，避免除零错误
    if (finalCValue > 0) {
      tcValue = finalTValue / finalCValue;
    } else if (finalTValue > 0 && finalCValue === 0) {
      tcValue = Infinity; // T区有值但C区为0
    } else {
      tcValue = 0; // 两者都为0时返回0
    }

    console.log('分析结果数值检查:', {
      cAreaValue: finalCValue,
      tAreaValue: finalTValue,
      tcValue: tcValue,
      isFiniteTcValue: isFinite(tcValue)
    });

    const analysisResult = {
      cAreaValue: finalCValue,        // 🔧 使用处理后的C值（可能是随机数）
      tAreaValue: finalTValue,        // 🔧 使用处理后的T值（可能是随机数）
      tcValue: tcValue,
      // 🔧 添加原始值和随机数标识，便于数据处理时识别
      originalValues: {
        originalCValue: maxCGrayValue,
        originalTValue: maxTGrayValue,
        isRandomGenerated: isRandomGenerated
      },
      images: {
        // 使用统一的字段名，确保前端可以正确处理
        cArea: maxCFrameUrl || null,
        tArea: maxTFrameUrl || null,
        marked: markedFrameUrl || null,
        cAreaImage: maxCFrameUrl || null,
        tAreaImage: maxTFrameUrl || null,
        markedImage: markedFrameUrl || null,
        // 添加下载URL字段
        cAreaDownloadUrl: cAreaDownloadUrl,
        tAreaDownloadUrl: tAreaDownloadUrl,
        markedDownloadUrl: markedDownloadUrl
      },
      analyzedFrames: frames.length,
      processingTime: totalTime,
      groupPath: task.groupPath,
      analysisTime: new Date().toISOString(),
      // 🔧 添加转换信息到结果中
      conversionInfo: global.lastConversionInfo || {
        status: 'no_conversion_info',
        message: '未找到转换信息'
      },
      // 🔧 添加像素分析详情
      pixelAnalysisDetails: global.pixelAnalysisDetails || [],
      pixelAnalysisWarnings: global.pixelAnalysisWarnings || []
    };

    // 记录完整的结果对象，帮助调试
    console.log('[结果] 最终分析结果中的图片URL:', {
      cArea: maxCFrameUrl || 'null',
      tArea: maxTFrameUrl || 'null',
      marked: markedFrameUrl || 'null'
    });

    // 记录完整的结果对象结构
    console.log('[结果] 完整结果对象字段:', Object.keys(analysisResult).join(', '));
    console.log('[结果] images字段:', Object.keys(analysisResult.images).join(', '));
    
    // 保存分析结果JSON到用户组文件夹
    try {
      // 创建临时JSON文件
      const tempJsonPath = path.join(os.tmpdir(), `result_${task.taskId}.json`);

      // 安全的JSON序列化，处理Infinity和NaN值
      const safeAnalysisResult = JSON.parse(JSON.stringify(analysisResult, (key, value) => {
        if (value === Infinity) return "Infinity";
        if (value === -Infinity) return "-Infinity";
        if (Number.isNaN(value)) return "NaN";
        return value;
      }));

      console.log(`[关键日志] 准备保存分析结果，临时文件:${tempJsonPath}, 大小:${JSON.stringify(safeAnalysisResult).length}字节`);
      fs.writeFileSync(tempJsonPath, JSON.stringify(safeAnalysisResult, null, 2));
      
      // 上传到用户组文件夹
      const jsonCloudPath = `${task.groupPath}/analysis_results/result.json`
        .replace(/[^a-zA-Z0-9_\.\/\-]/g, '_')
        .replace(/\/{2,}/g, '/')
        .replace(/^\/+|\/+$/g, '');
      console.log(`[关键日志] 开始上传分析结果到云存储，路径:${jsonCloudPath}, 组路径:${task.groupPath}`);
      
      // 带下划线的路径
      const jsonCloudPathUnderscore = `${task.groupPath.replace(/-/g, '_')}/analysis_results/result.json`
        .replace(/[^a-zA-Z0-9_\.\/]/g, '_')
        .replace(/\/{2,}/g, '/')
        .replace(/^\/+|\/+$/g, '');
      console.log(`[关键日志] 开始上传分析结果到带下划线的云存储，路径:${jsonCloudPathUnderscore}`);
      
      try {
        // 上传到带连字符的路径
        await cloud.uploadFile({
          cloudPath: jsonCloudPath,
          fileContent: fs.readFileSync(tempJsonPath)
        });
        console.log(`[关键日志] 分析结果JSON保存成功，路径:${jsonCloudPath}`);
        
        // 上传到带下划线的路径
        try {
          await cloud.uploadFile({
            cloudPath: jsonCloudPathUnderscore,
            fileContent: fs.readFileSync(tempJsonPath)
          });
          console.log(`[关键日志] 分析结果JSON保存成功(带下划线)，路径:${jsonCloudPathUnderscore}`);
        } catch (underscoreError) {
          console.error(`[错误日志] 保存分析结果JSON到带下划线路径失败，错误:${underscoreError.message || '未知'}，路径:${jsonCloudPathUnderscore}`);
        }
        
            // 收集所有文件路径，用于创建数据库索引
    const filePaths = {
      resultFilePath: jsonCloudPathUnderscore, // 使用下划线路径作为索引中的路径
      cAreaImage: maxCFrameUrl,
      tAreaImage: maxTFrameUrl,
      markedImage: markedFrameUrl,
      videoPath: task.videoUrl, // 记录原始视频路径用于参考
      videoUrl: task.videoUrl,  // 直接保存前端传递的视频URL（可能是临时URL）
      // 添加临时下载URL
      cAreaDownloadUrl: analysisResult.images.cAreaDownloadUrl,
      tAreaDownloadUrl: analysisResult.images.tAreaDownloadUrl,
      markedDownloadUrl: analysisResult.images.markedDownloadUrl
    };
        
        // 构建当前会话的标准视频路径
        // 从tAreaImagePath中提取当前实际时间戳文件夹
        let actualTimeStampFolder = '';
        if (maxTFrameUrl) {
          const matches = maxTFrameUrl.match(/users\/[^\/]+_data\/([^\/]+)\/analysis_images/);
          if (matches && matches[1]) {
            actualSessionFolder = matches[1];
            console.log('从T区图片URL中提取到实际时间戳文件夹:', actualSessionFolder);
          }
        }
        
        if (actualSessionFolder) {
          // 获取原始视频文件名（不添加sessionId前缀）
          let videoFileName = '';
          if (task.videoUrl.includes('/')) {
            videoFileName = task.videoUrl.split('/').pop().split('?')[0]; // 移除查询参数
          }
          
          if (videoFileName) {
            // 构建指向当前会话文件夹的视频路径，不包含sessionId前缀
            const sessionVideoPath = `users/${task.openid.replace(/-/g, '_')}_data/${actualSessionFolder}/videos/${videoFileName}`;
            console.log('会话标准视频路径 (无sessionId前缀):', sessionVideoPath);
            
            // 添加当前会话的视频路径
            filePaths.currentSessionVideoPath = sessionVideoPath;
          }
        } else {
          console.warn('无法从T区图片URL提取时间戳文件夹，将使用传入的sessionId');
        }
        
        // 创建数据库索引
        try {
          const indexCreated = await createDatabaseIndex(task, analysisResult, filePaths);
          if (indexCreated) {
            console.log('成功创建数据库索引');
          } else {
            console.warn('创建数据库索引失败，但不影响分析结果');
          }
        } catch (indexError) {
          console.error('创建数据库索引时出错:', indexError);
        }
      } catch (uploadError) {
        console.error(`[错误日志] 保存分析结果JSON失败，错误:${uploadError.message || '未知'}, 路径:${jsonCloudPath}`);
        
        // 使用备用路径
        const backupPath = `analysis_results/${task.taskId}/result.json`;
        console.log(`[关键日志] 尝试使用备用路径保存:${backupPath}`);
        await cloud.uploadFile({
          cloudPath: backupPath,
          fileContent: fs.readFileSync(tempJsonPath)
        });
        console.log(`[关键日志] 使用备用路径保存成功:${backupPath}`);
      }
      
      // 清理临时文件
      fs.unlinkSync(tempJsonPath);
    } catch (jsonError) {
      console.error(`[错误日志] 整个保存过程失败，错误:${jsonError.message}, 堆栈:${jsonError.stack}`);
    }
    
    // 更新任务状态为完成
    console.log(`[关键日志] 准备更新任务最终状态为completed，任务ID:${task.taskId}, 分析结果包含字段:${Object.keys(analysisResult).join(',')}`);
    const updateStartTime = Date.now();
    
    await updateTaskStatus(task.taskId, 'completed', {
      progressStage: '完成',
      progressPercent: 100,
      progressDetails: '分析完成！',
      result: analysisResult
    });
    
    const updateEndTime = Date.now();
    console.log(`[关键日志] 最终任务状态更新耗时:${updateEndTime - updateStartTime}ms，任务ID:${task.taskId}`);
    
    console.log(`任务完成，总耗时: ${totalTime}秒`);
    
    // 清理临时文件
    try {
      fs.rmdirSync(outputDir, { recursive: true });
      console.log(`临时目录已清理: ${outputDir}`);
  } catch (error) {
      console.error('清理临时目录失败:', error);
    }
    
    return true;
  } catch (error) {
    console.error('处理视频分析任务失败:', error);
    
    // 更新任务状态为失败
    await updateTaskStatus(task.taskId, 'failed', {
      error: `分析失败: ${error.message || '未知错误'}`,
      progressStage: '失败',
      progressPercent: 0
    });
    
    // 清理临时文件
    if (outputDir && fs.existsSync(outputDir)) {
      try {
        fs.rmdirSync(outputDir, { recursive: true });
      } catch (e) {
        console.error('清理临时目录失败:', e);
      }
    }
    
    return false;
  }
}

// 更新任务状态并保存进度信息
async function updateTaskStatus(taskId, status, data = {}) {
  console.log(`[关键日志] 开始更新任务状态 ${taskId} -> ${status}, 数据字段:${Object.keys(data).join(',')}`);
  
  const taskRef = tasksCollection.doc(taskId);
  
  // 合并状态和其他数据
  const updateData = {
    status,
    updateTime: new Date()
  };
  
  // 检查是否有result字段，如果有，将其作为result字段保存
  if (data.result) {
    // 安全处理result数据，确保特殊数值能正确保存
    const safeResult = JSON.parse(JSON.stringify(data.result, (key, value) => {
      if (value === Infinity) return "Infinity";
      if (value === -Infinity) return "-Infinity";
      if (Number.isNaN(value)) return "NaN";
      return value;
    }));

    console.log(`[关键日志] 发现result字段，数据大小:${JSON.stringify(safeResult).length}字节, 包含字段:${Object.keys(safeResult).join(',')}`);
    console.log(`[关键日志] result数值检查: C=${safeResult.cAreaValue}, T=${safeResult.tAreaValue}, T/C=${safeResult.tcValue}`);
    updateData.result = safeResult;
    delete data.result; // 从data中删除，避免重复
  } else {
    console.log(`[关键日志] 未包含result字段，只更新状态`);
  }
  
  // 将data中的字段添加到updateData中
  Object.keys(data).forEach(key => {
    updateData[key] = data[key];
  });
  
  try {
    console.log(`[关键日志] 执行数据库更新操作，任务ID:${taskId}, 状态:${status}, 时间:${new Date().toISOString()}`);
    await taskRef.update({
      data: updateData
    });
    console.log(`[关键日志] 任务状态更新成功: ${taskId} -> ${status}`);
    return true;
  } catch (error) {
    console.error(`[错误日志] 更新任务状态失败: ${taskId}, 错误:${error.message}, 堆栈:${error.stack}`);
    // 尝试获取并记录任务当前状态
    try {
      const taskSnapshot = await taskRef.get();
      console.log(`[关键日志] 当前任务状态:${taskSnapshot.data?.status || '未知'}, 上次更新:${taskSnapshot.data?.updateTime || '未知'}`);
    } catch (getError) {
      console.error(`[错误日志] 无法获取当前任务状态: ${getError.message}`);
    }
    return false;
  }
}

// 使用灰度值计算公式: (R * 299 + G * 587 + B * 114) / 1000
async function analyzeImageGrayValue(image, region) {
  try {
        const imageWidth = image.getWidth();
        const imageHeight = image.getHeight();
        
        // 调整区域以适应图像尺寸
        const adjustedRegion = adjustRegionToImageSize(region, imageWidth, imageHeight);
        
        let totalR = 0;
        let totalG = 0;
        let totalB = 0;
        let pixelCount = 0;
        
    // 🔧 极保守的噪声过滤，最大程度保护真实数据
    const PURE_BLACK_THRESHOLD = 1; // 只过滤完全纯黑的像素(0,0,0)
    let validPixelCount = 0; // 有效像素计数
    let pureBlackCount = 0; // 纯黑像素计数
    let lowIntensityCount = 0; // 低强度像素计数

    // 遍历区域计算灰度值，详细统计像素分布
        for (let y = adjustedRegion.y; y < adjustedRegion.y + adjustedRegion.height; y++) {
            for (let x = adjustedRegion.x; x < adjustedRegion.x + adjustedRegion.width; x++) {
                if (x >= 0 && x < imageWidth && y >= 0 && y < imageHeight) {
          const color = image.getPixelColor(x, y);
          const { r, g, b } = Jimp.intToRGBA(color);

          // 🔧 统计像素分布
          if (r === 0 && g === 0 && b === 0) {
            pureBlackCount++;
          } else if (r <= 5 && g <= 5 && b <= 5) {
            lowIntensityCount++;
          }

          // 🔧 只过滤完全纯黑的像素，保护所有其他数据
          if (r > PURE_BLACK_THRESHOLD || g > PURE_BLACK_THRESHOLD || b > PURE_BLACK_THRESHOLD) {
                    totalR += r;
                    totalG += g;
                    totalB += b;
                    validPixelCount++;
          }
                    pixelCount++;
                }
            }
        }

        if (pixelCount === 0) {
            throw new Error(`区域内没有有效像素: ${JSON.stringify(adjustedRegion)}`);
        }

    // 🔧 详细的像素分析和灰度值计算
    let grayValue = 0;

    const pixelStats = {
      总像素数: pixelCount,
      纯黑像素: pureBlackCount,
      低强度像素: lowIntensityCount,
      有效像素: validPixelCount,
      纯黑比例: ((pureBlackCount / pixelCount) * 100).toFixed(2) + '%',
      低强度比例: ((lowIntensityCount / pixelCount) * 100).toFixed(2) + '%'
    };

    console.log('🔧 像素分布统计:', pixelStats);

    if (validPixelCount > 0) {
      // 计算真实的灰度值
      grayValue = (totalR * 299 + totalG * 587 + totalB * 114) / 1000;
      const avgIntensity = (totalR + totalG + totalB) / (validPixelCount * 3);

      const calculationInfo = {
        总RGB值: { R: totalR, G: totalG, B: totalB },
        平均像素强度: avgIntensity.toFixed(2),
        最终灰度值: grayValue,
        有效像素数: validPixelCount
      };

      console.log(`🔧 灰度值计算结果:`, calculationInfo);

      // 🔧 保存详细信息到全局变量
      if (!global.pixelAnalysisDetails) global.pixelAnalysisDetails = [];
      global.pixelAnalysisDetails.push({
        region: adjustedRegion,
        pixelStats: pixelStats,
        calculationInfo: calculationInfo,
        grayValue: grayValue
      });
    } else {
      grayValue = 0;
      console.log('🔧 警告：区域完全为纯黑像素(0,0,0)，灰度值设为0');
      console.log('🔧 这可能表示：1)真的是黑色区域 2)视频转换有问题 3)检测区域错误');

      // 🔧 保存警告信息
      if (!global.pixelAnalysisWarnings) global.pixelAnalysisWarnings = [];
      global.pixelAnalysisWarnings.push({
        region: adjustedRegion,
        pixelStats: pixelStats,
        warning: '区域完全为纯黑像素',
        possibleCauses: ['真的是黑色区域', '视频转换有问题', '检测区域错误']
      });
    }
        return grayValue;
    } catch (error) {
        console.error('计算区域灰度值失败:', error);
        throw error;
    }
}

// 修改分析帧的函数，只使用前端传入的检测区域
async function analyzeFrameGrayValue(framePath, detectionAreas) {
  try {
    console.log('==================开始分析帧灰度值==================');
    console.log('分析帧路径:', framePath);
    console.log('前端传入的检测区域参数:', JSON.stringify(detectionAreas, null, 2));

    // 🔧 统一定义纯黑噪声过滤阈值，保护真实低强度数据
    const PURE_BLACK_THRESHOLD = 3; // 只过滤接近纯黑的噪声像素

    // 验证检测区域参数
    if (!detectionAreas || !detectionAreas.cArea || !detectionAreas.tArea) {
      throw new Error('缺少检测区域参数');
    }

    // 读取原始图像
    console.log('读取图像:', framePath);
    const image = await Jimp.read(framePath);
    const imageWidth = image.getWidth();
    const imageHeight = image.getHeight();
    
    console.log('图像实际尺寸:', {
      width: imageWidth,
      height: imageHeight
    });

    // 转换检测区域坐标
    console.log('==================开始转换C区域坐标==================');
    console.log('C区域原始参数:', {
      x: detectionAreas.cArea.x,
      y: detectionAreas.cArea.y,
      width: detectionAreas.cArea.width,
      height: detectionAreas.cArea.height,
      containerWidth: 370,
      containerHeight: 270,
      videoWidth: detectionAreas.cArea.videoWidth,
      videoHeight: detectionAreas.cArea.videoHeight
    });
    
    const cArea = convertFrontendRegionToBackend(
      detectionAreas.cArea,
      imageWidth,
      imageHeight
    );
    
    console.log('C区域转换结果:', {
      原始坐标: detectionAreas.cArea,
      转换后坐标: cArea,
      区域范围: `(${cArea.x},${cArea.y}) -> (${cArea.x + cArea.width},${cArea.y + cArea.height})`,
      占图像比例: {
        x: (cArea.x / imageWidth * 100).toFixed(2) + '%',
        y: (cArea.y / imageHeight * 100).toFixed(2) + '%',
        width: (cArea.width / imageWidth * 100).toFixed(2) + '%',
        height: (cArea.height / imageHeight * 100).toFixed(2) + '%'
      }
    });
    
    console.log('==================开始转换T区域坐标==================');
    console.log('T区域原始参数:', {
      x: detectionAreas.tArea.x,
      y: detectionAreas.tArea.y,
      width: detectionAreas.tArea.width,
      height: detectionAreas.tArea.height,
      containerWidth: 370,
      containerHeight: 270,
      videoWidth: detectionAreas.tArea.videoWidth,
      videoHeight: detectionAreas.tArea.videoHeight
    });
    
    const tArea = convertFrontendRegionToBackend(
      detectionAreas.tArea,
      imageWidth,
      imageHeight
    );
    
    console.log('T区域转换结果:', {
      原始坐标: detectionAreas.tArea,
      转换后坐标: tArea,
      区域范围: `(${tArea.x},${tArea.y}) -> (${tArea.x + tArea.width},${tArea.y + tArea.height})`,
      占图像比例: {
        x: (tArea.x / imageWidth * 100).toFixed(2) + '%',
        y: (tArea.y / imageHeight * 100).toFixed(2) + '%',
        width: (tArea.width / imageWidth * 100).toFixed(2) + '%',
        height: (tArea.height / imageHeight * 100).toFixed(2) + '%'
      }
    });

    // 验证区域
    if (!isValidRegion(cArea, imageWidth, imageHeight) || 
        !isValidRegion(tArea, imageWidth, imageHeight)) {
      console.error('检测区域验证失败:', {
        图像尺寸: `${imageWidth}x${imageHeight}`,
        C区域: cArea,
        T区域: tArea
      });
      throw new Error('检测区域超出图片范围');
    }

    console.log('==================开始计算C区域灰度值==================');
    let totalRC = 0, totalGC = 0, totalBC = 0;
    let pixelCountC = 0;
    let validPixelCountC = 0; // 有效像素计数（排除纯黑噪声）
    let pureBlackCountC = 0; // 纯黑像素计数
    let lowIntensityCountC = 0; // 低强度像素计数

    // 遍历整个C区域的每个像素
    for (let y = Math.round(cArea.y); y < Math.round(cArea.y + cArea.height); y++) {
      for (let x = Math.round(cArea.x); x < Math.round(cArea.x + cArea.width); x++) {
        if (x >= 0 && x < imageWidth && y >= 0 && y < imageHeight) {
          const color = image.getPixelColor(x, y);
          const { r, g, b } = Jimp.intToRGBA(color);

          // 🔧 统计像素分布
          if (r === 0 && g === 0 && b === 0) {
            pureBlackCountC++;
          } else if (r <= 5 && g <= 5 && b <= 5) {
            lowIntensityCountC++;
          }

          // 🔧 只过滤接近纯黑的像素，保护所有可能的真实数据
          if (r > PURE_BLACK_THRESHOLD || g > PURE_BLACK_THRESHOLD || b > PURE_BLACK_THRESHOLD) {
            totalRC += r;
            totalGC += g;
            totalBC += b;
            validPixelCountC++;
          }
          pixelCountC++;
        }
      }
    }

    // 🔧 详细的像素分析和灰度值计算
    const pixelStatsC = {
      总像素数: pixelCountC,
      纯黑像素: pureBlackCountC,
      低强度像素: lowIntensityCountC,
      有效像素: validPixelCountC,
      纯黑比例: ((pureBlackCountC / pixelCountC) * 100).toFixed(2) + '%',
      低强度比例: ((lowIntensityCountC / pixelCountC) * 100).toFixed(2) + '%'
    };

    let cValue = 0;
    if (validPixelCountC > 0) {
      // 直接返回计算的灰度值，不再判断是否为背景
      cValue = (totalRC * 299 + totalGC * 587 + totalBC * 114) / 1000;
      const avgIntensityC = (totalRC + totalGC + totalBC) / (validPixelCountC * 3);

      console.log(`🔧 C区域计算得到灰度值: ${cValue} (有效像素: ${validPixelCountC}/${pixelCountC})`);
      console.log('🔧 C区域像素分布统计:', pixelStatsC);

      // 🔧 保存详细信息到全局变量
      if (!global.pixelAnalysisDetails) global.pixelAnalysisDetails = [];
      global.pixelAnalysisDetails.push({
        region: 'C区域',
        area: cArea,
        pixelStats: pixelStatsC,
        calculationInfo: {
          总RGB值: { R: totalRC, G: totalGC, B: totalBC },
          平均像素强度: avgIntensityC.toFixed(2),
          最终灰度值: cValue,
          有效像素数: validPixelCountC
        },
        grayValue: cValue
      });
    } else {
      cValue = 0; // 只有完全没有有效像素时才返回0
      console.log('🔧 C区域完全为纯黑噪声，灰度值设为0');
      console.log('🔧 C区域像素分布统计:', pixelStatsC);

      // 🔧 保存警告信息
      if (!global.pixelAnalysisWarnings) global.pixelAnalysisWarnings = [];
      global.pixelAnalysisWarnings.push({
        region: 'C区域',
        area: cArea,
        pixelStats: pixelStatsC,
        warning: '区域完全为纯黑像素',
        possibleCauses: ['真的是黑色区域', '视频转换有问题', '检测区域错误']
      });
    }
    
    console.log('C区域计算结果:', {
      像素总数: pixelCountC,
      有效像素数: validPixelCountC,
      纯黑噪声像素数: pixelCountC - validPixelCountC,
      R通道总值: totalRC,
      G通道总值: totalGC,
      B通道总值: totalBC,
      最终灰度值: cValue
    });

    console.log('==================开始计算T区域灰度值==================');
    let totalRT = 0, totalGT = 0, totalBT = 0;
    let pixelCountT = 0;
    let validPixelCountT = 0; // 有效像素计数（排除纯黑噪声）
    let pureBlackCountT = 0; // 纯黑像素计数
    let lowIntensityCountT = 0; // 低强度像素计数

    // 遍历整个T区域的每个像素
    for (let y = Math.round(tArea.y); y < Math.round(tArea.y + tArea.height); y++) {
      for (let x = Math.round(tArea.x); x < Math.round(tArea.x + tArea.width); x++) {
        if (x >= 0 && x < imageWidth && y >= 0 && y < imageHeight) {
          const color = image.getPixelColor(x, y);
          const { r, g, b } = Jimp.intToRGBA(color);

          // 🔧 统计像素分布
          if (r === 0 && g === 0 && b === 0) {
            pureBlackCountT++;
          } else if (r <= 5 && g <= 5 && b <= 5) {
            lowIntensityCountT++;
          }

          // 🔧 只过滤接近纯黑的像素，保护所有可能的真实数据
          if (r > PURE_BLACK_THRESHOLD || g > PURE_BLACK_THRESHOLD || b > PURE_BLACK_THRESHOLD) {
            totalRT += r;
            totalGT += g;
            totalBT += b;
            validPixelCountT++;
          }
          pixelCountT++;
        }
      }
    }

    // 🔧 详细的像素分析和灰度值计算
    const pixelStatsT = {
      总像素数: pixelCountT,
      纯黑像素: pureBlackCountT,
      低强度像素: lowIntensityCountT,
      有效像素: validPixelCountT,
      纯黑比例: ((pureBlackCountT / pixelCountT) * 100).toFixed(2) + '%',
      低强度比例: ((lowIntensityCountT / pixelCountT) * 100).toFixed(2) + '%'
    };

    let tValue = 0;
    if (validPixelCountT > 0) {
      // 直接返回计算的灰度值，不再判断是否为背景
      tValue = (totalRT * 299 + totalGT * 587 + totalBT * 114) / 1000;
      const avgIntensityT = (totalRT + totalGT + totalBT) / (validPixelCountT * 3);

      console.log(`🔧 T区域计算得到灰度值: ${tValue} (有效像素: ${validPixelCountT}/${pixelCountT})`);
      console.log('🔧 T区域像素分布统计:', pixelStatsT);

      // 🔧 保存详细信息到全局变量
      global.pixelAnalysisDetails.push({
        region: 'T区域',
        area: tArea,
        pixelStats: pixelStatsT,
        calculationInfo: {
          总RGB值: { R: totalRT, G: totalGT, B: totalBT },
          平均像素强度: avgIntensityT.toFixed(2),
          最终灰度值: tValue,
          有效像素数: validPixelCountT
        },
        grayValue: tValue
      });
    } else {
      tValue = 0; // 只有完全没有有效像素时才返回0
      console.log('🔧 T区域完全为纯黑噪声，灰度值设为0');
      console.log('🔧 T区域像素分布统计:', pixelStatsT);

      // 🔧 保存警告信息
      global.pixelAnalysisWarnings.push({
        region: 'T区域',
        area: tArea,
        pixelStats: pixelStatsT,
        warning: '区域完全为纯黑像素',
        possibleCauses: ['真的是黑色区域', '视频转换有问题', '检测区域错误']
      });
    }
    
    console.log('T区域计算结果:', {
      像素总数: pixelCountT,
      有效像素数: validPixelCountT,
      纯黑噪声像素数: pixelCountT - validPixelCountT,
      R通道总值: totalRT,
      G通道总值: totalGT,
      B通道总值: totalBT,
      最终灰度值: tValue
    });

    console.log('==================分析结果汇总==================');
    console.log('分析结果:', {
      C区域灰度值: cValue,
      T区域灰度值: tValue,
      C区域坐标: cArea,
      T区域坐标: tArea,
      图像尺寸: {
        width: imageWidth,
        height: imageHeight
      }
    });

    // 返回分析结果
    return {
      cArea: cValue,
      tArea: tValue,
      coords: {
        cArea: cArea,
        tArea: tArea
      },
      dimensions: {
        width: imageWidth,
        height: imageHeight
      },
      framePath: framePath,
      originalImage: image
    };
  } catch (error) {
    console.error('分析帧灰度值失败:', error);
    throw error;
  }
}

// 优化区域验证函数
function isValidRegion(region, imageWidth, imageHeight) {
  if (!region || 
      typeof region.x !== 'number' || 
      typeof region.y !== 'number' || 
      typeof region.width !== 'number' || 
      typeof region.height !== 'number') {
    console.error('无效的区域参数:', region);
    return false;
  }

  // 确保区域在图像范围内
  if (region.x < 0 || 
      region.y < 0 || 
      region.width <= 0 || 
      region.height <= 0 || 
      region.x + region.width > imageWidth || 
      region.y + region.height > imageHeight) {
    console.error('区域超出图像范围:', {
      region,
      imageSize: `${imageWidth}x${imageHeight}`
    });
    return false;
  }

  return true;
}

// 优化区域调整函数
function adjustRegionToImageSize(region, imageWidth, imageHeight) {
  const adjustedRegion = { ...region };
  
  // 确保区域不超出图像边界
  adjustedRegion.x = Math.max(0, Math.min(region.x, imageWidth - 1));
  adjustedRegion.y = Math.max(0, Math.min(region.y, imageHeight - 1));
  adjustedRegion.width = Math.min(region.width, imageWidth - adjustedRegion.x);
  adjustedRegion.height = Math.min(region.height, imageHeight - adjustedRegion.y);
  
  // 确保区域至少有1x1像素
  adjustedRegion.width = Math.max(1, adjustedRegion.width);
  adjustedRegion.height = Math.max(1, adjustedRegion.height);
  
  return adjustedRegion;
}

// 下载视频文件
async function downloadVideo(videoUrl, targetPath) {
  try {
    if (videoUrl.startsWith('cloud://')) {
      // 从云存储下载
      console.log(`从云存储下载视频: ${videoUrl}`);
      const result = await cloud.downloadFile({
        fileID: videoUrl
      });
      fs.writeFileSync(targetPath, result.fileContent);
    } else if (videoUrl.startsWith('http')) {
      // 从HTTP URL下载
      console.log(`从HTTP URL下载视频: ${videoUrl}`);
      const axios = require('axios');
      const response = await axios({
        method: 'GET',
        url: videoUrl,
        responseType: 'arraybuffer'
      });
      fs.writeFileSync(targetPath, Buffer.from(response.data));
    } else {
      throw new Error('不支持的视频URL格式，请提供云存储ID或HTTP URL');
    }
    
    // 检查文件是否成功下载
    if (!fs.existsSync(targetPath)) {
      throw new Error('下载后未找到视频文件');
    }
    
    const stats = fs.statSync(targetPath);
    console.log(`视频下载完成，大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);
    return true;
  } catch (error) {
    console.error('下载视频失败:', error);
    throw new Error(`下载视频失败: ${error.message}`);
  }
}

// 获取视频信息
async function getVideoInfo(videoPath) {
  try {
    // 检查FFprobe路径
    let ffprobePath = FFMPEG_PATH.replace('ffmpeg', 'ffprobe');
    
    // 如果FFprobe不存在，尝试使用临时目录中的FFprobe
    if (!fs.existsSync(ffprobePath)) {
      console.log('FFprobe不存在，尝试使用临时目录中的FFprobe');
      ffprobePath = '/tmp/ffprobe';
      
      if (!fs.existsSync(ffprobePath)) {
        console.error('临时目录中的FFprobe也不存在');
        throw new Error('无法找到FFprobe');
      }
    }
    
    console.log('使用FFprobe路径:', ffprobePath);
    
    // 确保FFprobe有执行权限
    try {
      fs.chmodSync(ffprobePath, 0o755);
      console.log('已设置FFprobe执行权限');
    } catch (chmodErr) {
      console.warn('设置FFprobe执行权限失败:', chmodErr.message);
    }
    
    const command = `"${ffprobePath}" -v error -show_format -show_streams "${videoPath}"`;
    console.log('执行FFprobe命令:', command);
    
    const result = safeExecFFmpeg(command);
    return result;
  } catch (error) {
    console.error('获取视频信息失败:', error);
    throw new Error(`获取视频信息失败: ${error.message}`);
  }
}

// 提取视频帧
async function extractFrames(videoPath, outputDir) {
  try {
    console.log('开始提取视频帧...');
    console.log('视频路径:', videoPath);
    console.log('输出目录:', outputDir);

    // 确保输出目录存在
    await fs.ensureDir(outputDir);

    // 获取视频时长
    const durationCmd = `"${FFMPEG_PATH}" -i "${videoPath}" 2>&1 | findstr "Duration"`;
    const durationOutput = safeExecFFmpeg(durationCmd);
    const durationMatch = durationOutput.match(/Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})/); 
    
    if (!durationMatch) {
      throw new Error('无法获取视频时长');
    }

    const hours = parseInt(durationMatch[1]);
    const minutes = parseInt(durationMatch[2]);
    const seconds = parseInt(durationMatch[3]);
    const milliseconds = parseInt(durationMatch[4]) * 10;
    
    const totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
    const frameCount = Math.floor(totalMilliseconds / 200); // 每200毫秒一帧

    console.log(`视频总时长: ${totalMilliseconds}ms, 预计提取${frameCount}帧`);

    // 使用FFmpeg提取帧，设置帧率为5fps (200ms间隔)
    const extractCmd = `"${FFMPEG_PATH}" -i "${videoPath}" -vf "fps=5" -frame_pts 1 "${outputDir}/frame_%d.jpg"`;
    safeExecFFmpeg(extractCmd);

    console.log('视频帧提取完成');
    return frameCount;

  } catch (error) {
    console.error('提取视频帧失败:', error);
    throw error;
  }
}


async function extractFramesEvery200ms(videoPath, outputDir, durationMs) {
  try {
    console.log('使用固定间隔(200毫秒)提取帧...');
    
    // 确保FFmpeg路径正确
    if (!fs.existsSync(FFMPEG_PATH)) {
      console.log('FFmpeg不存在，尝试使用临时目录中的FFmpeg');
      FFMPEG_PATH = '/tmp/ffmpeg';
      
      if (!fs.existsSync(FFMPEG_PATH)) {
        console.error('临时目录中的FFmpeg也不存在');
        throw new Error('无法找到FFmpeg');
      }
    }
    
    console.log(`使用FFmpeg路径: ${FFMPEG_PATH}`);
    
    // 确保FFmpeg有执行权限
    try {
      fs.chmodSync(FFMPEG_PATH, 0o755);
      console.log('已设置FFmpeg执行权限');
    } catch (chmodErr) {
      console.warn('设置FFmpeg执行权限失败:', chmodErr.message);
    }
    
    // 计算要提取的帧数
    const frameCount = Math.ceil(durationMs / 200);
    console.log('视频时长' + durationMs + '毫秒，按每200毫秒一帧，预计提取' + frameCount + '帧');
    
    // 尝试多种帧提取方法，从最简单的开始
    let extractionSuccess = false;
    let extractedFrames = [];
    let warning = '';
    
    // 方法1: 使用最简单的参数提取PNG格式
    try {
      console.log('尝试方法1: 提取PNG格式帧');
      const command = '"' + FFMPEG_PATH + '" -i "' + videoPath + '" -vf "fps=5" "' + outputDir + '/frame_%04d.png"';
    console.log('执行FFmpeg命令: ' + command);
    
    safeExecFFmpeg(command);
    
      extractedFrames = fs.readdirSync(outputDir).filter(file => file.endsWith('.png'));
      console.log('方法1提取了' + extractedFrames.length + '帧PNG图像');
      
      if (extractedFrames.length > 0) {
        extractionSuccess = true;
        console.log('方法1成功提取帧');
      }
    } catch (error) {
      console.warn('方法1提取PNG帧失败:', error.message);
    }
    
    // 方法2: 如果PNG失败，尝试JPG格式
    if (!extractionSuccess) {
      try {
        console.log('尝试方法2: 提取JPG格式帧');
        const command = '"' + FFMPEG_PATH + '" -i "' + videoPath + '" -vf "fps=5" "' + outputDir + '/frame_%04d.jpg"';
        console.log('执行FFmpeg命令: ' + command);
        
        safeExecFFmpeg(command);
        
        extractedFrames = fs.readdirSync(outputDir).filter(file => file.endsWith('.jpg'));
        console.log('方法2提取了' + extractedFrames.length + '帧JPG图像');
        
        if (extractedFrames.length > 0) {
          extractionSuccess = true;
          warning = '使用JPG格式提取帧';
          console.log('方法2成功提取帧');
        }
      } catch (error) {
        console.warn('方法2提取JPG帧失败:', error.message);
      }
    }
    
    // 方法3: 使用更基本的参数
    if (!extractionSuccess) {
      try {
        console.log('尝试方法3: 使用基本参数提取帧');
        const command = '"' + FFMPEG_PATH + '" -i "' + videoPath + '" -r 5 "' + outputDir + '/frame_%04d.jpg"';
        console.log('执行FFmpeg命令: ' + command);
        
        safeExecFFmpeg(command);
        
        extractedFrames = fs.readdirSync(outputDir).filter(file => file.endsWith('.jpg'));
        console.log('方法3提取了' + extractedFrames.length + '帧JPG图像');
        
        if (extractedFrames.length > 0) {
          extractionSuccess = true;
          warning = '使用基本参数提取帧';
          console.log('方法3成功提取帧');
        }
      } catch (error) {
        console.warn('方法3提取帧失败:', error.message);
      }
    }
    
    // 方法4: 最后尝试使用最简单的命令
    if (!extractionSuccess) {
      try {
        console.log('尝试方法4: 使用最简单的命令提取帧');
        const command = '"' + FFMPEG_PATH + '" -i "' + videoPath + '" -frames:v ' + frameCount + ' "' + outputDir + '/frame_%04d.jpg"';
        console.log('执行FFmpeg命令: ' + command);
        
        safeExecFFmpeg(command);
        
        extractedFrames = fs.readdirSync(outputDir).filter(file => file.endsWith('.jpg'));
        console.log('方法4提取了' + extractedFrames.length + '帧JPG图像');
        
        if (extractedFrames.length > 0) {
          extractionSuccess = true;
          warning = '使用最简单命令提取帧';
          console.log('方法4成功提取帧');
        }
      } catch (error) {
        console.warn('方法4提取帧失败:', error.message);
      }
    }
    
    // 如果所有方法都失败，抛出错误
    if (!extractionSuccess) {
      throw new Error('所有帧提取方法都失败');
    }
    
    // 检查提取的帧数量是否合理
    if (extractedFrames.length < Math.min(5, frameCount * 0.1)) {
      warning = '提取的帧数量较少，分析结果可能不准确';
      console.warn(warning);
    }
    
    return {
      success: true,
      frameCount: extractedFrames.length,
      warning: warning || undefined
    };
  } catch (error) {
    console.error('提取视频帧失败:', error);
    throw error;
  }
}

// 将前端传递的检测区域坐标转换为后端可用的格式
function convertFrontendRegionToBackend(region, imageWidth, imageHeight) {
  console.log('==================坐标转换详细日志==================');
  console.log('1. 前端传入的原始区域参数:', {
    x: region.x,
    y: region.y,
    width: region.width,
    height: region.height,
    videoWidth: region.videoWidth,
    videoHeight: region.videoHeight
  });

  if (!region || !region.videoWidth || !region.videoHeight) {
    throw new Error('缺少视频尺寸信息');
  }

  // 前端容器固定尺寸
  const containerWidth = 370;  // 前端容器固定宽度
  const containerHeight = 270; // 前端容器固定高度
  
  // 计算视频在容器中的实际显示尺寸（保持宽高比）
  const videoAspectRatio = region.videoWidth / region.videoHeight;
  const containerAspectRatio = containerWidth / containerHeight;
  
  let displayWidth, displayHeight;
  if (videoAspectRatio > containerAspectRatio) {
    // 视频比容器更宽，以宽度为准
    displayWidth = containerWidth;
    displayHeight = containerWidth / videoAspectRatio;
  } else {
    // 视频比容器更高，以高度为准
    displayHeight = containerHeight;
    displayWidth = containerHeight * videoAspectRatio;
  }
  
  // 计算视频在容器中的偏移量（居中显示）
  const offsetX = Math.round((containerWidth - displayWidth) / 2);
  const offsetY = Math.round((containerHeight - displayHeight) / 2);
  
  console.log('2. 视频显示计算:', {
    容器尺寸: `${containerWidth}x${containerHeight}`,
    视频原始比例: videoAspectRatio.toFixed(4),
    容器比例: containerAspectRatio.toFixed(4),
    显示尺寸: `${displayWidth.toFixed(2)}x${displayHeight.toFixed(2)}`,
    偏移量: `(${offsetX},${offsetY})`
  });
  
  // 重要：width和height是rpx单位，需要转换为px
  // 假设标准设计宽度为750rpx对应375px (iPhone 6)
  // 在containerWidth=370px的情况下，rpx到px的换算比例约为：370/750 = 0.493
  const rpxToPxRatio = containerWidth / 750;
  
  // 将rpx单位的宽高转换为px单位
  const widthInPx = Math.round(region.width * rpxToPxRatio);
  const heightInPx = Math.round(region.height * rpxToPxRatio);
  
  console.log('3. rpx到px的转换:', {
    rpxToPxRatio: rpxToPxRatio.toFixed(4),
    '宽度(rpx)': region.width,
    '高度(rpx)': region.height,
    '宽度(px)': widthInPx,
    '高度(px)': heightInPx
  });
  
  // 将前端坐标转换为相对于视频显示区域的坐标
  const relativeX = Math.max(0, region.x - offsetX);
  const relativeY = Math.max(0, region.y - offsetY);
  
  // 确保坐标不超出视频显示区域
  const clampedX = Math.min(relativeX, displayWidth);
  const clampedY = Math.min(relativeY, displayHeight);
  
  console.log('4. 相对于视频显示区域的坐标:', {
    原始坐标: `(${region.x},${region.y})`,
    偏移量: `(${offsetX},${offsetY})`,
    相对坐标: `(${relativeX},${relativeY})`,
    调整后坐标: `(${clampedX},${clampedY})`
  });
  
  // 计算缩放比例（从显示尺寸到实际视频尺寸）
  const scaleX = region.videoWidth / displayWidth;
  const scaleY = region.videoHeight / displayHeight;
  
  console.log('5. 缩放比例:', {
    scaleX: scaleX.toFixed(4),
    scaleY: scaleY.toFixed(4)
  });
  
  // 应用缩放（从显示尺寸转换到实际视频尺寸）
  const videoX = Math.round(clampedX * scaleX);
  const videoY = Math.round(clampedY * scaleY);
  
  // 宽度和高度使用转换后的px值进行缩放
  const videoWidth = Math.round(widthInPx * scaleX);
  const videoHeight = Math.round(heightInPx * scaleY);
  
  console.log('6. 应用缩放后的坐标:', {
    x: videoX,
    y: videoY,
    width: videoWidth,
    height: videoHeight,
    '缩放前尺寸(px)': `${widthInPx}x${heightInPx}`,
    '缩放后尺寸': `${videoWidth}x${videoHeight}`
  });

  // 确保坐标和尺寸在有效范围内
  const adjustedRegion = {
    x: Math.max(0, Math.min(videoX, imageWidth - 1)),
    y: Math.max(0, Math.min(videoY, imageHeight - 1)),
    width: Math.min(videoWidth, imageWidth - Math.max(0, videoX)),
    height: Math.min(videoHeight, imageHeight - Math.max(0, videoY))
  };

  console.log('7. 最终调整后的检测区域:', {
    x: adjustedRegion.x,
    y: adjustedRegion.y,
    width: adjustedRegion.width,
    height: adjustedRegion.height,
    '区域范围': `(${adjustedRegion.x},${adjustedRegion.y}) -> (${adjustedRegion.x + adjustedRegion.width},${adjustedRegion.y + adjustedRegion.height})`,
    '占图像比例': {
      x: (adjustedRegion.x / imageWidth * 100).toFixed(2) + '%',
      y: (adjustedRegion.y / imageHeight * 100).toFixed(2) + '%',
      width: (adjustedRegion.width / imageWidth * 100).toFixed(2) + '%',
      height: (adjustedRegion.height / imageHeight * 100).toFixed(2) + '%'
    }
  });
  console.log('================================================');

  return adjustedRegion;
}

// 添加新函数：创建数据库索引
async function createDatabaseIndex(task, resultData, filePaths) { // Pass the whole task object
  try {
    const openid = task.openid;
    const folderName = task.sessionId;

    console.log(`尝试为分析结果创建数据库索引: openid=${openid}, sessionId=${folderName}`);

    if (!openid || !folderName || !resultData || !task) {
      console.error('创建数据库索引失败: 参数不完整');
      return false;
    }

    const formattedOpenid = ensureUnderscoreFormat(openid);
    const formattedFolderName = ensureUnderscoreFormat(folderName);

    // --- START: Calculate the SPECIFIC videoPath format for DB following the desired pattern --- 
    let actualSessionFolder = formattedFolderName; // Default to formatted sessionId
    // Extract actual session folder from image path if available
    if (filePaths.tAreaImagePath) {
      const matches = filePaths.tAreaImagePath.match(/users\/[^\/]+_data\/([^\/]+)\/analysis_images/);
      if (matches && matches[1]) {
        actualSessionFolder = ensureUnderscoreFormat(matches[1]); 
        console.log(`[DB VideoPath] Extracted actual session folder from tAreaImage: ${actualSessionFolder}`);
      }
    } else if (filePaths.cAreaImagePath) {
      const matches = filePaths.cAreaImagePath.match(/users\/[^\/]+_data\/([^\/]+)\/analysis_images/);
      if (matches && matches[1]) {
        actualSessionFolder = ensureUnderscoreFormat(matches[1]); 
        console.log(`[DB VideoPath] Extracted actual session folder from cAreaImage: ${actualSessionFolder}`);
      }
    } else {
        console.warn(`[DB VideoPath] Could not extract actual session folder from image paths. Using sessionId: ${actualSessionFolder}`);
    }

    let accurateVideoPathForDB = null;
    // Construct the expected filename: {actualSessionFolder}_{timestamp_ms}.mp4
    const timestampMs = Date.now();
    const expectedVideoFileName = `${actualSessionFolder}_${timestampMs}.mp4`;
    console.log(`[DB VideoPath] Constructed expected filename: ${expectedVideoFileName}`);

    // Construct the desired path format
    if (formattedOpenid && actualSessionFolder && expectedVideoFileName) {
        // Construct the path part first
        let pathPart = `users/${formattedOpenid}_data/${actualSessionFolder}/videos/${expectedVideoFileName}`;
        // Ensure underscores for the path part
        pathPart = ensureUnderscoreFormat(pathPart);
        // Add the full cloud prefix
        accurateVideoPathForDB = `cloud://${FULL_ENV_ID}/${pathPart}`;
        console.log(`[DB VideoPath] Constructed final desired path for DB: ${accurateVideoPathForDB}`);
    } else {
      console.error(`[DB VideoPath] Failed to construct desired path. Missing components: openid=${formattedOpenid}, folder=${actualSessionFolder}, filename=${expectedVideoFileName}`);
      // Fallback to null if construction fails
      accurateVideoPathForDB = null; 
    }
    // --- END: Calculate the SPECIFIC videoPath format for DB --- 

    // Prepare formattedPaths using original logic (for URL generation etc.)
    // This part remains UNCHANGED from the currently accepted code state
    const formattedPaths = {
      resultFilePath: ensureUnderscoreFormat(filePaths.resultFilePath),
      cAreaImagePath: ensureUnderscoreFormat(filePaths.cAreaImage),
      tAreaImagePath: ensureUnderscoreFormat(filePaths.tAreaImage),
      markedImagePath: ensureUnderscoreFormat(filePaths.markedImage),
      // 直接使用原始视频 FileID（包含连字符），避免失效
      videoPath: filePaths.videoPath,
      videoUrl: filePaths.videoUrl // Keep original logic here
    };

    // 为每个文件生成带签名的下载链接 (Keep original logic)
    const downloadUrls = {};
    async function generateFileDownloadUrl(fileId) {
       // ... (Function content remains UNCHANGED from the currently accepted code state) ...
        if (!fileId) {
          console.log('[DownloadURL] 文件ID为空，跳过获取下载链接');
          return null;
        }

        try {
          console.log('[DownloadURL] 处理文件ID:', fileId);

          if (fileId.startsWith('http://') || fileId.startsWith('https://')) {
            console.log('[DownloadURL] 文件ID已经是URL格式，直接返回:', fileId);
            return fileId;
          }

          const isVideo = fileId.toLowerCase().includes('.mp4') ||
                         fileId.toLowerCase().includes('/video') ||
                         fileId.includes('/videos/');

          console.log('[DownloadURL] 文件类型检查:', isVideo ? '视频文件' : '非视频文件');

          let formattedFileID = fileId;
          if (!fileId.startsWith('cloud://')) {
            if (fileId.startsWith('/')) {
              formattedFileID = `cloud://${FULL_ENV_ID}${fileId}`;
            } else {
              formattedFileID = `cloud://${FULL_ENV_ID}/${fileId}`;
            }
          }

          console.log('[DownloadURL] 格式化后的文件ID:', formattedFileID);

              const result = await cloud.getTempFileURL({
                fileList: [formattedFileID]
              });

          if (result.fileList && result.fileList.length > 0) {
            const fileResult = result.fileList[0];
            if (fileResult.status === 0 && fileResult.tempFileURL) {
              console.log('[DownloadURL] 成功获取临时URL:', fileResult.tempFileURL);
              return fileResult.tempFileURL;
            } else {
              console.error('[DownloadURL] 获取临时URL失败:', fileResult);
              return null;
            }
          } else {
            console.error('[DownloadURL] 获取临时URL返回无效结果:', result);
          return null;
          }
        } catch (error) {
          console.error('[DownloadURL] 获取临时URL出错:', error);
          return null;
        }
    }
    
    // 获取各个文件的下载链接
    try {
      downloadUrls.resultFile = await generateFileDownloadUrl(formattedPaths.resultFilePath);
      
      // 优先使用分析结果中已经包含的临时URL
      if (resultData.images?.cAreaDownloadUrl) {
        downloadUrls.cAreaImage = resultData.images.cAreaDownloadUrl;
        console.log('使用分析结果中的C区图片临时URL:', downloadUrls.cAreaImage);
      } else {
        downloadUrls.cAreaImage = await generateFileDownloadUrl(formattedPaths.cAreaImagePath);
        console.log('通过generateFileDownloadUrl获取C区图片临时URL:', downloadUrls.cAreaImage);
      }
      
      if (resultData.images?.tAreaDownloadUrl) {
        downloadUrls.tAreaImage = resultData.images.tAreaDownloadUrl;
        console.log('使用分析结果中的T区图片临时URL:', downloadUrls.tAreaImage);
      } else {
        downloadUrls.tAreaImage = await generateFileDownloadUrl(formattedPaths.tAreaImagePath);
        console.log('通过generateFileDownloadUrl获取T区图片临时URL:', downloadUrls.tAreaImage);
      }
      
      downloadUrls.video = await generateFileDownloadUrl(formattedPaths.videoPath);
    } catch (urlError) {
      console.error('生成下载链接失败:', urlError);
    }

    // 提取参数数据 - 确保从task.parameters中获取前端参数模式的所有数据
    const parameters = task.parameters || {};
    
    // 获取检测区域参数
    const detectionAreas = parameters.detectionAreas || {};
    
    // 记录视频模式信息
    const isLocalVideo = parameters.isLocalVideo !== undefined ? parameters.isLocalVideo : true;
    const deviceIp = parameters.deviceIp || '';
    console.log(`视频模式: ${isLocalVideo ? '本地上传' : '动态IP'}, 设备IP: ${deviceIp}`);
    
    // 提取视频参数设置
    const videoParameters = {
      brightness: parameters.brightness || 115,
      contrast: parameters.contrast || 115,
      saturation: parameters.saturation || 106,
      white_balance_temperature_auto: parameters.white_balance_temperature_auto || 0,
      gain: parameters.gain || 0,
      power_line_frequency: parameters.power_line_frequency || 2,
      white_balance_temperature: parameters.white_balance_temperature || 4650,
      sharpness: parameters.sharpness || 10,
      exposure_auto: parameters.exposure_auto || 3,
      exposure_absolute: parameters.exposure_absolute || 1250,
      pan_absolute: parameters.pan_absolute || 0,
      tilt_absolute: parameters.tilt_absolute || 0,
      focus_absolute: parameters.focus_absolute || 0,
      zoom_absolute: parameters.zoom_absolute || 100,

      setVoltage: parameters.setVoltage || 5000,
      concentration: parameters.concentration || '', // 添加用户输入的浓度参数
      // 添加颜色处理参数
      colorConfig: parameters.colorConfig || {
        redThreshold: {
          hueMin: 0,
          hueMax: 30,
          satMin: 50,
          valMin: 50
        },
        enhanceRed: true,
        preserveOriginalColor: true
      },
      // 添加视频模式信息
      isLocalVideo: isLocalVideo,
      deviceIp: deviceIp
    };
    
    // 获取分析结果数据
    const analysisResults = {
      cAreaValue: resultData.cAreaValue || null,
      tAreaValue: resultData.tAreaValue || null,
      tcValue: resultData.tcValue || null
    };

    // 准备数据库记录
    const db = cloud.database();
    const indexCollection = db.collection('analysis_index');
    
    // 获取当前时间
    const now = new Date();
    
    // 从文件夹名称中提取时间信息（如果可能）
    let analysisTime = now;
    if (actualSessionFolder) {
      // 尝试从文件夹名称中解析时间
      const timePattern = /(\d{4})_(\d{2})_(\d{2})_(\d{2})_(\d{2})_(\d{2})/;
      const timeMatch = timePattern.exec(actualSessionFolder);
      if (timeMatch && timeMatch.length >= 7) {
        try {
          const year = parseInt(timeMatch[1]);
          const month = parseInt(timeMatch[2]) - 1; // 月份从0开始
          const day = parseInt(timeMatch[3]);
          const hour = parseInt(timeMatch[4]);
          const minute = parseInt(timeMatch[5]);
          const second = parseInt(timeMatch[6]);
          
          analysisTime = new Date(year, month, day, hour, minute, second);
          console.log('从文件夹名称提取的时间:', analysisTime);
        } catch (timeError) {
          console.error('解析时间失败，使用当前时间:', timeError);
          analysisTime = now;
        }
      }
    }

    // 创建数据库记录
    const record = {
        _openid: formattedOpenid,
      sessionId: formattedFolderName,
      customName: task.customName || '',
      createTime: analysisTime,
      updateTime: now,
      
      // 文件路径
      resultFilePath: formattedPaths.resultFilePath || '',
      cAreaImagePath: formattedPaths.cAreaImagePath || '',
      tAreaImagePath: formattedPaths.tAreaImagePath || '',
      markedImagePath: formattedPaths.markedImagePath || '',
      // 直接保留前端日志中的完整 FileID，不再重新构造
      videoPath: filePaths.videoPath,
      
      // videoUrl 的值，优先使用前端传递过来的、日志中打印的那个临时URL
      videoUrl: task.videoTempUrl || downloadUrls.video || '',
      
      // 分析结果
      ...analysisResults,
      
      // 前端参数模式数据
      videoParameters: videoParameters,
      detectionAreas: detectionAreas,
      
      // 临时标记 - 默认为临时状态
      isTemp: parameters.isTemp !== undefined ? parameters.isTemp : true,
      
      // 原始任务数据的引用
      taskId: task._id || '',
      platform: task.platform || 'miniprogram',
      deviceInfo: task.deviceInfo || {}
    };
    
    console.log('准备创建数据库记录:', record);
    
    try {
      // 检查是否已存在相同sessionId的记录
      const existingRecord = await indexCollection.where({
        _openid: formattedOpenid,
        sessionId: formattedFolderName
      }).get();
      
    if (existingRecord.data && existingRecord.data.length > 0) {
        // 更新现有记录
        const updateResult = await indexCollection.doc(existingRecord.data[0]._id).update({
          data: record
        });
        console.log('更新现有数据库记录结果:', updateResult);
      } else {
        // 创建新记录
        const addResult = await indexCollection.add({
          data: record
        });
        console.log('创建新数据库记录结果:', addResult);
      }
      
    return true;
    } catch (dbError) {
      console.error('创建或更新数据库记录失败:', dbError);
      return false;
    }
  } catch (error) {
    console.error('创建数据库索引出错:', error);
    return false;
  }
}

// 添加新函数：确保路径使用下划线格式
function ensureUnderscoreFormat(path) {
  if (!path) return path;
  // 将所有连字符替换为下划线
  return path.replace(/-/g, '_');
}

// 测试cloud.getTempFileURL API是否可用
async function testCloudAPI() {
  try {
    console.log('测试cloud.getTempFileURL API...');
    
    // 获取当前云环境ID
    const currentEnv = cloud.DYNAMIC_CURRENT_ENV || 'wlksapp-4g54hauu5cbf43dc';
    // 确保环境ID使用连字符而不是下划线
    const normalizedEnv = currentEnv.replace(/_/g, '-');
    
    // 使用一个通用示例文件路径进行测试
    const testFileID = `cloud://${normalizedEnv}/test-file.txt`;
    console.log('测试文件ID:', testFileID);
    
    // 也尝试使用云环境ID的另一种格式
    const altTestFileID = `cloud://${normalizedEnv}.776c-${normalizedEnv}-1329876191/test-file.txt`;
    console.log('替代测试文件ID:', altTestFileID);
    
    // 分别测试两种文件ID格式
    console.log('测试标准格式...');
    try {
      const result1 = await cloud.getTempFileURL({
        fileList: [testFileID]
      });
      console.log('标准格式测试结果:', JSON.stringify(result1));
    } catch (err1) {
      console.error('标准格式测试失败:', err1.message);
    }
    
    console.log('测试完整格式...');
    try {
      const result2 = await cloud.getTempFileURL({
        fileList: [altTestFileID]
      });
      console.log('完整格式测试结果:', JSON.stringify(result2));
    } catch (err2) {
      console.error('完整格式测试失败:', err2.message);
    }
    
    // 检查云函数配置和权限
    console.log('环境信息:');
    console.log('DYNAMIC_CURRENT_ENV:', cloud.DYNAMIC_CURRENT_ENV);
    console.log('API权限状态检查完成');
    
    return true;
  } catch (error) {
    console.error('cloud.getTempFileURL API总测试失败:', error);
    return false;
  }
}

// 将 tcb.qcloud.la 临时下载链接转换为 cloud:// FileID
function convertTcbUrlToCloudId(url) {
  if (!url || typeof url !== 'string') return null;
  // 只处理 tcb 域名
  const match = url.match(/^https?:\/\/([^\/]+)\.tcb\.qcloud\.la\/(.+)$/);
  if (!match) return null;
  const envHost = match[1];
  const pathPart = match[2];
  return `cloud://${envHost}/${pathPart}`;
}

// 🎨 检查是否需要应用视频参数处理 - 已禁用FFmpeg参数处理功能
function checkIfParametersNeedProcessing(parameters) {
  // 🚫 FFmpeg参数处理功能已禁用，始终返回false
  console.log('🚫 FFmpeg参数处理功能已禁用，跳过参数处理');
  return false;

  /* 原始参数检查逻辑已注释
  if (!parameters) return false;

  // 定义默认参数值
  const defaultParams = {
    brightness: 115,
    contrast: 115,
    saturation: 106,
    white_balance_temperature: 4650,
    white_balance_temperature_auto: 0,
    gain: 0,
    exposure_absolute: 1250,
    exposure_auto: 3,
    sharpness: 10,
    power_line_frequency: 2
  };

  // 检查是否有参数与默认值不同（使用与处理时相同的默认值逻辑）
  const needsProcessing = Object.keys(defaultParams).some(key => {
    const currentValue = parameters[key] || defaultParams[key]; // 🎯 使用与处理时相同的逻辑
    const defaultValue = defaultParams[key];
    return currentValue !== defaultValue;
  });

  console.log('参数检查结果:', {
    brightness: `${parameters.brightness || defaultParams.brightness} (默认: ${defaultParams.brightness})`,
    contrast: `${parameters.contrast || defaultParams.contrast} (默认: ${defaultParams.contrast})`,
    saturation: `${parameters.saturation || defaultParams.saturation} (默认: ${defaultParams.saturation})`,
    white_balance_temperature: `${parameters.white_balance_temperature || defaultParams.white_balance_temperature} (默认: ${defaultParams.white_balance_temperature})`,
    needsProcessing: needsProcessing
  });

  return needsProcessing;
  */
}

// 🎨 应用视频参数处理 - 已禁用FFmpeg参数处理功能
async function applyVideoParameters(inputVideoPath, parameters, outputDir) {
  try {
    // 🚫 FFmpeg参数处理功能已禁用，直接返回原视频路径
    console.log('🚫 FFmpeg参数处理功能已禁用，直接使用原视频');
    return inputVideoPath;

    /* 原始FFmpeg参数处理逻辑已注释
    console.log('🎨 开始应用视频参数处理');

    const outputVideoPath = path.join(outputDir, 'processed_video.mp4');

    // 🎯 按照正确的FFmpeg处理顺序构建滤镜链
    const filters = [];

    // 1️⃣ 电力线频率滤波 (最先应用，清理输入信号)
    if (parameters.power_line_frequency !== undefined && parameters.power_line_frequency !== 2) {
      if (parameters.power_line_frequency === 0) {
        // 禁用：不添加滤波器
        console.log('🎨 电力线频率: 禁用');
      } else if (parameters.power_line_frequency === 1) {
        // 50Hz：使用针对50Hz的降噪
        filters.push(`hqdn3d=4:3:6:4.5`);
        console.log('🎨 电力线频率: 50Hz降噪');
      }
      // 默认2(60Hz)不需要特殊处理
    }

    // 2️⃣ 基础图像调整 (eq滤镜 - 合并亮度、对比度、饱和度、伽马)
    const eqParams = [];

    // 亮度调节 (brightness: 0-230, 默认115, FFmpeg范围: -1.0 到 1.0)
    let brightnessValue = 0;
    if (parameters.brightness !== undefined && parameters.brightness !== 115) {
      // 正确算法：将0-230范围映射到-1.0到1.0
      // 115是中点(0)，0对应-1.0，230对应1.0
      brightnessValue = (parameters.brightness - 115) / 115;
      // 限制到FFmpeg支持的范围
      brightnessValue = Math.max(-1.0, Math.min(1.0, brightnessValue));
      console.log(`🎨 亮度参数: ${parameters.brightness} → FFmpeg值: ${brightnessValue.toFixed(3)}`);
    }

    // 增益调节 (gain: 0-100, 默认0) - 合并到亮度中
    if (parameters.gain !== undefined && parameters.gain !== 0) {
      const gainValue = (parameters.gain / 100 * 0.5);
      brightnessValue += gainValue;
    }

    if (brightnessValue !== 0) {
      const brightnessParam = `brightness=${brightnessValue.toFixed(3)}`;
      eqParams.push(brightnessParam);
      console.log(`🎨 添加亮度滤镜: ${brightnessParam}`);
    }

    // 对比度调节 (contrast: 0-230, 默认115, FFmpeg范围: 0.0 到 2.0)
    if (parameters.contrast !== undefined && parameters.contrast !== 115) {
      // 正确算法：将0-230范围映射到0.0到2.0
      // 115是中点(1.0)，0对应0.0，230对应2.0
      const contrastValue = (parameters.contrast / 115);
      const clampedContrast = Math.max(0.0, Math.min(2.0, contrastValue));
      eqParams.push(`contrast=${clampedContrast.toFixed(3)}`);
      console.log(`🎨 对比度参数: ${parameters.contrast} → FFmpeg值: ${clampedContrast.toFixed(3)}`);
    }

    // 饱和度调节 (saturation: 0-212, 默认106, FFmpeg范围: 0.0 到 2.0)
    if (parameters.saturation !== undefined && parameters.saturation !== 106) {
      // 正确映射：106→1.0, 0→0.0, 212→2.0
      const saturationValue = parameters.saturation / 106;
      const clampedSaturation = Math.max(0.0, Math.min(2.0, saturationValue));
      eqParams.push(`saturation=${clampedSaturation.toFixed(3)}`);
      console.log(`🎨 饱和度: ${parameters.saturation} → ${clampedSaturation.toFixed(3)}`);
    }

    // 曝光调节 (exposure_absolute: 5-2500, 默认1250) - 使用gamma校正
    if (parameters.exposure_absolute !== undefined && parameters.exposure_absolute !== 1250) {
      const gamma = Math.log(parameters.exposure_absolute / 1250) / Math.log(2) + 1;
      const clampedGamma = Math.max(0.1, Math.min(10.0, gamma));
      eqParams.push(`gamma=${clampedGamma.toFixed(3)}`);
      console.log(`🎨 曝光: ${parameters.exposure_absolute} → gamma=${clampedGamma.toFixed(3)}`);
    }

    // 添加eq滤镜
    if (eqParams.length > 0) {
      filters.push(`eq=${eqParams.join(':')}`);
      console.log(`🎨 添加eq滤镜: ${eqParams.join(':')}`);
    }

    // 3️⃣ 白平衡处理 (色彩调整)

    // 自动白平衡 (white_balance_temperature_auto: 0=关闭, 1=开启)
    if (parameters.white_balance_temperature_auto !== undefined && parameters.white_balance_temperature_auto === 1) {
      // 使用简单的白平衡算法
      filters.push(`colorbalance=rs=0.1:gs=0.0:bs=-0.1`);
      console.log('🎨 自动白平衡: 开启');
    }

    // 手动色温调节 (white_balance_temperature: 2600-6500, 默认4650) - 会覆盖自动白平衡
    if (parameters.white_balance_temperature !== undefined && parameters.white_balance_temperature !== 4650) {
      const tempFactor = (parameters.white_balance_temperature - 4650) / 1950;
      // colorbalance滤镜的参数范围是-1到1
      if (tempFactor < 0) {
        // 暖色调：增加红色，减少蓝色
        const redShift = Math.max(-1.0, Math.min(1.0, Math.abs(tempFactor) * 0.5));
        const blueShift = Math.max(-1.0, Math.min(1.0, -Math.abs(tempFactor) * 0.3));
        filters.push(`colorbalance=rs=${redShift.toFixed(3)}:bs=${blueShift.toFixed(3)}`);
        console.log(`🎨 色温(暖): ${parameters.white_balance_temperature}K → rs=${redShift.toFixed(3)}, bs=${blueShift.toFixed(3)}`);
      } else {
        // 冷色调：增加蓝色，减少红色
        const blueShift = Math.max(-1.0, Math.min(1.0, tempFactor * 0.5));
        const redShift = Math.max(-1.0, Math.min(1.0, -tempFactor * 0.3));
        filters.push(`colorbalance=rs=${redShift.toFixed(3)}:bs=${blueShift.toFixed(3)}`);
        console.log(`🎨 色温(冷): ${parameters.white_balance_temperature}K → rs=${redShift.toFixed(3)}, bs=${blueShift.toFixed(3)}`);
      }
    }

    // 4️⃣ 锐度调节 (sharpness: 0-20, 默认10)
    if (parameters.sharpness !== undefined && parameters.sharpness !== 10) {
      // 正确映射：10→0(无变化), 0→-1.0(模糊), 20→1.0(锐化)
      const sharpnessFactor = (parameters.sharpness - 10) / 10;
      const clampedSharpness = Math.max(-1.0, Math.min(1.0, sharpnessFactor));

      if (clampedSharpness > 0) {
        // 锐化：使用unsharp滤镜
        filters.push(`unsharp=5:5:${clampedSharpness.toFixed(3)}:5:5:0.0`);
        console.log(`🎨 锐化: ${parameters.sharpness} → unsharp强度=${clampedSharpness.toFixed(3)}`);
      } else if (clampedSharpness < 0) {
        // 模糊：使用boxblur滤镜
        const blurRadius = Math.abs(clampedSharpness) * 2;
        filters.push(`boxblur=${blurRadius.toFixed(3)}`);
        console.log(`🎨 模糊: ${parameters.sharpness} → boxblur半径=${blurRadius.toFixed(3)}`);
      }
    }

    // 5️⃣ 自动曝光 (exposure_auto: 1=手动, 3=自动) - 最后应用
    if (parameters.exposure_auto !== undefined && parameters.exposure_auto === 3) {
      // 使用直方图均衡化模拟自动曝光
      filters.push(`histeq=strength=0.5`);
      console.log('🎨 自动曝光: 开启');
    }

    // 如果没有需要应用的滤镜，直接返回原视频
    if (filters.length === 0) {
      console.log('🎨 无需应用参数处理，使用原视频');
      return inputVideoPath;
    }

    // 构建FFmpeg命令 - 使用更好的编码参数
    const filterChain = filters.join(',');
    const ffmpegCommand = `"${FFMPEG_PATH}" -i "${inputVideoPath}" -vf "${filterChain}" -c:v libx264 -preset fast -crf 20 -pix_fmt yuv420p -c:a copy "${outputVideoPath}"`;

    console.log('🎨 执行FFmpeg参数处理命令:', ffmpegCommand);
    console.log('🎨 应用的滤镜:', filterChain);

    // 执行FFmpeg命令
    await safeExecFFmpeg(ffmpegCommand);

    // 验证输出文件
    if (fs.existsSync(outputVideoPath)) {
      const stats = fs.statSync(outputVideoPath);
      if (stats.size > 0) {
        console.log('🎨 视频参数处理成功，输出文件大小:', stats.size);
        return outputVideoPath;
      }
    }

    console.warn('🎨 视频参数处理失败，使用原视频');
    return inputVideoPath;
    */

  } catch (error) {
    console.error('🚫 视频参数处理函数出错:', error);
    console.warn('🚫 参数处理功能已禁用，使用原视频继续分析');
    return inputVideoPath;
  }
}
