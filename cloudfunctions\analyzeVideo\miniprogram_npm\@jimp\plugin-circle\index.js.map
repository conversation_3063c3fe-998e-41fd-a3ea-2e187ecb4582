{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional) radius, x, y\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    circle: function circle() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var cb = arguments.length > 1 ? arguments[1] : undefined;\n\n      if (typeof options === 'function') {\n        cb = options;\n        options = {};\n      }\n\n      var radius = options.radius || (this.bitmap.width > this.bitmap.height ? this.bitmap.height : this.bitmap.width) / 2;\n      var center = {\n        x: typeof options.x === 'number' ? options.x : this.bitmap.width / 2,\n        y: typeof options.y === 'number' ? options.y : this.bitmap.height / 2\n      };\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var curR = Math.sqrt(Math.pow(x - center.x, 2) + Math.pow(y - center.y, 2));\n\n        if (radius - curR <= 0.0) {\n          this.bitmap.data[idx + 3] = 0;\n        } else if (radius - curR < 1.0) {\n          this.bitmap.data[idx + 3] = 255 * (radius - curR);\n        }\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}