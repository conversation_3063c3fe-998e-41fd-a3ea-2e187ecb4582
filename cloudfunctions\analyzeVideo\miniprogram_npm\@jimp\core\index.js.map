{"version": 3, "sources": ["index.js", "modules/phash.js", "request.js", "composite/index.js", "constants.js", "composite/composite-modes.js", "utils/promisify.js", "utils/mime.js", "utils/image-bitmap.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;ACFA,AHSA,ACHA,ACHA;ACFA,AHSA,ACHA,ACHA;ACFA,AHSA,ACHA,ACHA;ACFA,ACHA,AJYA,ACHA,ACHA;ACFA,ACHA,AJYA,ACHA,ACHA;ACFA,ACHA,AJYA,ACHA,ACHA;AGRA,AFMA,ACHA,AJYA,ACHA,ACHA;AGRA,AFMA,ACHA,AJYA,ACHA,ACHA;AGRA,AFMA,ACHA,AJYA,ACHA,ACHA;AGRA,AFMA,ACHA,AJYA,ACHA,ACHA,AIZA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AIZA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AIZA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AKfA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AKfA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AKfA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA,ADGA;ADIA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,ACHA,AMlBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,AOrBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,AOrBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,AOrBA,ADGA;AFOA,AFMA,ACHA,AJYA,ACHA,AOrBA,ADGA;AFOA,AFMA,AHSA,ACHA,AOrBA,ADGA;AFOA,AFMA,AHSA,ACHA,AOrBA,ADGA;AFOA,AFMA,AHSA,ACHA,AOrBA,ADGA;AFOA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,AFMA,AHSA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,ACHA,AOrBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;AHUA,ALeA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA,AQxBA;ARyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addConstants = addConstants;\nexports.addJimpMethods = addJimpMethods;\nexports.jimpEvMethod = jimpEvMethod;\nexports.jimpEvChange = jimpEvChange;\nObject.defineProperty(exports, \"addType\", {\n  enumerable: true,\n  get: function get() {\n    return MIME.addType;\n  }\n});\nexports[\"default\"] = void 0;\n\nvar _construct2 = _interopRequireDefault(require(\"@babel/runtime/helpers/construct\"));\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\n\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\n\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\n\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\n\nvar _assertThisInitialized2 = _interopRequireDefault(require(\"@babel/runtime/helpers/assertThisInitialized\"));\n\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _fs = _interopRequireDefault(require(\"fs\"));\n\nvar _path = _interopRequireDefault(require(\"path\"));\n\nvar _events = _interopRequireDefault(require(\"events\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _anyBase = _interopRequireDefault(require(\"any-base\"));\n\nvar _mkdirp = _interopRequireDefault(require(\"mkdirp\"));\n\nvar _pixelmatch = _interopRequireDefault(require(\"pixelmatch\"));\n\nvar _tinycolor = _interopRequireDefault(require(\"tinycolor2\"));\n\nvar _phash = _interopRequireDefault(require(\"./modules/phash\"));\n\nvar _request = _interopRequireDefault(require(\"./request\"));\n\nvar _composite = _interopRequireDefault(require(\"./composite\"));\n\nvar _promisify = _interopRequireDefault(require(\"./utils/promisify\"));\n\nvar MIME = _interopRequireWildcard(require(\"./utils/mime\"));\n\nvar _imageBitmap = require(\"./utils/image-bitmap\");\n\nvar constants = _interopRequireWildcard(require(\"./constants\"));\n\nvar alphabet = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'; // an array storing the maximum string length of hashes at various bases\n// 0 and 1 do not exist as possible hash lengths\n\nvar maxHashLength = [NaN, NaN];\n\nfor (var i = 2; i < 65; i++) {\n  var maxHash = (0, _anyBase[\"default\"])(_anyBase[\"default\"].BIN, alphabet.slice(0, i))(new Array(64 + 1).join('1'));\n  maxHashLength.push(maxHash.length);\n} // no operation\n\n\nfunction noop() {} // error checking methods\n\n\nfunction isArrayBuffer(test) {\n  return Object.prototype.toString.call(test).toLowerCase().indexOf('arraybuffer') > -1;\n} // Prepare a Buffer object from the arrayBuffer. Necessary in the browser > node conversion,\n// But this function is not useful when running in node directly\n\n\nfunction bufferFromArrayBuffer(arrayBuffer) {\n  var buffer = Buffer.alloc(arrayBuffer.byteLength);\n  var view = new Uint8Array(arrayBuffer);\n\n  for (var _i = 0; _i < buffer.length; ++_i) {\n    buffer[_i] = view[_i];\n  }\n\n  return buffer;\n}\n\nfunction loadFromURL(options, cb) {\n  (0, _request[\"default\"])(options, function (err, response, data) {\n    if (err) {\n      return cb(err);\n    }\n\n    if ('headers' in response && 'location' in response.headers) {\n      options.url = response.headers.location;\n      return loadFromURL(options, cb);\n    }\n\n    if ((0, _typeof2[\"default\"])(data) === 'object' && Buffer.isBuffer(data)) {\n      return cb(null, data);\n    }\n\n    var msg = 'Could not load Buffer from <' + options.url + '> ' + '(HTTP: ' + response.statusCode + ')';\n    return new Error(msg);\n  });\n}\n\nfunction loadBufferFromPath(src, cb) {\n  if (_fs[\"default\"] && typeof _fs[\"default\"].readFile === 'function' && !src.match(/^(http|ftp)s?:\\/\\/./)) {\n    _fs[\"default\"].readFile(src, cb);\n  } else {\n    loadFromURL({\n      url: src\n    }, cb);\n  }\n}\n\nfunction isRawRGBAData(obj) {\n  return obj && (0, _typeof2[\"default\"])(obj) === 'object' && typeof obj.width === 'number' && typeof obj.height === 'number' && (Buffer.isBuffer(obj.data) || obj.data instanceof Uint8Array || typeof Uint8ClampedArray === 'function' && obj.data instanceof Uint8ClampedArray) && (obj.data.length === obj.width * obj.height * 4 || obj.data.length === obj.width * obj.height * 3);\n}\n\nfunction makeRGBABufferFromRGB(buffer) {\n  if (buffer.length % 3 !== 0) {\n    throw new Error('Buffer length is incorrect');\n  }\n\n  var rgbaBuffer = Buffer.allocUnsafe(buffer.length / 3 * 4);\n  var j = 0;\n\n  for (var _i2 = 0; _i2 < buffer.length; _i2++) {\n    rgbaBuffer[j] = buffer[_i2];\n\n    if ((_i2 + 1) % 3 === 0) {\n      rgbaBuffer[++j] = 255;\n    }\n\n    j++;\n  }\n\n  return rgbaBuffer;\n}\n\nvar emptyBitmap = {\n  data: null,\n  width: null,\n  height: null\n};\n/**\n * Jimp constructor (from a file)\n * @param path a path to the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a url with options)\n * @param options { url, otherOptions}\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from another Jimp image or raw image data)\n * @param image a Jimp image to clone\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a Buffer)\n * @param data a Buffer containing the image data\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param background color to fill the image with\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\nvar Jimp =\n/*#__PURE__*/\nfunction (_EventEmitter) {\n  (0, _inherits2[\"default\"])(Jimp, _EventEmitter);\n\n  // An object representing a bitmap in memory, comprising:\n  //  - data: a buffer of the bitmap data\n  //  - width: the width of the image in pixels\n  //  - height: the height of the image in pixels\n  // Default colour to use for new pixels\n  // Default MIME is PNG\n  // Exif data for the image\n  // Whether Transparency supporting formats will be exported as RGB or RGBA\n  function Jimp() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    (0, _classCallCheck2[\"default\"])(this, Jimp);\n    _this = (0, _possibleConstructorReturn2[\"default\"])(this, (0, _getPrototypeOf2[\"default\"])(Jimp).call(this));\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"bitmap\", emptyBitmap);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"_background\", 0x00000000);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"_originalMime\", Jimp.MIME_PNG);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"_exif\", null);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"_rgba\", true);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"writeAsync\", function (path) {\n      return (0, _promisify[\"default\"])(_this.write, (0, _assertThisInitialized2[\"default\"])(_this), path);\n    });\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"getBase64Async\", function (mime) {\n      return (0, _promisify[\"default\"])(_this.getBase64, (0, _assertThisInitialized2[\"default\"])(_this), mime);\n    });\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"getBuffer\", _imageBitmap.getBuffer);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"getBufferAsync\", _imageBitmap.getBufferAsync);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"getPixelColour\", _this.getPixelColor);\n    (0, _defineProperty2[\"default\"])((0, _assertThisInitialized2[\"default\"])(_this), \"setPixelColour\", _this.setPixelColor);\n    var jimpInstance = (0, _assertThisInitialized2[\"default\"])(_this);\n    var cb = noop;\n\n    if (isArrayBuffer(args[0])) {\n      args[0] = bufferFromArrayBuffer(args[0]);\n    }\n\n    function finish() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      var err = args[0];\n      var evData = err || {};\n      evData.methodName = 'constructor';\n      setTimeout(function () {\n        var _cb;\n\n        // run on next tick.\n        if (err && cb === noop) {\n          jimpInstance.emitError('constructor', err);\n        } else if (!err) {\n          jimpInstance.emitMulti('constructor', 'initialized');\n        }\n\n        (_cb = cb).call.apply(_cb, [jimpInstance].concat(args));\n      }, 1);\n    }\n\n    if (typeof args[0] === 'number' && typeof args[1] === 'number' || parseInt(args[0], 10) && parseInt(args[1], 10)) {\n      // create a new image\n      var w = parseInt(args[0], 10);\n      var h = parseInt(args[1], 10);\n      cb = args[2]; // with a hex color\n\n      if (typeof args[2] === 'number') {\n        _this._background = args[2];\n        cb = args[3];\n      } // with a css color\n\n\n      if (typeof args[2] === 'string') {\n        _this._background = Jimp.cssColorToHex(args[2]);\n        cb = args[3];\n      }\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'cb must be a function', finish));\n      }\n\n      _this.bitmap = {\n        data: Buffer.alloc(w * h * 4),\n        width: w,\n        height: h\n      };\n\n      for (var _i3 = 0; _i3 < _this.bitmap.data.length; _i3 += 4) {\n        _this.bitmap.data.writeUInt32BE(_this._background, _i3);\n      }\n\n      finish(null, (0, _assertThisInitialized2[\"default\"])(_this));\n    } else if ((0, _typeof2[\"default\"])(args[0]) === 'object' && args[0].url) {\n      cb = args[1] || noop;\n\n      if (typeof cb !== 'function') {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'cb must be a function', finish));\n      }\n\n      loadFromURL(args[0], function (err, data) {\n        if (err) {\n          return _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), err, finish);\n        }\n\n        _this.parseBitmap(data, args[0].url, finish);\n      });\n    } else if (args[0] instanceof Jimp) {\n      // clone an existing Jimp\n      var original = args[0];\n      cb = args[1];\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'cb must be a function', finish));\n      }\n\n      _this.bitmap = {\n        data: Buffer.from(original.bitmap.data),\n        width: original.bitmap.width,\n        height: original.bitmap.height\n      };\n      _this._quality = original._quality;\n      _this._deflateLevel = original._deflateLevel;\n      _this._deflateStrategy = original._deflateStrategy;\n      _this._filterType = original._filterType;\n      _this._rgba = original._rgba;\n      _this._background = original._background;\n      _this._originalMime = original._originalMime;\n      finish(null, (0, _assertThisInitialized2[\"default\"])(_this));\n    } else if (isRawRGBAData(args[0])) {\n      var imageData = args[0];\n      cb = args[1] || noop;\n      var isRGBA = imageData.width * imageData.height * 4 === imageData.data.length;\n      var buffer = isRGBA ? Buffer.from(imageData.data) : makeRGBABufferFromRGB(imageData.data);\n      _this.bitmap = {\n        data: buffer,\n        width: imageData.width,\n        height: imageData.height\n      };\n      finish(null, (0, _assertThisInitialized2[\"default\"])(_this));\n    } else if (typeof args[0] === 'string') {\n      // read from a path\n      var path = args[0];\n      cb = args[1];\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'cb must be a function', finish));\n      }\n\n      loadBufferFromPath(path, function (err, data) {\n        if (err) {\n          return _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), err, finish);\n        }\n\n        _this.parseBitmap(data, path, finish);\n      });\n    } else if ((0, _typeof2[\"default\"])(args[0]) === 'object' && Buffer.isBuffer(args[0])) {\n      // read from a buffer\n      var data = args[0];\n      cb = args[1];\n\n      if (typeof cb !== 'function') {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'cb must be a function', finish));\n      }\n\n      _this.parseBitmap(data, null, finish);\n    } else {\n      // Allow client libs to add new ways to build a Jimp object.\n      // Extra constructors must be added by `Jimp.appendConstructorOption()`\n      cb = args[args.length - 1];\n\n      if (typeof cb !== 'function') {\n        // TODO: try to solve the args after cb problem.\n        cb = args[args.length - 2];\n\n        if (typeof cb !== 'function') {\n          cb = noop;\n        }\n      }\n\n      var extraConstructor = Jimp.__extraConstructors.find(function (c) {\n        return c.test.apply(c, args);\n      });\n\n      if (extraConstructor) {\n        new Promise(function (resolve, reject) {\n          var _extraConstructor$run;\n\n          return (_extraConstructor$run = extraConstructor.run).call.apply(_extraConstructor$run, [(0, _assertThisInitialized2[\"default\"])(_this), resolve, reject].concat(args));\n        }).then(function () {\n          return finish(null, (0, _assertThisInitialized2[\"default\"])(_this));\n        })[\"catch\"](finish);\n      } else {\n        return (0, _possibleConstructorReturn2[\"default\"])(_this, _utils.throwError.call((0, _assertThisInitialized2[\"default\"])(_this), 'No matching constructor overloading was found. ' + 'Please see the docs for how to call the Jimp constructor.', finish));\n      }\n    }\n\n    return _this;\n  }\n  /**\n   * Parse a bitmap with the loaded image types.\n   *\n   * @param {Buffer} data raw image data\n   * @param {string} path optional path to file\n   * @param {function(Error, Jimp)} finish (optional) a callback for when complete\n   * @memberof Jimp\n   */\n\n\n  (0, _createClass2[\"default\"])(Jimp, [{\n    key: \"parseBitmap\",\n    value: function parseBitmap(data, path, finish) {\n      _imageBitmap.parseBitmap.call(this, data, null, finish);\n    }\n    /**\n     * Sets the type of the image (RGB or RGBA) when saving in a format that supports transparency (default is RGBA)\n     * @param {boolean} bool A Boolean, true to use RGBA or false to use RGB\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n\n  }, {\n    key: \"rgba\",\n    value: function rgba(bool, cb) {\n      if (typeof bool !== 'boolean') {\n        return _utils.throwError.call(this, 'bool must be a boolean, true for RGBA or false for RGB', cb);\n      }\n\n      this._rgba = bool;\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n    /**\n     * Emit for multiple listeners\n     * @param {string} methodName name of the method to emit an error for\n     * @param {string} eventName name of the eventName to emit an error for\n     * @param {object} data to emit\n     */\n\n  }, {\n    key: \"emitMulti\",\n    value: function emitMulti(methodName, eventName) {\n      var data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      data = Object.assign(data, {\n        methodName: methodName,\n        eventName: eventName\n      });\n      this.emit('any', data);\n\n      if (methodName) {\n        this.emit(methodName, data);\n      }\n\n      this.emit(eventName, data);\n    }\n  }, {\n    key: \"emitError\",\n    value: function emitError(methodName, err) {\n      this.emitMulti(methodName, 'error', err);\n    }\n    /**\n     * Get the current height of the image\n     * @return {number} height of the image\n     */\n\n  }, {\n    key: \"getHeight\",\n    value: function getHeight() {\n      return this.bitmap.height;\n    }\n    /**\n     * Get the current width of the image\n     * @return {number} width of the image\n     */\n\n  }, {\n    key: \"getWidth\",\n    value: function getWidth() {\n      return this.bitmap.width;\n    }\n    /**\n     * Nicely format Jimp object when sent to the console e.g. console.log(image)\n     * @returns {string} pretty printed\n     */\n\n  }, {\n    key: \"inspect\",\n    value: function inspect() {\n      return '<Jimp ' + (this.bitmap === emptyBitmap ? 'pending...' : this.bitmap.width + 'x' + this.bitmap.height) + '>';\n    }\n    /**\n     * Nicely format Jimp object when converted to a string\n     * @returns {string} pretty printed\n     */\n\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      return '[object Jimp]';\n    }\n    /**\n     * Returns the original MIME of the image (default: \"image/png\")\n     * @returns {string} the MIME\n     */\n\n  }, {\n    key: \"getMIME\",\n    value: function getMIME() {\n      var mime = this._originalMime || Jimp.MIME_PNG;\n      return mime;\n    }\n    /**\n     * Returns the appropriate file extension for the original MIME of the image (default: \"png\")\n     * @returns {string} the file extension\n     */\n\n  }, {\n    key: \"getExtension\",\n    value: function getExtension() {\n      var mime = this.getMIME();\n      return MIME.getExtension(mime);\n    }\n    /**\n     * Writes the image to a file\n     * @param {string} path a path to the destination file\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the image is saved to disk\n     * @returns {Jimp} this for chaining of methods\n     */\n\n  }, {\n    key: \"write\",\n    value: function write(path, cb) {\n      var _this2 = this;\n\n      if (!_fs[\"default\"] || !_fs[\"default\"].createWriteStream) {\n        throw new Error('Cant access the filesystem. You can use the getBase64 method.');\n      }\n\n      if (typeof path !== 'string') {\n        return _utils.throwError.call(this, 'path must be a string', cb);\n      }\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return _utils.throwError.call(this, 'cb must be a function', cb);\n      }\n\n      var mime = MIME.getType(path) || this.getMIME();\n\n      var pathObj = _path[\"default\"].parse(path);\n\n      if (pathObj.dir) {\n        _mkdirp[\"default\"].sync(pathObj.dir);\n      }\n\n      this.getBuffer(mime, function (err, buffer) {\n        if (err) {\n          return _utils.throwError.call(_this2, err, cb);\n        }\n\n        var stream = _fs[\"default\"].createWriteStream(path);\n\n        stream.on('open', function () {\n          stream.write(buffer);\n          stream.end();\n        }).on('error', function (err) {\n          return _utils.throwError.call(_this2, err, cb);\n        });\n        stream.on('finish', function () {\n          cb.call(_this2, null, _this2);\n        });\n      });\n      return this;\n    }\n  }, {\n    key: \"getBase64\",\n\n    /**\n     * Converts the image to a base 64 string\n     * @param {string} mime the mime type of the image data to be created\n     * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n     * @returns {Jimp} this for chaining of methods\n     */\n    value: function getBase64(mime, cb) {\n      if (mime === Jimp.AUTO) {\n        // allow auto MIME detection\n        mime = this.getMIME();\n      }\n\n      if (typeof mime !== 'string') {\n        return _utils.throwError.call(this, 'mime must be a string', cb);\n      }\n\n      if (typeof cb !== 'function') {\n        return _utils.throwError.call(this, 'cb must be a function', cb);\n      }\n\n      this.getBuffer(mime, function (err, data) {\n        if (err) {\n          return _utils.throwError.call(this, err, cb);\n        }\n\n        var src = 'data:' + mime + ';base64,' + data.toString('base64');\n        cb.call(this, null, src);\n      });\n      return this;\n    }\n  }, {\n    key: \"hash\",\n\n    /**\n     * Generates a perceptual hash of the image <https://en.wikipedia.org/wiki/Perceptual_hashing>. And pads the string. Can configure base.\n     * @param {number} base (optional) a number between 2 and 64 representing the base for the hash (e.g. 2 is binary, 10 is decimal, 16 is hex, 64 is base 64). Defaults to 64.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {string} a string representing the hash\n     */\n    value: function hash(base, cb) {\n      base = base || 64;\n\n      if (typeof base === 'function') {\n        cb = base;\n        base = 64;\n      }\n\n      if (typeof base !== 'number') {\n        return _utils.throwError.call(this, 'base must be a number', cb);\n      }\n\n      if (base < 2 || base > 64) {\n        return _utils.throwError.call(this, 'base must be a number between 2 and 64', cb);\n      }\n\n      var hash = this.pHash();\n      hash = (0, _anyBase[\"default\"])(_anyBase[\"default\"].BIN, alphabet.slice(0, base))(hash);\n\n      while (hash.length < maxHashLength[base]) {\n        hash = '0' + hash; // pad out with leading zeros\n      }\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, hash);\n      }\n\n      return hash;\n    }\n    /**\n     * Calculates the perceptual hash\n     * @returns {number} the perceptual hash\n     */\n\n  }, {\n    key: \"pHash\",\n    value: function pHash() {\n      var pHash = new _phash[\"default\"]();\n      return pHash.getHash(this);\n    }\n    /**\n     * Calculates the hamming distance of the current image and a hash based on their perceptual hash\n     * @param {hash} compareHash hash to compare to\n     * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n     */\n\n  }, {\n    key: \"distanceFromHash\",\n    value: function distanceFromHash(compareHash) {\n      var pHash = new _phash[\"default\"]();\n      var currentHash = pHash.getHash(this);\n      return pHash.distance(currentHash, compareHash);\n    }\n    /**\n     * Converts the image to a buffer\n     * @param {string} mime the mime type of the image buffer to be created\n     * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n     * @returns {Jimp} this for chaining of methods\n     */\n\n  }, {\n    key: \"getPixelIndex\",\n\n    /**\n     * Returns the offset of a pixel in the bitmap buffer\n     * @param {number} x the x coordinate\n     * @param {number} y the y coordinate\n     * @param {string} edgeHandling (optional) define how to sum pixels from outside the border\n     * @param {number} cb (optional) a callback for when complete\n     * @returns {number} the index of the pixel or -1 if not found\n     */\n    value: function getPixelIndex(x, y, edgeHandling, cb) {\n      var xi;\n      var yi;\n\n      if (typeof edgeHandling === 'function' && typeof cb === 'undefined') {\n        cb = edgeHandling;\n        edgeHandling = null;\n      }\n\n      if (!edgeHandling) {\n        edgeHandling = Jimp.EDGE_EXTEND;\n      }\n\n      if (typeof x !== 'number' || typeof y !== 'number') {\n        return _utils.throwError.call(this, 'x and y must be numbers', cb);\n      } // round input\n\n\n      x = Math.round(x);\n      y = Math.round(y);\n      xi = x;\n      yi = y;\n\n      if (edgeHandling === Jimp.EDGE_EXTEND) {\n        if (x < 0) xi = 0;\n        if (x >= this.bitmap.width) xi = this.bitmap.width - 1;\n        if (y < 0) yi = 0;\n        if (y >= this.bitmap.height) yi = this.bitmap.height - 1;\n      }\n\n      if (edgeHandling === Jimp.EDGE_WRAP) {\n        if (x < 0) {\n          xi = this.bitmap.width + x;\n        }\n\n        if (x >= this.bitmap.width) {\n          xi = x % this.bitmap.width;\n        }\n\n        if (y < 0) {\n          xi = this.bitmap.height + y;\n        }\n\n        if (y >= this.bitmap.height) {\n          yi = y % this.bitmap.height;\n        }\n      }\n\n      var i = this.bitmap.width * yi + xi << 2; // if out of bounds index is -1\n\n      if (xi < 0 || xi >= this.bitmap.width) {\n        i = -1;\n      }\n\n      if (yi < 0 || yi >= this.bitmap.height) {\n        i = -1;\n      }\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, i);\n      }\n\n      return i;\n    }\n    /**\n     * Returns the hex colour value of a pixel\n     * @param {number} x the x coordinate\n     * @param {number} y the y coordinate\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {number} the color of the pixel\n     */\n\n  }, {\n    key: \"getPixelColor\",\n    value: function getPixelColor(x, y, cb) {\n      if (typeof x !== 'number' || typeof y !== 'number') return _utils.throwError.call(this, 'x and y must be numbers', cb); // round input\n\n      x = Math.round(x);\n      y = Math.round(y);\n      var idx = this.getPixelIndex(x, y);\n      var hex = this.bitmap.data.readUInt32BE(idx);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, hex);\n      }\n\n      return hex;\n    }\n  }, {\n    key: \"setPixelColor\",\n\n    /**\n     * Returns the hex colour value of a pixel\n     * @param {number} hex color to set\n     * @param {number} x the x coordinate\n     * @param {number} y the y coordinate\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {number} the index of the pixel or -1 if not found\n     */\n    value: function setPixelColor(hex, x, y, cb) {\n      if (typeof hex !== 'number' || typeof x !== 'number' || typeof y !== 'number') return _utils.throwError.call(this, 'hex, x and y must be numbers', cb); // round input\n\n      x = Math.round(x);\n      y = Math.round(y);\n      var idx = this.getPixelIndex(x, y);\n      this.bitmap.data.writeUInt32BE(hex, idx);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  }, {\n    key: \"hasAlpha\",\n\n    /**\n     * Determine if the image contains opaque pixels.\n     * @return {boolean} hasAlpha whether the image contains opaque pixels\n     */\n    value: function hasAlpha() {\n      for (var yIndex = 0; yIndex < this.bitmap.height; yIndex++) {\n        for (var xIndex = 0; xIndex < this.bitmap.width; xIndex++) {\n          var idx = this.bitmap.width * yIndex + xIndex << 2;\n          var alpha = this.bitmap.data[idx + 3];\n\n          if (alpha !== 0xff) {\n            return true;\n          }\n        }\n      }\n\n      return false;\n    }\n    /**\n     * Iterate scan through a region of the bitmap\n     * @param {number} x the x coordinate to begin the scan at\n     * @param {number} y the y coordinate to begin the scan at\n     * @param w the width of the scan region\n     * @param h the height of the scan region\n     * @returns {IterableIterator<{x: number, y: number, idx: number, image: Jimp}>}\n     */\n\n  }, {\n    key: \"scanIterator\",\n    value: function scanIterator(x, y, w, h) {\n      if (typeof x !== 'number' || typeof y !== 'number') {\n        return _utils.throwError.call(this, 'x and y must be numbers');\n      }\n\n      if (typeof w !== 'number' || typeof h !== 'number') {\n        return _utils.throwError.call(this, 'w and h must be numbers');\n      }\n\n      return (0, _utils.scanIterator)(this, x, y, w, h);\n    }\n  }]);\n  return Jimp;\n}(_events[\"default\"]);\n\nfunction addConstants(constants) {\n  var jimpInstance = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Jimp;\n  Object.entries(constants).forEach(function (_ref) {\n    var _ref2 = (0, _slicedToArray2[\"default\"])(_ref, 2),\n        name = _ref2[0],\n        value = _ref2[1];\n\n    jimpInstance[name] = value;\n  });\n}\n\nfunction addJimpMethods(methods) {\n  var jimpInstance = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Jimp;\n  Object.entries(methods).forEach(function (_ref3) {\n    var _ref4 = (0, _slicedToArray2[\"default\"])(_ref3, 2),\n        name = _ref4[0],\n        value = _ref4[1];\n\n    jimpInstance.prototype[name] = value;\n  });\n}\n\naddConstants(constants);\naddJimpMethods({\n  composite: _composite[\"default\"]\n});\nJimp.__extraConstructors = [];\n/**\n * Allow client libs to add new ways to build a Jimp object.\n * @param {string} name identify the extra constructor.\n * @param {function} test a function that returns true when it accepts the arguments passed to the main constructor.\n * @param {function} run where the magic happens.\n */\n\nJimp.appendConstructorOption = function (name, test, run) {\n  Jimp.__extraConstructors.push({\n    name: name,\n    test: test,\n    run: run\n  });\n};\n/**\n * Read an image from a file or a Buffer. Takes the same args as the constructor\n * @returns {Promise} a promise\n */\n\n\nJimp.read = function () {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n\n  return new Promise(function (resolve, reject) {\n    (0, _construct2[\"default\"])(Jimp, args.concat([function (err, image) {\n      if (err) reject(err);else resolve(image);\n    }]));\n  });\n};\n\nJimp.create = Jimp.read;\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} r the red value (0-255)\n * @param {number} g the green value (0-255)\n * @param {number} b the blue value (0-255)\n * @param {number} a the alpha value (0-255)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {number} an single integer colour value\n */\n\nJimp.rgbaToInt = function (r, g, b, a, cb) {\n  if (typeof r !== 'number' || typeof g !== 'number' || typeof b !== 'number' || typeof a !== 'number') {\n    return _utils.throwError.call(this, 'r, g, b and a must be numbers', cb);\n  }\n\n  if (r < 0 || r > 255) {\n    return _utils.throwError.call(this, 'r must be between 0 and 255', cb);\n  }\n\n  if (g < 0 || g > 255) {\n    _utils.throwError.call(this, 'g must be between 0 and 255', cb);\n  }\n\n  if (b < 0 || b > 255) {\n    return _utils.throwError.call(this, 'b must be between 0 and 255', cb);\n  }\n\n  if (a < 0 || a > 255) {\n    return _utils.throwError.call(this, 'a must be between 0 and 255', cb);\n  }\n\n  r = Math.round(r);\n  b = Math.round(b);\n  g = Math.round(g);\n  a = Math.round(a);\n  var i = r * Math.pow(256, 3) + g * Math.pow(256, 2) + b * Math.pow(256, 1) + a * Math.pow(256, 0);\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, i);\n  }\n\n  return i;\n};\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} i a single integer value representing an RGBA colour (e.g. 0xFF0000FF for red)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {object} an object with the properties r, g, b and a representing RGBA values\n */\n\n\nJimp.intToRGBA = function (i, cb) {\n  if (typeof i !== 'number') {\n    return _utils.throwError.call(this, 'i must be a number', cb);\n  }\n\n  var rgba = {};\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor((i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) / Math.pow(256, 1));\n  rgba.a = Math.floor((i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2) - rgba.b * Math.pow(256, 1)) / Math.pow(256, 0));\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, rgba);\n  }\n\n  return rgba;\n};\n/**\n * Converts a css color (Hex, 8-digit (RGBA) Hex, RGB, RGBA, HSL, HSLA, HSV, HSVA, Named) to a hex number\n * @param {string} cssColor a number\n * @returns {number} a hex number representing a color\n */\n\n\nJimp.cssColorToHex = function (cssColor) {\n  cssColor = cssColor || 0; // 0, null, undefined, NaN\n\n  if (typeof cssColor === 'number') return Number(cssColor);\n  return parseInt((0, _tinycolor[\"default\"])(cssColor).toHex8(), 16);\n};\n/**\n * Limits a number to between 0 or 255\n * @param {number} n a number\n * @returns {number} the number limited to between 0 or 255\n */\n\n\nJimp.limit255 = function (n) {\n  n = Math.max(n, 0);\n  n = Math.min(n, 255);\n  return n;\n};\n/**\n * Diffs two images and returns\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @param {number} threshold (optional) a number, 0 to 1, the smaller the value the more sensitive the comparison (default: 0.1)\n * @returns {object} an object { percent: percent similar, diff: a Jimp image highlighting differences }\n */\n\n\nJimp.diff = function (img1, img2) {\n  var threshold = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.1;\n  if (!(img1 instanceof Jimp) || !(img2 instanceof Jimp)) return _utils.throwError.call(this, 'img1 and img2 must be an Jimp images');\n  var bmp1 = img1.bitmap;\n  var bmp2 = img2.bitmap;\n\n  if (bmp1.width !== bmp2.width || bmp1.height !== bmp2.height) {\n    if (bmp1.width * bmp1.height > bmp2.width * bmp2.height) {\n      // img1 is bigger\n      img1 = img1.cloneQuiet().resize(bmp2.width, bmp2.height);\n    } else {\n      // img2 is bigger (or they are the same in area)\n      img2 = img2.cloneQuiet().resize(bmp1.width, bmp1.height);\n    }\n  }\n\n  if (typeof threshold !== 'number' || threshold < 0 || threshold > 1) {\n    return _utils.throwError.call(this, 'threshold must be a number between 0 and 1');\n  }\n\n  var diff = new Jimp(bmp1.width, bmp1.height, 0xffffffff);\n  var numDiffPixels = (0, _pixelmatch[\"default\"])(bmp1.data, bmp2.data, diff.bitmap.data, diff.bitmap.width, diff.bitmap.height, {\n    threshold: threshold\n  });\n  return {\n    percent: numDiffPixels / (diff.bitmap.width * diff.bitmap.height),\n    image: diff\n  };\n};\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\n\n\nJimp.distance = function (img1, img2) {\n  var phash = new _phash[\"default\"]();\n  var hash1 = phash.getHash(img1);\n  var hash2 = phash.getHash(img2);\n  return phash.distance(hash1, hash2);\n};\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {hash} hash1 a pHash\n * @param {hash} hash2 a pHash\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\n\n\nJimp.compareHashes = function (hash1, hash2) {\n  var phash = new _phash[\"default\"]();\n  return phash.distance(hash1, hash2);\n};\n/**\n * Compute color difference\n * 0 means no difference, 1 means maximum difference.\n * @param {number} rgba1:    first color to compare.\n * @param {number} rgba2:    second color to compare.\n * Both parameters must be an color object {r:val, g:val, b:val, a:val}\n * Where `a` is optional and `val` is an integer between 0 and 255.\n * @returns {number} float between 0 and 1.\n */\n\n\nJimp.colorDiff = function (rgba1, rgba2) {\n  var pow = function pow(n) {\n    return Math.pow(n, 2);\n  };\n\n  var max = Math.max;\n  var maxVal = 255 * 255 * 3;\n\n  if (rgba1.a !== 0 && !rgba1.a) {\n    rgba1.a = 255;\n  }\n\n  if (rgba2.a !== 0 && !rgba2.a) {\n    rgba2.a = 255;\n  }\n\n  return (max(pow(rgba1.r - rgba2.r), pow(rgba1.r - rgba2.r - rgba1.a + rgba2.a)) + max(pow(rgba1.g - rgba2.g), pow(rgba1.g - rgba2.g - rgba1.a + rgba2.a)) + max(pow(rgba1.b - rgba2.b), pow(rgba1.b - rgba2.b - rgba1.a + rgba2.a))) / maxVal;\n};\n/**\n * Helper to create Jimp methods that emit events before and after its execution.\n * @param {string} methodName   The name to be appended to Jimp prototype.\n * @param {string} evName       The event name to be called.\n *                     It will be prefixed by `before-` and emitted when on method call.\n *                     It will be appended by `ed` and emitted after the method run.\n * @param {function} method       A function implementing the method itself.\n * It will also create a quiet version that will not emit events, to not\n * mess the user code with many `changed` event calls. You can call with\n * `methodName + \"Quiet\"`.\n *\n * The emitted event comes with a object parameter to the listener with the\n * `methodName` as one attribute.\n */\n\n\nfunction jimpEvMethod(methodName, evName, method) {\n  var evNameBefore = 'before-' + evName;\n  var evNameAfter = evName.replace(/e$/, '') + 'ed';\n\n  Jimp.prototype[methodName] = function () {\n    var wrappedCb;\n\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n\n    var cb = args[method.length - 1];\n    var jimpInstance = this;\n\n    if (typeof cb === 'function') {\n      wrappedCb = function wrappedCb() {\n        for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n          args[_key5] = arguments[_key5];\n        }\n\n        var err = args[0],\n            data = args[1];\n\n        if (err) {\n          jimpInstance.emitError(methodName, err);\n        } else {\n          jimpInstance.emitMulti(methodName, evNameAfter, (0, _defineProperty2[\"default\"])({}, methodName, data));\n        }\n\n        cb.apply(this, args);\n      };\n\n      args[args.length - 1] = wrappedCb;\n    } else {\n      wrappedCb = false;\n    }\n\n    this.emitMulti(methodName, evNameBefore);\n    var result;\n\n    try {\n      result = method.apply(this, args);\n\n      if (!wrappedCb) {\n        this.emitMulti(methodName, evNameAfter, (0, _defineProperty2[\"default\"])({}, methodName, result));\n      }\n    } catch (error) {\n      error.methodName = methodName;\n      this.emitError(methodName, error);\n    }\n\n    return result;\n  };\n\n  Jimp.prototype[methodName + 'Quiet'] = method;\n}\n/**\n * Creates a new image that is a clone of this one.\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns the new image\n */\n\n\njimpEvMethod('clone', 'clone', function (cb) {\n  var clone = new Jimp(this);\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(clone, null, clone);\n  }\n\n  return clone;\n});\n/**\n * Simplify jimpEvMethod call for the common `change` evName.\n * @param {string} methodName name of the method\n * @param {function} method to watch changes for\n */\n\nfunction jimpEvChange(methodName, method) {\n  jimpEvMethod(methodName, 'change', method);\n}\n/**\n * Sets the type of the image (RGB or RGBA) when saving as PNG format (default is RGBA)\n * @param b A Boolean, true to use RGBA or false to use RGB\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\n\n\njimpEvChange('background', function (hex, cb) {\n  if (typeof hex !== 'number') {\n    return _utils.throwError.call(this, 'hex must be a hexadecimal rgba value', cb);\n  }\n\n  this._background = hex;\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n});\n/**\n * Scans through a region of the bitmap, calling a function for each pixel.\n * @param {number} x the x coordinate to begin the scan at\n * @param {number} y the y coordinate to begin the scan at\n * @param w the width of the scan region\n * @param h the height of the scan region\n * @param f a function to call on even pixel; the (x, y) position of the pixel\n * and the index of the pixel in the bitmap buffer are passed to the function\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\n\njimpEvChange('scan', function (x, y, w, h, f, cb) {\n  if (typeof x !== 'number' || typeof y !== 'number') {\n    return _utils.throwError.call(this, 'x and y must be numbers', cb);\n  }\n\n  if (typeof w !== 'number' || typeof h !== 'number') {\n    return _utils.throwError.call(this, 'w and h must be numbers', cb);\n  }\n\n  if (typeof f !== 'function') {\n    return _utils.throwError.call(this, 'f must be a function', cb);\n  }\n\n  var result = (0, _utils.scan)(this, x, y, w, h, f);\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, result);\n  }\n\n  return result;\n});\n\nif (process.env.ENVIRONMENT === 'BROWSER') {\n  // For use in a web browser or web worker\n\n  /* global self */\n  var gl;\n\n  if (typeof window !== 'undefined' && (typeof window === \"undefined\" ? \"undefined\" : (0, _typeof2[\"default\"])(window)) === 'object') {\n    gl = window;\n  }\n\n  if (typeof self !== 'undefined' && (typeof self === \"undefined\" ? \"undefined\" : (0, _typeof2[\"default\"])(self)) === 'object') {\n    gl = self;\n  }\n\n  gl.Jimp = Jimp;\n  gl.Buffer = Buffer;\n}\n\nvar _default = Jimp;\nexports[\"default\"] = _default;\n//# sourceMappingURL=index.js.map", "\n\n/*\nCopyright (c) 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n// https://code.google.com/p/ironchef-team21/source/browse/ironchef_team21/src/ImagePHash.java\n\n/*\n * pHash-like image hash.\n * Author: Elliot Shepherd (<EMAIL>\n * Based On: http://www.hackerfactor.com/blog/index.php?/archives/432-Looks-Like-It.html\n */\nfunction ImagePHash(size, smallerSize) {\n  this.size = this.size || size;\n  this.smallerSize = this.smallerSize || smallerSize;\n  initCoefficients(this.size);\n}\n\nImagePHash.prototype.size = 32;\nImagePHash.prototype.smallerSize = 8;\n\nImagePHash.prototype.distance = function (s1, s2) {\n  var counter = 0;\n\n  for (var k = 0; k < s1.length; k++) {\n    if (s1[k] !== s2[k]) {\n      counter++;\n    }\n  }\n\n  return counter / s1.length;\n}; // Returns a 'binary string' (like. 001010111011100010) which is easy to do a hamming distance on.\n\n\nImagePHash.prototype.getHash = function (img) {\n  /* 1. Reduce size.\n   * Like Average Hash, pHash starts with a small image.\n   * However, the image is larger than 8x8; 32x32 is a good size.\n   * This is really done to simplify the DCT computation and not\n   * because it is needed to reduce the high frequencies.\n   */\n  img = img.clone().resize(this.size, this.size);\n  /* 2. Reduce color.\n   * The image is reduced to a grayscale just to further simplify\n   * the number of computations.\n   */\n\n  img.grayscale();\n  var vals = [];\n\n  for (var x = 0; x < img.bitmap.width; x++) {\n    vals[x] = [];\n\n    for (var y = 0; y < img.bitmap.height; y++) {\n      vals[x][y] = intToRGBA(img.getPixelColor(x, y)).b;\n    }\n  }\n  /* 3. Compute the DCT.\n   * The DCT separates the image into a collection of frequencies\n   * and scalars. While JPEG uses an 8x8 DCT, this algorithm uses\n   * a 32x32 DCT.\n   */\n\n\n  var dctVals = applyDCT(vals, this.size);\n  /* 4. Reduce the DCT.\n   * This is the magic step. While the DCT is 32x32, just keep the\n   * top-left 8x8. Those represent the lowest frequencies in the\n   * picture.\n   */\n\n  /* 5. Compute the average value.\n   * Like the Average Hash, compute the mean DCT value (using only\n   * the 8x8 DCT low-frequency values and excluding the first term\n   * since the DC coefficient can be significantly different from\n   * the other values and will throw off the average).\n   */\n\n  var total = 0;\n\n  for (var _x = 0; _x < this.smallerSize; _x++) {\n    for (var _y = 0; _y < this.smallerSize; _y++) {\n      total += dctVals[_x][_y];\n    }\n  }\n\n  var avg = total / (this.smallerSize * this.smallerSize);\n  /* 6. Further reduce the DCT.\n   * This is the magic step. Set the 64 hash bits to 0 or 1\n   * depending on whether each of the 64 DCT values is above or\n   * below the average value. The result doesn't tell us the\n   * actual low frequencies; it just tells us the very-rough\n   * relative scale of the frequencies to the mean. The result\n   * will not vary as long as the overall structure of the image\n   * remains the same; this can survive gamma and color histogram\n   * adjustments without a problem.\n   */\n\n  var hash = '';\n\n  for (var _x2 = 0; _x2 < this.smallerSize; _x2++) {\n    for (var _y2 = 0; _y2 < this.smallerSize; _y2++) {\n      hash += dctVals[_x2][_y2] > avg ? '1' : '0';\n    }\n  }\n\n  return hash;\n}; // DCT function stolen from http://stackoverflow.com/questions/4240490/problems-with-dct-and-idct-algorithm-in-java\n\n\nfunction intToRGBA(i) {\n  var rgba = {};\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor((i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) / Math.pow(256, 1));\n  rgba.a = Math.floor((i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2) - rgba.b * Math.pow(256, 1)) / Math.pow(256, 0));\n  return rgba;\n}\n\nvar c = [];\n\nfunction initCoefficients(size) {\n  for (var i = 1; i < size; i++) {\n    c[i] = 1;\n  }\n\n  c[0] = 1 / Math.sqrt(2.0);\n}\n\nfunction applyDCT(f, size) {\n  var N = size;\n  var F = [];\n\n  for (var u = 0; u < N; u++) {\n    F[u] = [];\n\n    for (var v = 0; v < N; v++) {\n      var sum = 0;\n\n      for (var i = 0; i < N; i++) {\n        for (var j = 0; j < N; j++) {\n          sum += Math.cos((2 * i + 1) / (2.0 * N) * u * Math.PI) * Math.cos((2 * j + 1) / (2.0 * N) * v * Math.PI) * f[i][j];\n        }\n      }\n\n      sum *= c[u] * c[v] / 4;\n      F[u][v] = sum;\n    }\n  }\n\n  return F;\n}\n\nmodule.exports = ImagePHash;\n//# sourceMappingURL=phash.js.map", "\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\n\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { (0, _defineProperty2[\"default\"])(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\n/* global XMLHttpRequest */\nif (process.browser || process.env.ENVIRONMENT === 'BROWSER' || typeof process.versions.electron !== 'undefined' && process.type === 'renderer' && typeof XMLHttpRequest === 'function') {\n  // If we run into a browser or the electron renderer process,\n  // use XHR method instead of Request node module.\n  module.exports = function (options, cb) {\n    var xhr = new XMLHttpRequest();\n    xhr.open('GET', options.url, true);\n    xhr.responseType = 'arraybuffer';\n    xhr.addEventListener('load', function () {\n      if (xhr.status < 400) {\n        try {\n          var data = Buffer.from(this.response);\n          cb(null, xhr, data);\n        } catch (error) {\n          return cb(new Error('Response is not a buffer for url ' + options.url + '. Error: ' + error.message));\n        }\n      } else {\n        cb(new Error('HTTP Status ' + xhr.status + ' for url ' + options.url));\n      }\n    });\n    xhr.addEventListener('error', function (e) {\n      cb(e);\n    });\n    xhr.send();\n  };\n} else {\n  module.exports = function (_ref, cb) {\n    var options = (0, _extends2[\"default\"])({}, _ref);\n\n    var p = require('phin');\n\n    p(_objectSpread({\n      compression: true\n    }, options), function (err, res) {\n      if (err === null) {\n        cb(null, res, res.body);\n      } else {\n        cb(err);\n      }\n    });\n  };\n}\n//# sourceMappingURL=request.js.map", "\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = composite;\n\nvar _utils = require(\"@jimp/utils\");\n\nvar constants = _interopRequireWildcard(require(\"../constants\"));\n\nvar compositeModes = _interopRequireWildcard(require(\"./composite-modes\"));\n\n/**\n * Composites a source image over to this image respecting alpha channels\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the x position to blit the image\n * @param {number} y the y position to blit the image\n * @param {object} options determine what mode to use\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction composite(src, x, y) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var cb = arguments.length > 4 ? arguments[4] : undefined;\n\n  if (typeof options === 'function') {\n    cb = options;\n    options = {};\n  }\n\n  if (!(src instanceof this.constructor)) {\n    return _utils.throwError.call(this, 'The source must be a Jimp image', cb);\n  }\n\n  if (typeof x !== 'number' || typeof y !== 'number') {\n    return _utils.throwError.call(this, 'x and y must be numbers', cb);\n  }\n\n  var _options = options,\n      mode = _options.mode,\n      opacitySource = _options.opacitySource,\n      opacityDest = _options.opacityDest;\n\n  if (!mode) {\n    mode = constants.BLEND_SOURCE_OVER;\n  }\n\n  if (typeof opacitySource !== 'number' || opacitySource < 0 || opacitySource > 1) {\n    opacitySource = 1.0;\n  }\n\n  if (typeof opacityDest !== 'number' || opacityDest < 0 || opacityDest > 1) {\n    opacityDest = 1.0;\n  }\n\n  var blendmode = compositeModes[mode]; // round input\n\n  x = Math.round(x);\n  y = Math.round(y);\n  var baseImage = this;\n\n  if (opacityDest !== 1.0) {\n    baseImage.opacity(opacityDest);\n  }\n\n  src.scanQuiet(0, 0, src.bitmap.width, src.bitmap.height, function (sx, sy, idx) {\n    var dstIdx = baseImage.getPixelIndex(x + sx, y + sy, constants.EDGE_CROP);\n    var blended = blendmode({\n      r: this.bitmap.data[idx + 0] / 255,\n      g: this.bitmap.data[idx + 1] / 255,\n      b: this.bitmap.data[idx + 2] / 255,\n      a: this.bitmap.data[idx + 3] / 255\n    }, {\n      r: baseImage.bitmap.data[dstIdx + 0] / 255,\n      g: baseImage.bitmap.data[dstIdx + 1] / 255,\n      b: baseImage.bitmap.data[dstIdx + 2] / 255,\n      a: baseImage.bitmap.data[dstIdx + 3] / 255\n    }, opacitySource);\n    baseImage.bitmap.data[dstIdx + 0] = this.constructor.limit255(blended.r * 255);\n    baseImage.bitmap.data[dstIdx + 1] = this.constructor.limit255(blended.g * 255);\n    baseImage.bitmap.data[dstIdx + 2] = this.constructor.limit255(blended.b * 255);\n    baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(blended.a * 255);\n  });\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.EDGE_CROP = exports.EDGE_WRAP = exports.EDGE_EXTEND = exports.BLEND_EXCLUSION = exports.BLEND_DIFFERENCE = exports.BLEND_HARDLIGHT = exports.BLEND_LIGHTEN = exports.BLEND_DARKEN = exports.BLEND_OVERLAY = exports.BLEND_SCREEN = exports.BLEND_ADD = exports.BLEND_MULTIPLY = exports.BLEND_DESTINATION_OVER = exports.BLEND_SOURCE_OVER = exports.VERTICAL_ALIGN_BOTTOM = exports.VERTICAL_ALIGN_MIDDLE = exports.VERTICAL_ALIGN_TOP = exports.HORIZONTAL_ALIGN_RIGHT = exports.HORIZONTAL_ALIGN_CENTER = exports.HORIZONTAL_ALIGN_LEFT = exports.AUTO = void 0;\n// used to auto resizing etc.\nvar AUTO = -1; // Align modes for cover, contain, bit masks\n\nexports.AUTO = AUTO;\nvar HORIZONTAL_ALIGN_LEFT = 1;\nexports.HORIZONTAL_ALIGN_LEFT = HORIZONTAL_ALIGN_LEFT;\nvar HORIZONTAL_ALIGN_CENTER = 2;\nexports.HORIZONTAL_ALIGN_CENTER = HORIZONTAL_ALIGN_CENTER;\nvar HORIZONTAL_ALIGN_RIGHT = 4;\nexports.HORIZONTAL_ALIGN_RIGHT = HORIZONTAL_ALIGN_RIGHT;\nvar VERTICAL_ALIGN_TOP = 8;\nexports.VERTICAL_ALIGN_TOP = VERTICAL_ALIGN_TOP;\nvar VERTICAL_ALIGN_MIDDLE = 16;\nexports.VERTICAL_ALIGN_MIDDLE = VERTICAL_ALIGN_MIDDLE;\nvar VERTICAL_ALIGN_BOTTOM = 32; // blend modes\n\nexports.VERTICAL_ALIGN_BOTTOM = VERTICAL_ALIGN_BOTTOM;\nvar BLEND_SOURCE_OVER = 'srcOver';\nexports.BLEND_SOURCE_OVER = BLEND_SOURCE_OVER;\nvar BLEND_DESTINATION_OVER = 'dstOver';\nexports.BLEND_DESTINATION_OVER = BLEND_DESTINATION_OVER;\nvar BLEND_MULTIPLY = 'multiply';\nexports.BLEND_MULTIPLY = BLEND_MULTIPLY;\nvar BLEND_ADD = 'add';\nexports.BLEND_ADD = BLEND_ADD;\nvar BLEND_SCREEN = 'screen';\nexports.BLEND_SCREEN = BLEND_SCREEN;\nvar BLEND_OVERLAY = 'overlay';\nexports.BLEND_OVERLAY = BLEND_OVERLAY;\nvar BLEND_DARKEN = 'darken';\nexports.BLEND_DARKEN = BLEND_DARKEN;\nvar BLEND_LIGHTEN = 'lighten';\nexports.BLEND_LIGHTEN = BLEND_LIGHTEN;\nvar BLEND_HARDLIGHT = 'hardLight';\nexports.BLEND_HARDLIGHT = BLEND_HARDLIGHT;\nvar BLEND_DIFFERENCE = 'difference';\nexports.BLEND_DIFFERENCE = BLEND_DIFFERENCE;\nvar BLEND_EXCLUSION = 'exclusion'; // Edge Handling\n\nexports.BLEND_EXCLUSION = BLEND_EXCLUSION;\nvar EDGE_EXTEND = 1;\nexports.EDGE_EXTEND = EDGE_EXTEND;\nvar EDGE_WRAP = 2;\nexports.EDGE_WRAP = EDGE_WRAP;\nvar EDGE_CROP = 3;\nexports.EDGE_CROP = EDGE_CROP;\n//# sourceMappingURL=constants.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.srcOver = srcOver;\nexports.dstOver = dstOver;\nexports.multiply = multiply;\nexports.add = add;\nexports.screen = screen;\nexports.overlay = overlay;\nexports.darken = darken;\nexports.lighten = lighten;\nexports.hardLight = hardLight;\nexports.difference = difference;\nexports.exclusion = exclusion;\n\nfunction srcOver(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var r = (src.r * src.a + dst.r * dst.a * (1 - src.a)) / a;\n  var g = (src.g * src.a + dst.g * dst.a * (1 - src.a)) / a;\n  var b = (src.b * src.a + dst.b * dst.a * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction dstOver(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var r = (dst.r * dst.a + src.r * src.a * (1 - dst.a)) / a;\n  var g = (dst.g * dst.a + src.g * src.a * (1 - dst.a)) / a;\n  var b = (dst.b * dst.a + src.b * src.a * (1 - dst.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction multiply(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  var g = (sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  var b = (sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction add(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (sra + dra) / a;\n  var g = (sga + dga) / a;\n  var b = (sba + dba) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction screen(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (sra * dst.a + dra * src.a - sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  var g = (sga * dst.a + dga * src.a - sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  var b = (sba * dst.a + dba * src.a - sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction overlay(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (2 * dra <= dst.a ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a) : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) / a;\n  var g = (2 * dga <= dst.a ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a) : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) / a;\n  var b = (2 * dba <= dst.a ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a) : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction darken(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (Math.min(sra * dst.a, dra * src.a) + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  var g = (Math.min(sga * dst.a, dga * src.a) + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  var b = (Math.min(sba * dst.a, dba * src.a) + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction lighten(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (Math.max(sra * dst.a, dra * src.a) + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  var g = (Math.max(sga * dst.a, dga * src.a) + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  var b = (Math.max(sba * dst.a, dba * src.a) + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction hardLight(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (2 * sra <= src.a ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a) : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) / a;\n  var g = (2 * sga <= src.a ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a) : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) / a;\n  var b = (2 * sba <= src.a ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a) : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction difference(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (sra + dra - 2 * Math.min(sra * dst.a, dra * src.a)) / a;\n  var g = (sga + dga - 2 * Math.min(sga * dst.a, dga * src.a)) / a;\n  var b = (sba + dba - 2 * Math.min(sba * dst.a, dba * src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction exclusion(src, dst) {\n  var ops = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  src.a *= ops;\n  var a = dst.a + src.a - dst.a * src.a;\n  var sra = src.r * src.a;\n  var sga = src.g * src.a;\n  var sba = src.b * src.a;\n  var dra = dst.r * dst.a;\n  var dga = dst.g * dst.a;\n  var dba = dst.b * dst.a;\n  var r = (sra * dst.a + dra * src.a - 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  var g = (sga * dst.a + dga * src.a - 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  var b = (sba * dst.a + dba * src.a - 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n//# sourceMappingURL=composite-modes.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar promisify = function promisify(fun, ctx) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n\n  return new Promise(function (resolve, reject) {\n    args.push(function (err, data) {\n      if (err) {\n        reject(err);\n      }\n\n      resolve(data);\n    });\n    fun.bind(ctx).apply(void 0, args);\n  });\n};\n\nvar _default = promisify;\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=promisify.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getExtension = exports.getType = exports.addType = void 0;\nvar mimeTypes = {};\n\nvar findType = function findType(extension) {\n  return Object.entries(mimeTypes).find(function (type) {\n    return type[1].includes(extension);\n  }) || [];\n};\n\nvar addType = function addType(mime, extensions) {\n  mimeTypes[mime] = extensions;\n};\n/**\n * Lookup a mime type based on extension\n * @param {string} path path to find extension for\n * @returns {string} mime found mime type\n */\n\n\nexports.addType = addType;\n\nvar getType = function getType(path) {\n  var pathParts = path.split('/').slice(-1);\n  var extension = pathParts[pathParts.length - 1].split('.').pop();\n  var type = findType(extension);\n  return type[0];\n};\n/**\n * Return file extension associated with a mime type\n * @param {string} type mime type to look up\n * @returns {string} extension file extension\n */\n\n\nexports.getType = getType;\n\nvar getExtension = function getExtension(type) {\n  return (mimeTypes[type.toLowerCase()] || [])[0];\n};\n\nexports.getExtension = getExtension;\n//# sourceMappingURL=mime.js.map", "\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\");\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.parseBitmap = parseBitmap;\nexports.getBuffer = getBuffer;\nexports.getBufferAsync = getBufferAsync;\n\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\n\nvar _fileType = _interopRequireDefault(require(\"file-type\"));\n\nvar _exifParser = _interopRequireDefault(require(\"exif-parser\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar constants = _interopRequireWildcard(require(\"../constants\"));\n\nvar MIME = _interopRequireWildcard(require(\"./mime\"));\n\nvar _promisify = _interopRequireDefault(require(\"./promisify\"));\n\nfunction getMIMEFromBuffer(buffer, path) {\n  var fileTypeFromBuffer = (0, _fileType[\"default\"])(buffer);\n\n  if (fileTypeFromBuffer) {\n    // If fileType returns something for buffer, then return the mime given\n    return fileTypeFromBuffer.mime;\n  }\n\n  if (path) {\n    // If a path is supplied, and fileType yields no results, then retry with MIME\n    // Path can be either a file path or a url\n    return MIME.getType(path);\n  }\n\n  return null;\n}\n/*\n * Obtains image orientation from EXIF metadata.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {number} a number 1-8 representing EXIF orientation,\n *          in particular 1 if orientation tag is missing\n */\n\n\nfunction getExifOrientation(img) {\n  return img._exif && img._exif.tags && img._exif.tags.Orientation || 1;\n}\n/**\n * Returns a function which translates EXIF-rotated coordinates into\n * non-rotated ones.\n *\n * Transformation reference: http://sylvana.net/jpegcrop/exif_orientation.html.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {function} transformation function for transformBitmap().\n */\n\n\nfunction getExifOrientationTransformation(img) {\n  var w = img.getWidth();\n  var h = img.getHeight();\n\n  switch (getExifOrientation(img)) {\n    case 1:\n      // Horizontal (normal)\n      // does not need to be supported here\n      return null;\n\n    case 2:\n      // Mirror horizontal\n      return function (x, y) {\n        return [w - x - 1, y];\n      };\n\n    case 3:\n      // Rotate 180\n      return function (x, y) {\n        return [w - x - 1, h - y - 1];\n      };\n\n    case 4:\n      // Mirror vertical\n      return function (x, y) {\n        return [x, h - y - 1];\n      };\n\n    case 5:\n      // Mirror horizontal and rotate 270 CW\n      return function (x, y) {\n        return [y, x];\n      };\n\n    case 6:\n      // Rotate 90 CW\n      return function (x, y) {\n        return [y, h - x - 1];\n      };\n\n    case 7:\n      // Mirror horizontal and rotate 90 CW\n      return function (x, y) {\n        return [w - y - 1, h - x - 1];\n      };\n\n    case 8:\n      // Rotate 270 CW\n      return function (x, y) {\n        return [w - y - 1, x];\n      };\n\n    default:\n      return null;\n  }\n}\n/*\n * Transforms bitmap in place (moves pixels around) according to given\n * transformation function.\n *\n * @param img {Jimp} a Jimp image object, which bitmap is supposed to\n *        be transformed\n * @param width {number} bitmap width after the transformation\n * @param height {number} bitmap height after the transformation\n * @param transformation {function} transformation function which defines pixel\n *        mapping between new and source bitmap. It takes a pair of coordinates\n *        in the target, and returns a respective pair of coordinates in\n *        the source bitmap, i.e. has following form:\n *        `function(new_x, new_y) { return [src_x, src_y] }`.\n */\n\n\nfunction transformBitmap(img, width, height, transformation) {\n  // Underscore-prefixed values are related to the source bitmap\n  // Their counterparts with no prefix are related to the target bitmap\n  var _data = img.bitmap.data;\n  var _width = img.bitmap.width;\n  var data = Buffer.alloc(_data.length);\n\n  for (var x = 0; x < width; x++) {\n    for (var y = 0; y < height; y++) {\n      var _transformation = transformation(x, y),\n          _transformation2 = (0, _slicedToArray2[\"default\"])(_transformation, 2),\n          _x = _transformation2[0],\n          _y = _transformation2[1];\n\n      var idx = width * y + x << 2;\n\n      var _idx = _width * _y + _x << 2;\n\n      var pixel = _data.readUInt32BE(_idx);\n\n      data.writeUInt32BE(pixel, idx);\n    }\n  }\n\n  img.bitmap.data = data;\n  img.bitmap.width = width;\n  img.bitmap.height = height;\n}\n/*\n * Automagically rotates an image based on its EXIF data (if present).\n * @param img {Jimp} a Jimp image object\n */\n\n\nfunction exifRotate(img) {\n  if (getExifOrientation(img) < 2) return;\n  var transformation = getExifOrientationTransformation(img);\n  var swapDimensions = getExifOrientation(img) > 4;\n  var newWidth = swapDimensions ? img.bitmap.height : img.bitmap.width;\n  var newHeight = swapDimensions ? img.bitmap.width : img.bitmap.height;\n  transformBitmap(img, newWidth, newHeight, transformation);\n} // parses a bitmap from the constructor to the JIMP bitmap property\n\n\nfunction parseBitmap(data, path, cb) {\n  var mime = getMIMEFromBuffer(data, path);\n\n  if (typeof mime !== 'string') {\n    return cb(new Error('Could not find MIME for Buffer <' + path + '>'));\n  }\n\n  this._originalMime = mime.toLowerCase();\n\n  try {\n    var _mime = this.getMIME();\n\n    if (this.constructor.decoders[_mime]) {\n      this.bitmap = this.constructor.decoders[_mime](data);\n    } else {\n      return _utils.throwError.call(this, 'Unsupported MIME type: ' + _mime, cb);\n    }\n  } catch (error) {\n    return cb.call(this, error, this);\n  }\n\n  try {\n    this._exif = _exifParser[\"default\"].create(data).parse();\n    exifRotate(this); // EXIF data\n  } catch (error) {\n    /* meh */\n  }\n\n  cb.call(this, null, this);\n  return this;\n}\n\nfunction compositeBitmapOverBackground(Jimp, image) {\n  return new Jimp(image.bitmap.width, image.bitmap.height, image._background).composite(image, 0, 0).bitmap;\n}\n/**\n * Converts the image to a buffer\n * @param {string} mime the mime type of the image buffer to be created\n * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n * @returns {Jimp} this for chaining of methods\n */\n\n\nfunction getBuffer(mime, cb) {\n  if (mime === constants.AUTO) {\n    // allow auto MIME detection\n    mime = this.getMIME();\n  }\n\n  if (typeof mime !== 'string') {\n    return _utils.throwError.call(this, 'mime must be a string', cb);\n  }\n\n  if (typeof cb !== 'function') {\n    return _utils.throwError.call(this, 'cb must be a function', cb);\n  }\n\n  mime = mime.toLowerCase();\n\n  if (this._rgba && this.constructor.hasAlpha[mime]) {\n    this.bitmap.data = Buffer.from(this.bitmap.data);\n  } else {\n    // when format doesn't support alpha\n    // composite onto a new image so that the background shows through alpha channels\n    this.bitmap.data = compositeBitmapOverBackground(this.constructor, this).data;\n  }\n\n  if (this.constructor.encoders[mime]) {\n    var buffer = this.constructor.encoders[mime](this);\n    cb.call(this, null, buffer);\n  } else {\n    cb.call(this, 'Unsupported MIME type: ' + mime);\n  }\n\n  return this;\n}\n\nfunction getBufferAsync(mime) {\n  return (0, _promisify[\"default\"])(getBuffer, this, mime);\n}\n//# sourceMappingURL=image-bitmap.js.map"]}