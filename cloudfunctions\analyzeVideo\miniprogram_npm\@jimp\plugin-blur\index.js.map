{"version": 3, "sources": ["index.js", "blur-tables.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _blurTables = require(\"./blur-tables\");\n\n/*\n    Superfast Blur (0.5)\n    http://www.quasimondo.com/BoxBlurForCanvas/FastBlur.js\n\n    Copyright (c) 2011 <PERSON>\n\n    Permission is hereby granted, free of charge, to any person\n    obtaining a copy of this software and associated documentation\n    files (the \"Software\"), to deal in the Software without\n    restriction, including without limitation the rights to use,\n    copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the\n    Software is furnished to do so, subject to the following\n    conditions:\n\n    The above copyright notice and this permission notice shall be\n    included in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n    OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n    HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n    WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n    OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar _default = function _default() {\n  return {\n    /**\n     * A fast blur algorithm that produces similar effect to a Gaussian blur - but MUCH quicker\n     * @param {number} r the pixel radius of the blur\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    blur: function blur(r, cb) {\n      if (typeof r !== 'number') return _utils.throwError.call(this, 'r must be a number', cb);\n      if (r < 1) return _utils.throwError.call(this, 'r must be greater than 0', cb);\n      var rsum;\n      var gsum;\n      var bsum;\n      var asum;\n      var x;\n      var y;\n      var i;\n      var p;\n      var p1;\n      var p2;\n      var yp;\n      var yi;\n      var yw;\n      var pa;\n      var wm = this.bitmap.width - 1;\n      var hm = this.bitmap.height - 1; // const wh = this.bitmap.width * this.bitmap.height;\n\n      var rad1 = r + 1;\n      var mulSum = _blurTables.mulTable[r];\n      var shgSum = _blurTables.shgTable[r];\n      var red = [];\n      var green = [];\n      var blue = [];\n      var alpha = [];\n      var vmin = [];\n      var vmax = [];\n      var iterations = 2;\n\n      while (iterations-- > 0) {\n        yi = 0;\n        yw = 0;\n\n        for (y = 0; y < this.bitmap.height; y++) {\n          rsum = this.bitmap.data[yw] * rad1;\n          gsum = this.bitmap.data[yw + 1] * rad1;\n          bsum = this.bitmap.data[yw + 2] * rad1;\n          asum = this.bitmap.data[yw + 3] * rad1;\n\n          for (i = 1; i <= r; i++) {\n            p = yw + ((i > wm ? wm : i) << 2);\n            rsum += this.bitmap.data[p++];\n            gsum += this.bitmap.data[p++];\n            bsum += this.bitmap.data[p++];\n            asum += this.bitmap.data[p];\n          }\n\n          for (x = 0; x < this.bitmap.width; x++) {\n            red[yi] = rsum;\n            green[yi] = gsum;\n            blue[yi] = bsum;\n            alpha[yi] = asum;\n\n            if (y === 0) {\n              vmin[x] = ((p = x + rad1) < wm ? p : wm) << 2;\n              vmax[x] = (p = x - r) > 0 ? p << 2 : 0;\n            }\n\n            p1 = yw + vmin[x];\n            p2 = yw + vmax[x];\n            rsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n            gsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n            bsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n            asum += this.bitmap.data[p1] - this.bitmap.data[p2];\n            yi++;\n          }\n\n          yw += this.bitmap.width << 2;\n        }\n\n        for (x = 0; x < this.bitmap.width; x++) {\n          yp = x;\n          rsum = red[yp] * rad1;\n          gsum = green[yp] * rad1;\n          bsum = blue[yp] * rad1;\n          asum = alpha[yp] * rad1;\n\n          for (i = 1; i <= r; i++) {\n            yp += i > hm ? 0 : this.bitmap.width;\n            rsum += red[yp];\n            gsum += green[yp];\n            bsum += blue[yp];\n            asum += alpha[yp];\n          }\n\n          yi = x << 2;\n\n          for (y = 0; y < this.bitmap.height; y++) {\n            pa = asum * mulSum >>> shgSum;\n            this.bitmap.data[yi + 3] = pa; // normalize alpha\n\n            if (pa > 255) {\n              this.bitmap.data[yi + 3] = 255;\n            }\n\n            if (pa > 0) {\n              pa = 255 / pa;\n              this.bitmap.data[yi] = (rsum * mulSum >>> shgSum) * pa;\n              this.bitmap.data[yi + 1] = (gsum * mulSum >>> shgSum) * pa;\n              this.bitmap.data[yi + 2] = (bsum * mulSum >>> shgSum) * pa;\n            } else {\n              this.bitmap.data[yi + 2] = 0;\n              this.bitmap.data[yi + 1] = 0;\n              this.bitmap.data[yi] = 0;\n            }\n\n            if (x === 0) {\n              vmin[y] = ((p = y + rad1) < hm ? p : hm) * this.bitmap.width;\n              vmax[y] = (p = y - r) > 0 ? p * this.bitmap.width : 0;\n            }\n\n            p1 = x + vmin[y];\n            p2 = x + vmax[y];\n            rsum += red[p1] - red[p2];\n            gsum += green[p1] - green[p2];\n            bsum += blue[p1] - blue[p2];\n            asum += alpha[p1] - alpha[p2];\n            yi += this.bitmap.width << 2;\n          }\n        }\n      }\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map", "\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.shgTable = exports.mulTable = void 0;\nvar mulTable = [1, 57, 41, 21, 203, 34, 97, 73, 227, 91, 149, 62, 105, 45, 39, 137, 241, 107, 3, 173, 39, 71, 65, 238, 219, 101, 187, 87, 81, 151, 141, 133, 249, 117, 221, 209, 197, 187, 177, 169, 5, 153, 73, 139, 133, 127, 243, 233, 223, 107, 103, 99, 191, 23, 177, 171, 165, 159, 77, 149, 9, 139, 135, 131, 253, 245, 119, 231, 224, 109, 211, 103, 25, 195, 189, 23, 45, 175, 171, 83, 81, 79, 155, 151, 147, 9, 141, 137, 67, 131, 129, 251, 123, 30, 235, 115, 113, 221, 217, 53, 13, 51, 50, 49, 193, 189, 185, 91, 179, 175, 43, 169, 83, 163, 5, 79, 155, 19, 75, 147, 145, 143, 35, 69, 17, 67, 33, 65, 255, 251, 247, 243, 239, 59, 29, 229, 113, 111, 219, 27, 213, 105, 207, 51, 201, 199, 49, 193, 191, 47, 93, 183, 181, 179, 11, 87, 43, 85, 167, 165, 163, 161, 159, 157, 155, 77, 19, 75, 37, 73, 145, 143, 141, 35, 138, 137, 135, 67, 33, 131, 129, 255, 63, 250, 247, 61, 121, 239, 237, 117, 29, 229, 227, 225, 111, 55, 109, 216, 213, 211, 209, 207, 205, 203, 201, 199, 197, 195, 193, 48, 190, 47, 93, 185, 183, 181, 179, 178, 176, 175, 173, 171, 85, 21, 167, 165, 41, 163, 161, 5, 79, 157, 78, 154, 153, 19, 75, 149, 74, 147, 73, 144, 143, 71, 141, 140, 139, 137, 17, 135, 134, 133, 66, 131, 65, 129, 1];\nexports.mulTable = mulTable;\nvar shgTable = [0, 9, 10, 10, 14, 12, 14, 14, 16, 15, 16, 15, 16, 15, 15, 17, 18, 17, 12, 18, 16, 17, 17, 19, 19, 18, 19, 18, 18, 19, 19, 19, 20, 19, 20, 20, 20, 20, 20, 20, 15, 20, 19, 20, 20, 20, 21, 21, 21, 20, 20, 20, 21, 18, 21, 21, 21, 21, 20, 21, 17, 21, 21, 21, 22, 22, 21, 22, 22, 21, 22, 21, 19, 22, 22, 19, 20, 22, 22, 21, 21, 21, 22, 22, 22, 18, 22, 22, 21, 22, 22, 23, 22, 20, 23, 22, 22, 23, 23, 21, 19, 21, 21, 21, 23, 23, 23, 22, 23, 23, 21, 23, 22, 23, 18, 22, 23, 20, 22, 23, 23, 23, 21, 22, 20, 22, 21, 22, 24, 24, 24, 24, 24, 22, 21, 24, 23, 23, 24, 21, 24, 23, 24, 22, 24, 24, 22, 24, 24, 22, 23, 24, 24, 24, 20, 23, 22, 23, 24, 24, 24, 24, 24, 24, 24, 23, 21, 23, 22, 23, 24, 24, 24, 22, 24, 24, 24, 23, 22, 24, 24, 25, 23, 25, 25, 23, 24, 25, 25, 24, 22, 25, 25, 25, 24, 23, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 23, 25, 23, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 24, 22, 25, 25, 23, 25, 25, 20, 24, 25, 24, 25, 25, 22, 24, 25, 24, 25, 24, 25, 25, 24, 25, 25, 25, 25, 22, 25, 25, 25, 24, 25, 24, 25, 18];\nexports.shgTable = shgTable;\n//# sourceMappingURL=blur-tables.js.map"]}