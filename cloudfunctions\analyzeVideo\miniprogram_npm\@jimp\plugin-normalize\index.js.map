{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Get an image's histogram\n * @return {object} An object with an array of color occurrence counts for each channel (r,g,b)\n */\nfunction histogram() {\n  var histogram = {\n    r: new Array(256).fill(0),\n    g: new Array(256).fill(0),\n    b: new Array(256).fill(0)\n  };\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, index) {\n    histogram.r[this.bitmap.data[index + 0]]++;\n    histogram.g[this.bitmap.data[index + 1]]++;\n    histogram.b[this.bitmap.data[index + 2]]++;\n  });\n  return histogram;\n}\n/**\n * Normalize values\n * @param  {integer} value Pixel channel value.\n * @param  {integer} min   Minimum value for channel\n * @param  {integer} max   Maximum value for channel\n * @return {integer} normalized values\n */\n\n\nvar _normalize = function normalize(value, min, max) {\n  return (value - min) * 255 / (max - min);\n};\n\nvar getBounds = function getBounds(histogramChannel) {\n  return [histogramChannel.findIndex(function (value) {\n    return value > 0;\n  }), 255 - histogramChannel.slice().reverse().findIndex(function (value) {\n    return value > 0;\n  })];\n};\n/**\n * Normalizes the image\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\n\n\nvar _default = function _default() {\n  return {\n    normalize: function normalize(cb) {\n      var h = histogram.call(this); // store bounds (minimum and maximum values)\n\n      var bounds = {\n        r: getBounds(h.r),\n        g: getBounds(h.g),\n        b: getBounds(h.b)\n      }; // apply value transformations\n\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var r = this.bitmap.data[idx + 0];\n        var g = this.bitmap.data[idx + 1];\n        var b = this.bitmap.data[idx + 2];\n        this.bitmap.data[idx + 0] = _normalize(r, bounds.r[0], bounds.r[1]);\n        this.bitmap.data[idx + 1] = _normalize(g, bounds.g[0], bounds.g[1]);\n        this.bitmap.data[idx + 2] = _normalize(b, bounds.b[0], bounds.b[1]);\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}