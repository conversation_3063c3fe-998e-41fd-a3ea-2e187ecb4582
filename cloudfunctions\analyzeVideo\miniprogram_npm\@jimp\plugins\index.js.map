{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _timm = require(\"timm\");\n\nvar _pluginBlit = _interopRequireDefault(require(\"@jimp/plugin-blit\"));\n\nvar _pluginBlur = _interopRequireDefault(require(\"@jimp/plugin-blur\"));\n\nvar _pluginCircle = _interopRequireDefault(require(\"@jimp/plugin-circle\"));\n\nvar _pluginColor = _interopRequireDefault(require(\"@jimp/plugin-color\"));\n\nvar _pluginContain = _interopRequireDefault(require(\"@jimp/plugin-contain\"));\n\nvar _pluginCover = _interopRequireDefault(require(\"@jimp/plugin-cover\"));\n\nvar _pluginCrop = _interopRequireDefault(require(\"@jimp/plugin-crop\"));\n\nvar _pluginDisplace = _interopRequireDefault(require(\"@jimp/plugin-displace\"));\n\nvar _pluginDither = _interopRequireDefault(require(\"@jimp/plugin-dither\"));\n\nvar _pluginFisheye = _interopRequireDefault(require(\"@jimp/plugin-fisheye\"));\n\nvar _pluginFlip = _interopRequireDefault(require(\"@jimp/plugin-flip\"));\n\nvar _pluginGaussian = _interopRequireDefault(require(\"@jimp/plugin-gaussian\"));\n\nvar _pluginInvert = _interopRequireDefault(require(\"@jimp/plugin-invert\"));\n\nvar _pluginMask = _interopRequireDefault(require(\"@jimp/plugin-mask\"));\n\nvar _pluginNormalize = _interopRequireDefault(require(\"@jimp/plugin-normalize\"));\n\nvar _pluginPrint = _interopRequireDefault(require(\"@jimp/plugin-print\"));\n\nvar _pluginResize = _interopRequireDefault(require(\"@jimp/plugin-resize\"));\n\nvar _pluginRotate = _interopRequireDefault(require(\"@jimp/plugin-rotate\"));\n\nvar _pluginScale = _interopRequireDefault(require(\"@jimp/plugin-scale\"));\n\nvar _pluginShadow = _interopRequireDefault(require(\"@jimp/plugin-shadow\"));\n\nvar _pluginThreshold = _interopRequireDefault(require(\"@jimp/plugin-threshold\"));\n\nvar plugins = [_pluginBlit[\"default\"], _pluginBlur[\"default\"], _pluginCircle[\"default\"], _pluginColor[\"default\"], _pluginContain[\"default\"], _pluginCover[\"default\"], _pluginCrop[\"default\"], _pluginDisplace[\"default\"], _pluginDither[\"default\"], _pluginFisheye[\"default\"], _pluginFlip[\"default\"], _pluginGaussian[\"default\"], _pluginInvert[\"default\"], _pluginMask[\"default\"], _pluginNormalize[\"default\"], _pluginPrint[\"default\"], _pluginResize[\"default\"], _pluginRotate[\"default\"], _pluginScale[\"default\"], _pluginShadow[\"default\"], _pluginThreshold[\"default\"]];\n\nvar _default = function _default(jimpEvChange) {\n  var initializedPlugins = plugins.map(function (pluginModule) {\n    var plugin = pluginModule(jimpEvChange) || {};\n\n    if (!plugin[\"class\"] && !plugin.constants) {\n      // Default to class function\n      plugin = {\n        \"class\": plugin\n      };\n    }\n\n    return plugin;\n  });\n  return _timm.mergeDeep.apply(void 0, (0, _toConsumableArray2[\"default\"])(initializedPlugins));\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}