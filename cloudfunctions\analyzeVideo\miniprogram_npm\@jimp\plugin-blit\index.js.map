{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _typeof2 = _interopRequireDefault(require(\"@babel/runtime/helpers/typeof\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nvar _default = function _default() {\n  return {\n    /**\n     * Blits a source image on to this image\n     * @param {Jimp} src the source Jimp instance\n     * @param {number} x the x position to blit the image\n     * @param {number} y the y position to blit the image\n     * @param {number} srcx (optional) the x position from which to crop the source image\n     * @param {number} srcy (optional) the y position from which to crop the source image\n     * @param {number} srcw (optional) the width to which to crop the source image\n     * @param {number} srch (optional) the height to which to crop the source image\n     * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    blit: function blit(src, x, y, srcx, srcy, srcw, srch, cb) {\n      if (!(src instanceof this.constructor)) {\n        return _utils.throwError.call(this, 'The source must be a Jimp image', cb);\n      }\n\n      if (typeof x !== 'number' || typeof y !== 'number') {\n        return _utils.throwError.call(this, 'x and y must be numbers', cb);\n      }\n\n      if (typeof srcx === 'function') {\n        cb = srcx;\n        srcx = 0;\n        srcy = 0;\n        srcw = src.bitmap.width;\n        srch = src.bitmap.height;\n      } else if ((0, _typeof2[\"default\"])(srcx) === (0, _typeof2[\"default\"])(srcy) && (0, _typeof2[\"default\"])(srcy) === (0, _typeof2[\"default\"])(srcw) && (0, _typeof2[\"default\"])(srcw) === (0, _typeof2[\"default\"])(srch)) {\n        srcx = srcx || 0;\n        srcy = srcy || 0;\n        srcw = srcw || src.bitmap.width;\n        srch = srch || src.bitmap.height;\n      } else {\n        return _utils.throwError.call(this, 'srcx, srcy, srcw, srch must be numbers', cb);\n      } // round input\n\n\n      x = Math.round(x);\n      y = Math.round(y); // round input\n\n      srcx = Math.round(srcx);\n      srcy = Math.round(srcy);\n      srcw = Math.round(srcw);\n      srch = Math.round(srch);\n      var maxWidth = this.bitmap.width;\n      var maxHeight = this.bitmap.height;\n      var baseImage = this;\n      src.scanQuiet(srcx, srcy, srcw, srch, function (sx, sy, idx) {\n        var xOffset = x + sx - srcx;\n        var yOffset = y + sy - srcy;\n\n        if (xOffset >= 0 && yOffset >= 0 && maxWidth - xOffset > 0 && maxHeight - yOffset > 0) {\n          var dstIdx = baseImage.getPixelIndex(xOffset, yOffset);\n          var _src = {\n            r: this.bitmap.data[idx],\n            g: this.bitmap.data[idx + 1],\n            b: this.bitmap.data[idx + 2],\n            a: this.bitmap.data[idx + 3]\n          };\n          var dst = {\n            r: baseImage.bitmap.data[dstIdx],\n            g: baseImage.bitmap.data[dstIdx + 1],\n            b: baseImage.bitmap.data[dstIdx + 2],\n            a: baseImage.bitmap.data[dstIdx + 3]\n          };\n          baseImage.bitmap.data[dstIdx] = (_src.a * (_src.r - dst.r) - dst.r + 255 >> 8) + dst.r;\n          baseImage.bitmap.data[dstIdx + 1] = (_src.a * (_src.g - dst.g) - dst.g + 255 >> 8) + dst.g;\n          baseImage.bitmap.data[dstIdx + 2] = (_src.a * (_src.b - dst.b) - dst.b + 255 >> 8) + dst.b;\n          baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(dst.a + _src.a);\n        }\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}