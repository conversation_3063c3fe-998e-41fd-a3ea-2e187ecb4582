{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _utils = require(\"@jimp/utils\");\n\n/**\n * Scale the image to the given width and height keeping the aspect ratio. Some parts of the image may be letter boxed.\n * @param {number} w the width to resize the image to\n * @param {number} h the height to resize the image to\n * @param {number} alignBits (optional) A bitmask for horizontal and vertical alignment\n * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nvar _default = function _default() {\n  return {\n    contain: function contain(w, h, alignBits, mode, cb) {\n      if (typeof w !== 'number' || typeof h !== 'number') {\n        return _utils.throwError.call(this, 'w and h must be numbers', cb);\n      } // permit any sort of optional parameters combination\n\n\n      if (typeof alignBits === 'string') {\n        if (typeof mode === 'function' && typeof cb === 'undefined') cb = mode;\n        mode = alignBits;\n        alignBits = null;\n      }\n\n      if (typeof alignBits === 'function') {\n        if (typeof cb === 'undefined') cb = alignBits;\n        mode = null;\n        alignBits = null;\n      }\n\n      if (typeof mode === 'function' && typeof cb === 'undefined') {\n        cb = mode;\n        mode = null;\n      }\n\n      alignBits = alignBits || this.constructor.HORIZONTAL_ALIGN_CENTER | this.constructor.VERTICAL_ALIGN_MIDDLE;\n      var hbits = alignBits & (1 << 3) - 1;\n      var vbits = alignBits >> 3; // check if more flags than one is in the bit sets\n\n      if (!(hbits !== 0 && !(hbits & hbits - 1) || vbits !== 0 && !(vbits & vbits - 1))) {\n        return _utils.throwError.call(this, 'only use one flag per alignment direction', cb);\n      }\n\n      var alignH = hbits >> 1; // 0, 1, 2\n\n      var alignV = vbits >> 1; // 0, 1, 2\n\n      var f = w / h > this.bitmap.width / this.bitmap.height ? h / this.bitmap.height : w / this.bitmap.width;\n      var c = this.cloneQuiet().scale(f, mode);\n      this.resize(w, h, mode);\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        this.bitmap.data.writeUInt32BE(this._background, idx);\n      });\n      this.blit(c, (this.bitmap.width - c.bitmap.width) / 2 * alignH, (this.bitmap.height - c.bitmap.height) / 2 * alignV);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}