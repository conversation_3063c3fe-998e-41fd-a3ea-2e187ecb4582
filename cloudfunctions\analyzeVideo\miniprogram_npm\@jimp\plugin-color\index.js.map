{"version": 3, "sources": ["index.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _toConsumableArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/toConsumableArray\"));\n\nvar _tinycolor = _interopRequireDefault(require(\"tinycolor2\"));\n\nvar _utils = require(\"@jimp/utils\");\n\nfunction applyKernel(im, kernel, x, y) {\n  var value = [0, 0, 0];\n  var size = (kernel.length - 1) / 2;\n\n  for (var kx = 0; kx < kernel.length; kx += 1) {\n    for (var ky = 0; ky < kernel[kx].length; ky += 1) {\n      var idx = im.getPixelIndex(x + kx - size, y + ky - size);\n      value[0] += im.bitmap.data[idx] * kernel[kx][ky];\n      value[1] += im.bitmap.data[idx + 1] * kernel[kx][ky];\n      value[2] += im.bitmap.data[idx + 2] * kernel[kx][ky];\n    }\n  }\n\n  return value;\n}\n\nvar isDef = function isDef(v) {\n  return typeof v !== 'undefined' && v !== null;\n};\n\nfunction greyscale(cb) {\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n    var grey = parseInt(0.2126 * this.bitmap.data[idx] + 0.7152 * this.bitmap.data[idx + 1] + 0.0722 * this.bitmap.data[idx + 2], 10);\n    this.bitmap.data[idx] = grey;\n    this.bitmap.data[idx + 1] = grey;\n    this.bitmap.data[idx + 2] = grey;\n  });\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nfunction mix(clr, clr2) {\n  var p = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 50;\n  return {\n    r: (clr2.r - clr.r) * (p / 100) + clr.r,\n    g: (clr2.g - clr.g) * (p / 100) + clr.g,\n    b: (clr2.b - clr.b) * (p / 100) + clr.b\n  };\n}\n\nfunction colorFn(actions, cb) {\n  var _this = this;\n\n  if (!actions || !Array.isArray(actions)) {\n    return _utils.throwError.call(this, 'actions must be an array', cb);\n  }\n\n  actions = actions.map(function (action) {\n    if (action.apply === 'xor' || action.apply === 'mix') {\n      action.params[0] = (0, _tinycolor[\"default\"])(action.params[0]).toRgb();\n    }\n\n    return action;\n  });\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n    var clr = {\n      r: _this.bitmap.data[idx],\n      g: _this.bitmap.data[idx + 1],\n      b: _this.bitmap.data[idx + 2]\n    };\n\n    var colorModifier = function colorModifier(i, amount) {\n      return _this.constructor.limit255(clr[i] + amount);\n    };\n\n    actions.forEach(function (action) {\n      if (action.apply === 'mix') {\n        clr = mix(clr, action.params[0], action.params[1]);\n      } else if (action.apply === 'tint') {\n        clr = mix(clr, {\n          r: 255,\n          g: 255,\n          b: 255\n        }, action.params[0]);\n      } else if (action.apply === 'shade') {\n        clr = mix(clr, {\n          r: 0,\n          g: 0,\n          b: 0\n        }, action.params[0]);\n      } else if (action.apply === 'xor') {\n        clr = {\n          r: clr.r ^ action.params[0].r,\n          g: clr.g ^ action.params[0].g,\n          b: clr.b ^ action.params[0].b\n        };\n      } else if (action.apply === 'red') {\n        clr.r = colorModifier('r', action.params[0]);\n      } else if (action.apply === 'green') {\n        clr.g = colorModifier('g', action.params[0]);\n      } else if (action.apply === 'blue') {\n        clr.b = colorModifier('b', action.params[0]);\n      } else {\n        var _clr;\n\n        if (action.apply === 'hue') {\n          action.apply = 'spin';\n        }\n\n        clr = (0, _tinycolor[\"default\"])(clr);\n\n        if (!clr[action.apply]) {\n          return _utils.throwError.call(_this, 'action ' + action.apply + ' not supported', cb);\n        }\n\n        clr = (_clr = clr)[action.apply].apply(_clr, (0, _toConsumableArray2[\"default\"])(action.params)).toRgb();\n      }\n    });\n    _this.bitmap.data[idx] = clr.r;\n    _this.bitmap.data[idx + 1] = clr.g;\n    _this.bitmap.data[idx + 2] = clr.b;\n  });\n\n  if ((0, _utils.isNodePattern)(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nvar _default = function _default() {\n  return {\n    /**\n     * Adjusts the brightness of the image\n     * @param {number} val the amount to adjust the brightness, a number between -1 and +1\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    brightness: function brightness(val, cb) {\n      if (typeof val !== 'number') {\n        return _utils.throwError.call(this, 'val must be numbers', cb);\n      }\n\n      if (val < -1 || val > +1) {\n        return _utils.throwError.call(this, 'val must be a number between -1 and +1', cb);\n      }\n\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        if (val < 0.0) {\n          this.bitmap.data[idx] = this.bitmap.data[idx] * (1 + val);\n          this.bitmap.data[idx + 1] = this.bitmap.data[idx + 1] * (1 + val);\n          this.bitmap.data[idx + 2] = this.bitmap.data[idx + 2] * (1 + val);\n        } else {\n          this.bitmap.data[idx] = this.bitmap.data[idx] + (255 - this.bitmap.data[idx]) * val;\n          this.bitmap.data[idx + 1] = this.bitmap.data[idx + 1] + (255 - this.bitmap.data[idx + 1]) * val;\n          this.bitmap.data[idx + 2] = this.bitmap.data[idx + 2] + (255 - this.bitmap.data[idx + 2]) * val;\n        }\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Adjusts the contrast of the image\n     * @param {number} val the amount to adjust the contrast, a number between -1 and +1\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    contrast: function contrast(val, cb) {\n      if (typeof val !== 'number') {\n        return _utils.throwError.call(this, 'val must be numbers', cb);\n      }\n\n      if (val < -1 || val > +1) {\n        return _utils.throwError.call(this, 'val must be a number between -1 and +1', cb);\n      }\n\n      var factor = (val + 1) / (1 - val);\n\n      function adjust(value) {\n        value = Math.floor(factor * (value - 127) + 127);\n        return value < 0 ? 0 : value > 255 ? 255 : value;\n      }\n\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        this.bitmap.data[idx] = adjust(this.bitmap.data[idx]);\n        this.bitmap.data[idx + 1] = adjust(this.bitmap.data[idx + 1]);\n        this.bitmap.data[idx + 2] = adjust(this.bitmap.data[idx + 2]);\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Apply a posterize effect\n     * @param {number} n the amount to adjust the contrast, minimum threshold is two\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    posterize: function posterize(n, cb) {\n      if (typeof n !== 'number') {\n        return _utils.throwError.call(this, 'n must be numbers', cb);\n      }\n\n      if (n < 2) {\n        n = 2;\n      } // minimum of 2 levels\n\n\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        this.bitmap.data[idx] = Math.floor(this.bitmap.data[idx] / 255 * (n - 1)) / (n - 1) * 255;\n        this.bitmap.data[idx + 1] = Math.floor(this.bitmap.data[idx + 1] / 255 * (n - 1)) / (n - 1) * 255;\n        this.bitmap.data[idx + 2] = Math.floor(this.bitmap.data[idx + 2] / 255 * (n - 1)) / (n - 1) * 255;\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Removes colour from the image using ITU Rec 709 luminance values\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    greyscale: greyscale,\n    // Alias of greyscale for our American friends\n    grayscale: greyscale,\n\n    /**\n     * Multiplies the opacity of each pixel by a factor between 0 and 1\n     * @param {number} f A number, the factor by which to multiply the opacity of each pixel\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    opacity: function opacity(f, cb) {\n      if (typeof f !== 'number') return _utils.throwError.call(this, 'f must be a number', cb);\n      if (f < 0 || f > 1) return _utils.throwError.call(this, 'f must be a number from 0 to 1', cb);\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var v = this.bitmap.data[idx + 3] * f;\n        this.bitmap.data[idx + 3] = v;\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Applies a sepia tone to the image\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    sepia: function sepia(cb) {\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        var red = this.bitmap.data[idx];\n        var green = this.bitmap.data[idx + 1];\n        var blue = this.bitmap.data[idx + 2];\n        red = red * 0.393 + green * 0.769 + blue * 0.189;\n        green = red * 0.349 + green * 0.686 + blue * 0.168;\n        blue = red * 0.272 + green * 0.534 + blue * 0.131;\n        this.bitmap.data[idx] = red < 255 ? red : 255;\n        this.bitmap.data[idx + 1] = green < 255 ? green : 255;\n        this.bitmap.data[idx + 2] = blue < 255 ? blue : 255;\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Fades each pixel by a factor between 0 and 1\n     * @param {number} f A number from 0 to 1. 0 will haven no effect. 1 will turn the image completely transparent.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    fade: function fade(f, cb) {\n      if (typeof f !== 'number') {\n        return _utils.throwError.call(this, 'f must be a number', cb);\n      }\n\n      if (f < 0 || f > 1) {\n        return _utils.throwError.call(this, 'f must be a number from 0 to 1', cb);\n      } // this method is an alternative to opacity (which may be deprecated)\n\n\n      this.opacity(1 - f);\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Adds each element of the image to its local neighbors, weighted by the kernel\n     * @param {array} kernel a matrix to weight the neighbors sum\n     * @param {string} edgeHandling (optional) define how to sum pixels from outside the border\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    convolution: function convolution(kernel, edgeHandling, cb) {\n      if (typeof edgeHandling === 'function' && typeof cb === 'undefined') {\n        cb = edgeHandling;\n        edgeHandling = null;\n      }\n\n      if (!edgeHandling) {\n        edgeHandling = this.constructor.EDGE_EXTEND;\n      }\n\n      var newData = Buffer.from(this.bitmap.data);\n      var kRows = kernel.length;\n      var kCols = kernel[0].length;\n      var rowEnd = Math.floor(kRows / 2);\n      var colEnd = Math.floor(kCols / 2);\n      var rowIni = -rowEnd;\n      var colIni = -colEnd;\n      var weight;\n      var rSum;\n      var gSum;\n      var bSum;\n      var ri;\n      var gi;\n      var bi;\n      var xi;\n      var yi;\n      var idxi;\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        bSum = 0;\n        gSum = 0;\n        rSum = 0;\n\n        for (var row = rowIni; row <= rowEnd; row++) {\n          for (var col = colIni; col <= colEnd; col++) {\n            xi = x + col;\n            yi = y + row;\n            weight = kernel[row + rowEnd][col + colEnd];\n            idxi = this.getPixelIndex(xi, yi, edgeHandling);\n\n            if (idxi === -1) {\n              bi = 0;\n              gi = 0;\n              ri = 0;\n            } else {\n              ri = this.bitmap.data[idxi + 0];\n              gi = this.bitmap.data[idxi + 1];\n              bi = this.bitmap.data[idxi + 2];\n            }\n\n            rSum += weight * ri;\n            gSum += weight * gi;\n            bSum += weight * bi;\n          }\n        }\n\n        if (rSum < 0) {\n          rSum = 0;\n        }\n\n        if (gSum < 0) {\n          gSum = 0;\n        }\n\n        if (bSum < 0) {\n          bSum = 0;\n        }\n\n        if (rSum > 255) {\n          rSum = 255;\n        }\n\n        if (gSum > 255) {\n          gSum = 255;\n        }\n\n        if (bSum > 255) {\n          bSum = 255;\n        }\n\n        newData[idx + 0] = rSum;\n        newData[idx + 1] = gSum;\n        newData[idx + 2] = bSum;\n      });\n      this.bitmap.data = newData;\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Set the alpha channel on every pixel to fully opaque\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    opaque: function opaque(cb) {\n      this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function (x, y, idx) {\n        this.bitmap.data[idx + 3] = 255;\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Pixelates the image or a region\n     * @param {number} size the size of the pixels\n     * @param {number} x (optional) the x position of the region to pixelate\n     * @param {number} y (optional) the y position of the region to pixelate\n     * @param {number} w (optional) the width of the region to pixelate\n     * @param {number} h (optional) the height of the region to pixelate\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    pixelate: function pixelate(size, x, y, w, h, cb) {\n      if (typeof x === 'function') {\n        cb = x;\n        h = null;\n        w = null;\n        y = null;\n        x = null;\n      } else {\n        if (typeof size !== 'number') {\n          return _utils.throwError.call(this, 'size must be a number', cb);\n        }\n\n        if (isDef(x) && typeof x !== 'number') {\n          return _utils.throwError.call(this, 'x must be a number', cb);\n        }\n\n        if (isDef(y) && typeof y !== 'number') {\n          return _utils.throwError.call(this, 'y must be a number', cb);\n        }\n\n        if (isDef(w) && typeof w !== 'number') {\n          return _utils.throwError.call(this, 'w must be a number', cb);\n        }\n\n        if (isDef(h) && typeof h !== 'number') {\n          return _utils.throwError.call(this, 'h must be a number', cb);\n        }\n      }\n\n      var kernel = [[1 / 16, 2 / 16, 1 / 16], [2 / 16, 4 / 16, 2 / 16], [1 / 16, 2 / 16, 1 / 16]];\n      x = x || 0;\n      y = y || 0;\n      w = isDef(w) ? w : this.bitmap.width - x;\n      h = isDef(h) ? h : this.bitmap.height - y;\n      var source = this.cloneQuiet();\n      this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n        xx = size * Math.floor(xx / size);\n        yx = size * Math.floor(yx / size);\n        var value = applyKernel(source, kernel, xx, yx);\n        this.bitmap.data[idx] = value[0];\n        this.bitmap.data[idx + 1] = value[1];\n        this.bitmap.data[idx + 2] = value[2];\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Applies a convolution kernel to the image or a region\n     * @param {array} kernel the convolution kernel\n     * @param {number} x (optional) the x position of the region to apply convolution to\n     * @param {number} y (optional) the y position of the region to apply convolution to\n     * @param {number} w (optional) the width of the region to apply convolution to\n     * @param {number} h (optional) the height of the region to apply convolution to\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    convolute: function convolute(kernel, x, y, w, h, cb) {\n      if (!Array.isArray(kernel)) return _utils.throwError.call(this, 'the kernel must be an array', cb);\n\n      if (typeof x === 'function') {\n        cb = x;\n        x = null;\n        y = null;\n        w = null;\n        h = null;\n      } else {\n        if (isDef(x) && typeof x !== 'number') {\n          return _utils.throwError.call(this, 'x must be a number', cb);\n        }\n\n        if (isDef(y) && typeof y !== 'number') {\n          return _utils.throwError.call(this, 'y must be a number', cb);\n        }\n\n        if (isDef(w) && typeof w !== 'number') {\n          return _utils.throwError.call(this, 'w must be a number', cb);\n        }\n\n        if (isDef(h) && typeof h !== 'number') {\n          return _utils.throwError.call(this, 'h must be a number', cb);\n        }\n      }\n\n      var ksize = (kernel.length - 1) / 2;\n      x = isDef(x) ? x : ksize;\n      y = isDef(y) ? y : ksize;\n      w = isDef(w) ? w : this.bitmap.width - x;\n      h = isDef(h) ? h : this.bitmap.height - y;\n      var source = this.cloneQuiet();\n      this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n        var value = applyKernel(source, kernel, xx, yx);\n        this.bitmap.data[idx] = this.constructor.limit255(value[0]);\n        this.bitmap.data[idx + 1] = this.constructor.limit255(value[1]);\n        this.bitmap.data[idx + 2] = this.constructor.limit255(value[2]);\n      });\n\n      if ((0, _utils.isNodePattern)(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Apply multiple color modification rules\n     * @param {array} actions list of color modification rules, in following format: { apply: '<rule-name>', params: [ <rule-parameters> ]  }\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp }this for chaining of methods\n     */\n    color: colorFn,\n    colour: colorFn\n  };\n};\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}